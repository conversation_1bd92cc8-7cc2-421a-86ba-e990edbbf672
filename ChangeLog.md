# ChangeLog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.1.0/)

## Unreleased
### Added
* **Issue [#143](https://github.com/CQ-Dev-Team/Admin_Quiz_Frontend/issues/143)**: When marking a quiz as checked, also submit all users’ tests.
* **Issue [#119](https://github.com/CQ-Dev-Team/Admin_Quiz_Frontend/issues/119)**: Add Search for roll no in Invited.
* **issue [#118](https://github.com/cq-dev-team/admin_quiz_frontend/issues/118)**: add sorting for attempt status.
* **issue [#100](https://github.com/cq-dev-team/admin_quiz_frontend/issues/100)**: "Detect VM" Appears Off When Previewing Shared Test
* Add cloud icon to question list and editor when `show_cloud_icon` query param is set (per <PERSON><PERSON><PERSON>).

### Updated
* **Issue [#143](https://github.com/CQ-Dev-Team/Admin_Quiz_Frontend/issues/143)**: Refactor user report processing and update data structures for improved time tracking.