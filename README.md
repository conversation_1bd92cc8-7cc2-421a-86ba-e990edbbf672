# React TypeScript Zustand Module Federation Boilerplate

A modern React application boilerplate built with TypeScript, <PERSON>ustand for state management, and Module Federation for micro-frontend architecture.

## 🚀 Features

- **React 18** with TypeScript
- **Zustand** for lightweight state management
- **Module Federation** for micro-frontend architecture
- **Vite** for fast development and building
- **Ant Design** + **Shadcn UI** for comprehensive UI components
- **TailwindCSS** for utility-first styling
- **ESLint** + **Prettier** for code quality and formatting
- **React Router** for client-side routing
- **Axios** for API communication
- **Socket.io** for real-time features

## 📦 Tech Stack

### Core
- React 18
- TypeScript
- Vite
- Module Federation

### State Management
- Zustand
- Immer

### UI & Styling
- Ant Design
- Shadcn UI
- TailwindCSS
- Lucide React Icons

### Development Tools
- ESLint
- Prettier
- React Hot Reload

## 🛠️ Getting Started

### Prerequisites
- Node.js (v18 or higher)
- Yarn or npm

### Installation

1. Clone the repository
```bash
git clone <repository-url>
cd <project-name>
```

2. Install dependencies
```bash
yarn install
# or
npm install
```

3. Set up environment variables
```bash
cp .env.example .env
```

4. Start the development server
```bash
yarn dev
# or
npm run dev
```

The application will be available at `http://localhost:2001`

## 📝 Available Scripts

- `yarn dev` - Start development server on port 2001
- `yarn build` - Build for production
- `yarn preview` - Preview production build
- `yarn lint` - Run ESLint
- `yarn format` - Format code with Prettier
- `yarn format:check` - Check code formatting

## 🏗️ Project Structure

```
src/
├── @types/              # TypeScript type definitions
├── assets/              # Static assets
├── client/              # API client implementations
├── components/          # Reusable UI components
│   └── ui/              # Basic UI components
├── config/              # Application configuration
├── constants/           # Application constants
├── hooks/               # Custom React hooks
├── lib/                 # Utility libraries
├── store/               # Zustand state management
├── utils/               # Utility functions
├── App.tsx              # Main application component
├── Router.tsx           # Application routing
└── main.tsx             # Application entry point
```

## 🔧 Configuration

### Module Federation
The project is configured to expose components via Module Federation:
- Exposes: `./Button` from `./src/components/button/button.tsx`
- Shared dependencies: React, React DOM, Zustand, Immer, React Router, Axios

### Code Formatting
- **Prettier** is configured with tabs, 4-space width, and semicolons
- **ESLint** enforces code quality and integrates with Prettier
- Auto-formatting on save is enabled in VS Code

### Path Aliases
- `@/*` maps to `./src/*`
- `@store/*` maps to `src/store/*`

## 🌐 Environment Variables

Create a `.env` file based on `.env.example`:

```env
VITE_TEST_ATTEMPT_URL_BASE=https://test.example.com/
VITE_MAIN_SERVER_URL=https://api.example.com
VITE_MAIN_SOCKET_SERVER_URL=https://socket.example.com
VITE_COURSE_SERVER_URL=https://course.example.com
VITE_LOGIN_SERVER_URL=https://login.example.com
VITE_MASTER_VIDEO_URL=https://video.example.com/
VITE_PLATFORM_ORGANISATION=your-org
```

## 🚀 Deployment

Build the application for production:

```bash
yarn build
```

The built files will be in the `dist` directory, ready for deployment.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run `yarn format` and `yarn lint`
5. Commit your changes
6. Push to your branch
7. Create a Pull Request
