import React from "react";
import { useEffect, useState } from "react";
import { Button, Image, Space, Tooltip } from "antd";
import { downloadFile, createToolbarRender } from "../utils/ImageToolBar";
import { LoadingOutlined } from "@ant-design/icons";

export function PreviewImage(props: {
	sources: string[];
	onClose?: () => void;
	onDownload?: (_url: string) => string | undefined;
}) {
	const { sources } = props;
	const [visible, setVisible] = useState(false);
	const [currentIndex, setCurrentIndex] = useState(0);

	useEffect(() => {
		setVisible(sources.length > 0);
	}, [sources]);

	return (
		<Image.PreviewGroup
			preview={{
				visible,
				styles: { mask: { backdropFilter: "blur(5px)" } },
				toolbarRender: createToolbarRender({
					onDownload: () =>
						downloadFile(
							sources[currentIndex],
							`${props.onDownload?.(sources[currentIndex]) ?? "image"}`
						),
				}),
				onChange: (index: number) => setCurrentIndex(index),
				onVisibleChange: isVisible => {
					setVisible(isVisible);
					if (!isVisible) {
						props.onClose?.();
					}
				},
				countRender(current, total) {
					if (total < 2) return null;
					return (
						<div
							style={{
								padding: "8px 16px",
								backgroundColor: "rgba(0, 0, 0, 0.4)",
								borderRadius: "100px",
							}}
						>
							{current} / {total}
						</div>
					);
				},
				imageRender: originalNode =>
					React.cloneElement(originalNode, {
						style: {
							...(originalNode.props.style || {}),
							border: "8px solid #fff",
							borderRadius: "8px",
							backgroundColor: "#fff",
						},
					}),
			}}
		>
			{sources.map(src => (
				<Image src={src} key={src} style={{ display: "none" }} />
			))}
		</Image.PreviewGroup>
	);
}

const PreviewImageButton = (props: {
	src: string;
	onReady: (_src: string) => void;
	onError?: (_reason: string) => void;
}) => {
	const [isLoading, setLoading] = useState(false);
	const fallbackImage = "/images/unavailable-image.png";

	async function loadImage() {
		setLoading(true);
		try {
			const imageSrc = await fetch(props.src)
				.then(response => response.text())
				.catch(() => fallbackImage);
			props.onReady(imageSrc);
		} catch (error) {
			console.error(error);
			props.onError?.("Failed to load image");
		}
		setLoading(false);
	}

	return (
		<Tooltip title="Click to view image">
			<Button type="default" disabled={isLoading} onClick={loadImage}>
				{isLoading ? (
					<Space>
						<LoadingOutlined />
						loading
					</Space>
				) : (
					"Show image"
				)}
			</Button>
		</Tooltip>
	);
};

export default PreviewImageButton;
