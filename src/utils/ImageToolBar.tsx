import React from "react";
import { DownloadOutlined, UndoOutlined } from "@ant-design/icons";
import timeDate from "./timeDate";

export async function downloadFile(url: string, filename: string) {
	try {
		const date = new Date().toISOString();
		const donwloadedFileName = `${filename}-${timeDate(date)}.jpg`;
		const anchor = document.createElement("a");
		anchor.href = url;
		anchor.download = `${donwloadedFileName.replace(/\s/g, "_")}`;
		anchor.style.display = "none";
		document.body.appendChild(anchor);
		anchor.click();
		document.body.removeChild(anchor);
	} catch (error) {
		console.error("Download failed:", error);
	}
}

export const createToolbarRender = (props: { onDownload?: () => void }) => {
	return (
		originalToolbar: React.ReactElement,
		{ actions: { onReset } }: { actions: { onReset: () => void } }
	) => {
		const toolbarItems = React.Children.toArray(
			originalToolbar.props.children
		);
		const [baseControl] = toolbarItems;

		const zoomControls = toolbarItems.filter(
			item =>
				React.isValidElement(item) &&
				item.key?.toString().toLowerCase().includes("zoom")
		);

		return React.cloneElement(originalToolbar, {
			...originalToolbar.props,
			style: {
				backgroundColor: "rgba(0, 0, 0, 0.4)",
			},
			children: (
				<>
					{props.onDownload === undefined ? (
						<></>
					) : (
						React.cloneElement(
							baseControl as React.ReactElement<{
								children: React.ReactNode;
								onClick: () => void;
							}>,
							{
								key: "download",
								onClick: props.onDownload,
								children: <DownloadOutlined />,
							}
						)
					)}
					{zoomControls}
					{React.cloneElement(
						baseControl as React.ReactElement<{
							children: React.ReactNode;
							onClick: () => void;
						}>,
						{
							key: "undo",
							onClick: onReset,
							children: <UndoOutlined />,
						}
					)}
				</>
			),
		});
	};
};
