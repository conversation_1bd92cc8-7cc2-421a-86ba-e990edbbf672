import { email } from "@/constants/email";
import { RoleAction, RoleResource, UserRole } from "@/constants/roles";

export const hasEditPermission = (
	record: {
		createdById: string | undefined;
		orgId: string | undefined;
	},
	session: Session | null,
	userRoleId: UserRole,
	hasResourcePermission: (
		_resource: RoleResource,
		_action?: RoleAction
	) => boolean
) => {
	if (!(record && session)) {
		return false;
	}

	if (session.email === email) {
		return true;
	}

	if (userRoleId === UserRole.ADMIN) {
		return true;
	}

	let isAllowed = false;
	if (!hasResourcePermission(RoleResource.QUIZ, RoleAction.EDIT)) {
		return false;
	}

	if (
		userRoleId === UserRole.USER ||
		userRoleId === UserRole.FACULTY ||
		userRoleId === UserRole.CUSTOM
	) {
		isAllowed = record.createdById === session?.userId;
	}

	if (userRoleId === UserRole.CUSTOM && session?.roleObj.containsOrg) {
		return true;
	}

	if (userRoleId === UserRole.SUB_ADMIN && record.orgId === session.orgId) {
		return true;
	}

	if (userRoleId === UserRole.SUPER_ORG && record.orgId === session.orgId) {
		return true;
	}

	return isAllowed;
};
