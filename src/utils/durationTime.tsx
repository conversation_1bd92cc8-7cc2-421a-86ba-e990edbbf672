import dayjs from "dayjs";
import duration from "dayjs/plugin/duration";

dayjs.extend(duration);

const formatDuration = (seconds: number) => {
	const dur = dayjs.duration(seconds, "seconds");

	const days = dur.days();
	const hours = dur.hours();
	const minutes = dur.minutes();
	const secs = dur.seconds();

	const parts: string[] = [];

	// Only days - hours - minutes (skip seconds for day level)
	if (days > 0) {
		parts.push(`${days} day${days > 1 ? "s" : ""}`);
		if (hours > 0) parts.push(`${hours} hr`);
		if (minutes > 0) parts.push(`${minutes} min`);
		return parts.join(" ");
	}

	// Hours - minutes - seconds
	if (hours > 0) {
		parts.push(`${hours} hr`);
		if (minutes > 0) parts.push(`${minutes} min`);
		if (secs > 0) parts.push(`${secs} sec`);
		return parts.join(" ");
	}

	// Minutes - seconds
	if (minutes > 0) {
		parts.push(`${minutes} min`);
		if (secs > 0) parts.push(`${secs} sec`);
		return parts.join(" ");
	}

	// Just seconds
	if (secs > 0) {
		return `${secs} sec`;
	}

	// Handle 0 seconds case
	return "0 sec";
};

export default formatDuration;
