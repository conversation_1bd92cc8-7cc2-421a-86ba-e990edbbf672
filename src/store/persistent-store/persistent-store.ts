import { ReportFilterState, TestFilterState } from "@/@types/store";
import { create, StoreApi, UseBoundStore } from "zustand";
import { immer } from "zustand/middleware/immer";
import { persist, createJSONStorage } from "zustand/middleware";

import { AppStoreState } from "@store/app-store";

interface UserSpecificStoreDetails {
	testListState: Record<string, TestFilterState>;
	reportSearchState: Record<string, ReportFilterState>;
}

interface PersistentStore {
	data: Record<string, UserSpecificStoreDetails>;
	setTestListState: (_id: string, _value: TestFilterState) => void;
	setReportSearchState: (
		_id: string,
		_value: Partial<ReportFilterState>
	) => void;
}

export const createPersistentStore = (
	appStore: UseBoundStore<StoreApi<AppStoreState>>
) => {
	const initalValue: PersistentStore = {
		data: {},
		setTestListState: () => {},
		setReportSearchState: () => {},
	};
	return create<PersistentStore>()(
		persist(
			immer(set => ({
				...initalValue,
				setTestListState: (id, value) => {
					const session = appStore.getState().session;
					if (session === null) {
						return;
					}
					set(state => {
						if (state.data[session.userId] === undefined) {
							state.data[session.userId] = {
								testListState: {},
								reportSearchState: {},
							};
						}
						state.data[session.userId].testListState[id] = value;
						return state;
					});
				},
				setReportSearchState: (id, value) => {
					const session = appStore.getState().session;
					if (session === null) {
						return;
					}
					const userId = session.userId;
					set(state => {
						if (state.data[userId] === undefined) {
							state.data[userId] = {
								testListState: {},
								reportSearchState: {},
							};
						}
						const finalValue = {
							...state.data[userId].reportSearchState[id],
							...value,
						};
						state.data[userId].reportSearchState[id] = finalValue;
						return state;
					});
				},
			})),
			{
				name: "persistent-store",
				version: 1,
				storage: createJSONStorage(() => localStorage),
				migrate(_, version) {
					if (version === 0) {
						return {
							testListState: {},
							reportSearchState: {},
						};
					}
				},
			}
		)
	);
};
