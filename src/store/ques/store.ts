import { create } from "zustand";
import { immer } from "zustand/middleware/immer";
import QuizAddClient from "../../client/test-add";
import { QuestionStoreState } from "@/@types/ques";
import {
	QuestionAddRoot,
	QuestionEditRoot,
} from "@/components/question-layout/data";

export const createQuestionStore = (client: QuizAddClient) => {
	const initialValues: QuestionStoreState = {
		initialQuestionData: {} as
			| QuestionAddRoot
			| QuestionEditRoot["quesDetail"],

		async getInitialQuest(id?: string) {
			const quiz = await client.getInitialQuest(id);
			return quiz;
		},

		setQuesData: () => {},
	};

	return create<QuestionStoreState>()(
		immer(set => ({
			...initialValues,

			async getInitialQuest(id?: string) {
				const data = await client.getInitialQuest(id);
				set(state => {
					state.initialQuestionData =
						"quesDetail" in data ? data.quesDetail : data;
				});

				return data;
			},

			setQuesData: (
				data: QuestionAddRoot | QuestionEditRoot["quesDetail"]
			) =>
				set(state => {
					if ("quesDetail" in data)
						state.initialQuestionData = data.quesDetail as
							| QuestionAddRoot
							| QuestionEditRoot["quesDetail"];
					else state.initialQuestionData = data;
				}),
		}))
	);
};
