import { create } from "zustand";
import { immer } from "zustand/middleware/immer";
import QuizAddClient from "../../client/test-add";
import { TestStoreState } from "@/@types/store";

export const createTestStore = (client: QuizAddClient) => {
	const initialValues: TestStoreState = {
		quizData: [],
		async getQuiz(id: string) {
			const quiz = await client.getQuiz(id);
			return quiz;
		},
		checkLinkAvailability: client.checkLinkAvailability.bind(client),
		allowCandidates: async () => {},
		allowedCandidates: {},
		getTestAllowedCandidate: () => [],
		inviteCandidates: async () => {},
		invitedCandidates: {},
		getTestInvitedCandidate: () => {},
	};

	return create<TestStoreState>()(
		immer(set => ({
			...initialValues,
			async getQuiz(id: string) {
				return await client.getQuiz(id);
			},

			async allowCandidates(testId, emails) {
				await client.addAllowedEmailsToQiz(testId, emails);
				const res = await client.getTestReport(testId);
				const setOfUsersAttemptedTest = new Set();
				for (const currentUserData of res.quizReport.userDataArray) {
					setOfUsersAttemptedTest.add(currentUserData.email);
				}
				const finalAllowedUsers = res.sampleData.emails.map(user => {
					return {
						...user,
						attemptStatus: setOfUsersAttemptedTest.has(user.email),
					};
				});
				set({ allowedCandidates: { [testId]: finalAllowedUsers } });
			},
			getTestAllowedCandidate(testId: string) {
				return this.allowedCandidates[testId];
			},
			async inviteCandidates(testId, data) {
				try {
					const invitedCandidatesData =
						await client.addQuizInviteesEmails(testId, data);
					const res = await client.getTestReport(testId);
					const setOfUsersAttemptedTest = new Set();
					for (const currentUserData of res.quizReport
						.userDataArray) {
						setOfUsersAttemptedTest.add(currentUserData.email);
					}
					const finalInvitedUsers = res.sampleData?.invitees?.map(
						user => {
							return {
								...user,
								attemptStatus: setOfUsersAttemptedTest.has(
									user.email
								),
							};
						}
					);
					set({
						invitedCandidates: {
							[testId]: finalInvitedUsers,
						},
					});
					return invitedCandidatesData;
				} catch (ex) {
					console.log(ex);
					throw ex;
				}
			},
			getTestInvitedCandidate(testId: string) {
				return this.invitedCandidates[testId];
			},
		}))
	);
};
