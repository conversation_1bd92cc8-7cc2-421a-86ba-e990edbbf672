import { create } from "zustand";
import { immer } from "zustand/middleware/immer";
import { QuizClient } from "../../client";
import { RoleAction, RoleResource, UserRole } from "@/constants/roles";
import {
	QuestionSupportedLanguage,
	QuestionSupportedValueToName,
} from "@/constants/languages";
import { MessageInstance } from "antd/es/message/interface";
import { email } from "@/constants/email";

export interface AppStoreState {
	session: Session | null;
	init: () => Promise<void>;
	isInitCompleted: boolean;
	user: {
		displayName: string;
		role: { id: UserRole; title: string };
	};
	rolesMap: Map<RoleResource, Set<RoleAction>>;
	messageInstance: MessageInstance | null;
	questionLanguagesAllowed: { code: string; name: string }[];
	setMessageInstance: (message: MessageInstance) => void;
	hasResourcePermission: (
		resource: RoleResource,
		action?: RoleAction
	) => boolean;
}

export const createAppStore = (client: QuizClient) => {
	const initialValues: AppStoreState = {
		session: null,
		user: {
			displayName: "<user-name>",
			role: { id: UserRole.CUSTOM, title: "<user-role>" },
		},
		messageInstance: null,
		isInitCompleted: false,
		init: async () => {},
		rolesMap: new Map(),
		questionLanguagesAllowed: [],
		setMessageInstance: () => {},
		hasResourcePermission: () => false,
	};

	return create<AppStoreState>()(
		immer((set, get) => ({
			...initialValues,

			async init() {
				const session = await client.getSession();
				if (!session) {
					// window.location.replace(client.origin);
					window.location.href = `${client.origin}/login?redirect=${window.location.href}`;
					localStorage.clear();
					return;
				}
				const roleId: UserRole = +session.role;
				set({
					session: session,
					user: {
						displayName: session.displayname,
						role: {
							id: roleId,
							title: session.roleObj.name,
						},
					},
					isInitCompleted: true,
					questionLanguagesAllowed: session.languagesAllowed
						.filter(code => {
							if (
								code &&
								QuestionSupportedValueToName[parseInt(code)]
							) {
								return true;
							}
							return false;
						})
						.map(code => ({
							code,
							name:
								Object.keys(QuestionSupportedLanguage).find(
									key =>
										QuestionSupportedLanguage[
											key as keyof typeof QuestionSupportedLanguage
										].toString() === code
								) || code,
						})),
					rolesMap: new Map(
						session.roleObj.roles.map(role => [
							role.resource,
							new Set(role.actions),
						])
					),
				});
			},

			setMessageInstance(mI) {
				set(state => {
					state.messageInstance = mI;
				});
			},

			hasResourcePermission(resource, action): boolean {
				const session = get().session;
				if (session && session.email === email) {
					return true;
				}
				const resourceActions = get().rolesMap.get(resource);
				if (
					resourceActions === undefined ||
					resourceActions.size === 0
				) {
					return false;
				}
				if (action === undefined) {
					return true;
				}
				return resourceActions.has(action);
			},
		}))
	);
};
