import { createTestStore } from "./test";
import { AppConfig } from "../config/index";
import QuizAddClient from "../client/test-add";
import SockClient from "../client/socket";
import { createAppStore } from "./app-store";
import { createQuestionStore } from "./ques";
import { createPersistentStore } from "./persistent-store";
import { LoginServiceClient } from "../client/login-service-client";
import { MasterServiceClient } from "@/client/master-client";

export const quizClient = new QuizAddClient(AppConfig.quizServerURL);
export const loginClient = new LoginServiceClient(AppConfig.loginServerURL);
export const masterVideoClient = new MasterServiceClient(
	AppConfig.masterVideoURL
);
export const socketClient = new SockClient(AppConfig.socketServerURL);

export const useTestStore = createTestStore(quizClient);
export const useAppStore = createAppStore(quizClient);
export const useQuestionStore = createQuestionStore(quizClient);
export const usePersistentStore = createPersistentStore(useAppStore);
