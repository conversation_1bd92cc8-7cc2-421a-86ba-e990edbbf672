import { AnyObject } from "antd/es/_util/type";
import { useCallback, useEffect, useReducer, useState } from "react";

type ObjectListItemType = AnyObject;

export type ReturnOfUseObjectList<T extends ObjectListItemType> = ReturnType<
	typeof useObjectList<T>
>;

export default function useObjectList<T extends ObjectListItemType>(
	list: T[],
	onChange?: (list: T[]) => void
) {
	const [items, setItems] = useState(list);

	const set = useCallback(
		(items: T[]) => {
			setItems(items);
			onChange?.(items);
		},
		[onChange]
	);

	useEffect(() => set(list), [list, set]);

	return { items, set, remove, add, update, move, updateProperty };

	function remove(index: number, count = 1) {
		const list = Array.from(items);
		const removedItems = list.splice(index, count);
		set(list);
		return removedItems;
	}

	function add(item: T) {
		const list = Array.from(items);
		list.push(item);
		set(list);
	}

	function move(oldIndex: number, newIndex: number) {
		const list = Array.from(items);
		const splicedItems = list.splice(oldIndex, 1);
		list.splice(newIndex, 0, ...splicedItems);
		set(list);
	}

	function update(index: number, updateHandler: (item: T) => T) {
		const list = Array.from(items);
		list[index] = updateHandler(list[index]);
		set(list);
	}

	function updateProperty<K extends keyof T>(
		index: number,
		property: K,
		value: T[K] | ((value: T[K]) => T[K])
	) {
		const list = Array.from(items);
		const item = list.at(index);
		if (item === undefined) {
			return false;
		}
		item[property] =
			value instanceof Function ? value(item[property]) : value;
		set(list);
		return true;
	}
}

// eslint-disable-next-line no-unused-vars, @typescript-eslint/no-unused-vars
type ExcludeFirst<T extends unknown[]> = T extends [infer _I, ...infer Rest]
	? Rest
	: never;

export type ReducerActionFunction<T> = (
	state: T,
	...args: never[]
) => [state: T, ...returnValues: unknown[]];

export type ReducerActionFunctionsMap<T> = {
	[key: string]: ReducerActionFunction<T>;
};

type ReducerAction<T, K, F extends ReducerActionFunction<T>> = {
	type: K;
	data: ExcludeFirst<Parameters<F>>;
};

export type ReducerActions<T, A extends ReducerActionFunctionsMap<T>> = {
	[K in keyof A]: ReducerAction<T, K, A[K]>;
}[keyof A];

export type ReducerDispatchFunction<
	T,
	A extends ReducerActionFunctionsMap<T>,
> = ReturnType<typeof useMappedReducer<T, A>>[1];

export type ReducerOnChange<T, A extends ReducerActionFunctionsMap<T>> = (
	items: T,
	action: ReducerActions<T, A>
) => void;

export function useMappedReducer<T, A extends ReducerActionFunctionsMap<T>>(
	methods: A,
	list: T,
	onChange?: ReducerOnChange<T, A>
) {
	const reduce = useCallback(
		function (state: T, action: ReducerActions<T, A>): T {
			if (action.type in methods === false) {
				return state;
			}
			const updatedState = methods[action.type](state, ...action.data)[0];
			onChange?.(updatedState, action);
			return updatedState;
		},
		[methods, onChange]
	);
	const [items, dispatch] = useReducer(reduce, list);
	const dispatchFn = useCallback(function <K extends keyof A>(
		type: K,
		...data: ExcludeFirst<Parameters<A[K]>>
	) {
		return dispatch({ type, data: data });
	}, []);
	return [items, dispatchFn] as const;
}

export function createReducerActionMap<T>() {
	return function <A extends ReducerActionFunctionsMap<T>>(methods: A) {
		return methods;
	};
}
