import { CQSocket, ListeningEvent } from "@/@types/client";
import { socketClient } from "@/store";
import { useCallback, useEffect, useRef, useState } from "react";

export const useSocket = () => {
	const [error, setError] = useState<string>("");
	const [socket, setSocket] = useState<CQSocket | null>(null);
	const [isSocketConnected, setIsSocketConnected] = useState<boolean>(false);

	const socketConnection = useCallback(async () => {
		if (isSocketConnected) {
			return;
		}
		if (!socket) {
			return;
		}
		await new Promise((resolve, reject) => {
			socket.once("auth_succeed", () => resolve(""));
			socket.once("connect_error", reject);
		});
	}, [socket, isSocketConnected]);

	useEffect(() => {
		console.log("Socket Object Changed", socket);
	}, [socket]);

	const handleAuthSucceed = useCallback(() => {
		setIsSocketConnected(prev => {
			console.log("PreviousState: ", prev, "New State: ", true);
			return true;
		});
		setError("");
	}, []);

	const handleSocketDisconnection = useCallback(() => {
		setIsSocketConnected(prev => {
			console.log("PreviousState: ", prev, "New State: ", false);
			return false;
		});
	}, []);

	const handleSocketConnectionError = useCallback((error: Error) => {
		setError(error.message);
	}, []);

	useEffect(() => {
		console.log("Why are you rerendering");
		const socket = socketClient.getSocket();
		setSocket(socket);
		socket.on("auth_succeed", handleAuthSucceed);
		socket.on("disconnect", handleSocketDisconnection);
		socket.on("connect_error", handleSocketConnectionError);
		socket.connect();
		return () => {
			console.log("Should disconnect socket");
			socket.off("auth_succeed", handleAuthSucceed);
			socket.off("disconnect", handleSocketDisconnection);
			socket.off("connect_error", handleSocketConnectionError);
			handleSocketDisconnection();
			socket.disconnect();
			setSocket(null);
		};
	}, [
		handleAuthSucceed,
		handleSocketConnectionError,
		handleSocketDisconnection,
	]);

	return {
		socketConnected: isSocketConnected,
		socket: socket,
		socketConnectionError: error,
		socketConnection,
	};
};

export const useSocketQuery = <T extends keyof ListeningEvent, TResult>(
	event: T,
	queryFunc: (...params: Parameters<ListeningEvent[T]>) => TResult
) => {
	const queryFuncRef = useRef(queryFunc);
	const [loading, setLoading] = useState<boolean>(false);
	const { socket, socketConnectionError, socketConnection } = useSocket();
	const [data, setData] = useState<TResult | null>(null);

	const getAsync = useCallback(
		async (callback?: () => void): Promise<TResult> => {
			if (!socket) {
				throw new Error("Sockt is not connected");
			}
			setLoading(true);
			await socketConnection();
			if (socketConnectionError) {
				throw new Error(socketConnectionError);
			}
			const result = await new Promise<Parameters<ListeningEvent[T]>>(
				(resolve, reject) => {
					const onDisconnect = () => {
						socket?.off(event, onEvent as never);
						reject("Socket Disconnected");
					};

					const onEvent = (
						...data: Parameters<ListeningEvent[T]>
					) => {
						socket?.off("disconnect", onDisconnect);
						resolve(data);
					};
					socket?.once("disconnect", onDisconnect);
					console.log("Socket: ", event);
					socket?.once(event, onEvent as never);
					callback?.();
				}
			);
			return queryFuncRef.current(...result);
		},
		[socketConnectionError, socket, event, socketConnection]
	);
	const get = useCallback(
		async (callback?: () => void) => {
			const data = await getAsync(callback);
			setData(data);
		},
		[getAsync]
	);
	return { loading, data, get, getAsync };
};

export const useSocketWatch = <T extends keyof ListeningEvent>(
	event: T,
	config: {
		listnerFunction: (...data: Parameters<ListeningEvent[T]>) => void;
		key: Array<unknown>;
	}
) => {
	const [loading, setLoading] = useState<boolean>(true);
	const [data, setData] = useState<Parameters<ListeningEvent[T]> | null>(
		null
	);
	const queryFuncRef = useRef(config.listnerFunction);
	const { socket, socketConnected, socketConnectionError } = useSocket();

	useEffect(() => {
		queryFuncRef.current = config.listnerFunction;
	}, [config.listnerFunction, ...config.key]);

	useEffect(() => {
		console.log("🚀 useSocketWatch re-rendered");
		console.log("🔄 socket in watch:", socket);
		console.log("🔄 is socket connected:", socketConnected);
	}, [socket, socketConnected]);

	useEffect(() => {
		if (!socket) {
			console.log("Socket Connected: ", socketConnected);
			console.log("Socket: ", socket);
			return;
		}

		const currentSocket = socket;
		setLoading(true);

		const handleEvent = (...args: Parameters<ListeningEvent[T]>) => {
			console.log("Socket Handler: ", ...args);
			setData(args);
			queryFuncRef.current(...args);
		};

		currentSocket.on(event, handleEvent as never);

		return () => {
			currentSocket.off(event, handleEvent as never);
		};
	}, [event, socket, socketConnected]);

	return { loading, data, socketConnectionError };
};

export const useSocketMutation = <TArgs extends unknown[], TResult>(
	mutationFunction: (socket: CQSocket, ...args: TArgs) => Promise<TResult>
) => {
	const [loading, setLoading] = useState<boolean>(false);
	const mutationFunctionRef = useRef(mutationFunction);
	const { socket, socketConnectionError, socketConnection } = useSocket();

	const mutateAsync = useCallback(
		async (...args: TArgs) => {
			if (socketConnectionError) {
				throw new Error(socketConnectionError);
			}
			try {
				setLoading(true);
				await socketConnection();
				const data = await mutationFunctionRef.current(
					socket!,
					...args
				);
				setLoading(false);
				return data;
			} catch (error) {
				console.error(error);
				throw error;
			} finally {
				setLoading(false);
			}
		},
		[socket, socketConnectionError, socketConnection]
	);
	return { mutateAsync, loading };
};
