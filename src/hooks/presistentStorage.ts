import { usePersistentStore, useAppStore } from "@/store";
import { useCallback, useMemo } from "react";
import { ReportFilterState } from "../@types/store";

export interface ReportSearchState {
	searchValue: string;
	currentPage: number;
	pageSize: number;
	dropDownValue: number;
	questionWiseMarks: boolean;
	visibleColumns: string[];
	marksRange: [number, number] | null;
	dateRange: [string, string];
	attemptType: number;
	multiEmailSearch: string[];
}

const defaultValues: ReportSearchState = {
	searchValue: "",
	currentPage: 1,
	pageSize: 10,
	dropDownValue: 1,
	questionWiseMarks: false,
	visibleColumns: [
		"displayname",
		"email",
		"rollNo",
		"status",
		"timeTaken",
		"score",
		"feedback",
		"startTime",
		"Actions",
	],
	marksRange: null,
	dateRange: ["", ""],
	attemptType: 0,
	multiEmailSearch: [],
};

type SetterFunction = (data: ReportSearchState) => ReportSearchState;

export const useReportPersistentStore = (quizId: string) => {
	const session = useAppStore().session;
	const pStore = usePersistentStore().data;
	const setReportData = usePersistentStore().setReportSearchState;
	const data: ReportSearchState = useMemo(() => {
		if (!session?.userId || !pStore[session.userId]) {
			return defaultValues;
		}
		if (pStore[session.userId].reportSearchState) {
			const storeValue = pStore[session.userId].reportSearchState[quizId];
			if (!storeValue) {
				return defaultValues;
			}
			const value: ReportSearchState = {
				searchValue: storeValue?.searchValue ?? "",
				currentPage: 1,
				pageSize: 10,
				dropDownValue: storeValue?.dropDownValue,
				questionWiseMarks: false,
				visibleColumns: storeValue?.reportFilters?.activeField ?? [],
				marksRange: storeValue?.reportFilters?.marksRange
					? [
							storeValue?.reportFilters?.marksRange?.min ?? 0,
							storeValue?.reportFilters?.marksRange?.max ?? 0,
						]
					: null,
				dateRange: [
					storeValue?.reportFilters?.startTimeRange?.start ?? "",
					storeValue?.reportFilters?.startTimeRange?.end ?? "",
				],
				attemptType: 0,
				multiEmailSearch: [],
			};
			if (value?.dropDownValue === 1) {
				value.questionWiseMarks =
					storeValue?.reportFilters?.questionWiseMarks ?? false;
				value.currentPage = storeValue?.reportFilters?.currentPage ?? 1;
				value.pageSize = storeValue?.reportFilters?.pageSize ?? 10;
				value.multiEmailSearch =
					storeValue?.reportFilters?.multipleEmailSearch;
			}
			if (value?.dropDownValue === 2) {
				value.currentPage = storeValue?.attemptFilter?.currentPage ?? 1;
				value.pageSize = storeValue?.attemptFilter?.pageSize ?? 10;
				value.attemptType = storeValue?.attemptFilter?.attemptType ?? 0;
				value.multiEmailSearch =
					storeValue?.attemptFilter?.multipleEmailSearch;
			}
			if (value?.dropDownValue === 3) {
				value.currentPage =
					storeValue?.allowedCandidateFilter?.currentPage ?? 1;
				value.pageSize =
					storeValue?.allowedCandidateFilter?.pageSize ?? 10;
				value.attemptType =
					storeValue?.allowedCandidateFilter?.attemptType ?? 0;
				value.multiEmailSearch =
					storeValue?.allowedCandidateFilter?.multipleEmailSearch;
			}
			return value;
		}
		return data;
	}, [pStore, session, quizId]);

	const converter = useCallback((data: ReportSearchState) => {
		const reportDataToSet: Partial<ReportFilterState> = {
			searchValue: data?.searchValue ?? "",
			dropDownValue: data.dropDownValue,
		};
		if (data.dropDownValue == 1) {
			reportDataToSet.reportFilters = {
				questionWiseMarks: data.questionWiseMarks,
				currentPage: data.currentPage,
				pageSize: data.pageSize,
				multipleEmailSearch: data.multiEmailSearch,
				activeField: data.visibleColumns,
				marksRange: data.marksRange
					? {
							min: data?.marksRange[0] ?? 0,
							max: data?.marksRange[1] ?? 0,
						}
					: null,
				startTimeRange: {
					start: data.dateRange[0] || null,
					end: data.dateRange[1] || null,
				},
			};
		}
		if (data.dropDownValue == 2) {
			reportDataToSet.attemptFilter = {
				attemptType: data.attemptType,
				currentPage: data.currentPage,
				pageSize: data.pageSize,
				multipleEmailSearch: data.multiEmailSearch,
			};
		}
		if (data.dropDownValue == 3) {
			reportDataToSet.allowedCandidateFilter = {
				attemptType: data.attemptType,
				currentPage: data.currentPage,
				pageSize: data.pageSize,
				multipleEmailSearch: data.multiEmailSearch,
			};
		}
		return reportDataToSet;
	}, []);

	const setData = useCallback(
		(
			updatedData:
				| ReportSearchState
				| SetterFunction
				| { dropDownValue: number; dropDownOnly: true }
		) => {
			if (
				typeof updatedData === "object" &&
				"dropDownValue" in updatedData &&
				"dropDownOnly" in updatedData &&
				updatedData.dropDownOnly
			) {
				setReportData(quizId, {
					dropDownValue: updatedData.dropDownValue,
				});
				return;
			}

			const finalData =
				typeof updatedData === "function"
					? updatedData(data)
					: updatedData;

			const transformedData = converter(finalData as ReportSearchState);
			setReportData(quizId, transformedData);
		},
		[converter, data, quizId, setReportData]
	);

	return [data, setData] as const;
};
