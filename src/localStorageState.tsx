import { useState, useEffect } from "react";

function useLocalStorageState<T>(
	key: string,
	defaultValue: T,
	trigger?: unknown
): [T, React.Dispatch<React.SetStateAction<T>>] {
	const [state, setState] = useState<T>(() => {
		try {
			const storedValue = localStorage.getItem(key);
			return storedValue ? JSON.parse(storedValue) : defaultValue;
		} catch (error) {
			console.error(`Error reading localStorage key "${key}":`, error);
			return defaultValue;
		}
	});

	useEffect(() => {
		try {
			localStorage.setItem(key, JSON.stringify(state));
		} catch (error) {
			console.error(`Error saving to localStorage key "${key}":`, error);
		}
	}, [key, state]);

	useEffect(() => {
		try {
			const storedValue = localStorage.getItem(key);
			if (storedValue) {
				setState(JSON.parse(storedValue));
			}
		} catch {
			console.log("error in localstorage");
		}
	}, [trigger]);

	return [state, setState];
}

export default useLocalStorageState;
