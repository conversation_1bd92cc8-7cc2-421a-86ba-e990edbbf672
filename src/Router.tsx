import {
	createB<PERSON>er<PERSON>outer,
	createRoutesFromElements,
	Navigate,
	Outlet,
	Route,
	useSearchParams,
} from "react-router";
import TestEditLayout from "./components/test-edit-layout";
import TestDetailsEditForm from "./components/test-edit-form/general";
import TestAccessEditForm from "./components/test-edit-form/access";
import TestContentEditForm from "./components/test-edit-form/content";
import TestProctoringEditForm from "./components/test-edit-form/proctoring";
import UserReport from "./components/userReports/userReport";
import TestList from "./testList/testList";
import QuestionList from "./questionList/questionList";
import TestReport from "./testReport/testReport";
import TestNotificationsEditForm from "./components/test-edit-form/notifications";
import TestLayout from "./components/test-layout/test-layout";
import AttemptPreview from "./components/attempt-preview/AttemptPreview";
import Metrics from "./components/test-metrics/metrics";
import Rooms from "./components/test-rooms/rooms";
import TestsScreen from "./screen/quiz/quiz/tests";
import ProtectedRoute from "./components/protected-route/protected-route";
import { Roles } from "./constants";
import TestContent from "./components/test-content";
import QuizDownloadPreview from "./components/downloadTestPreview";
import QuesLayout from "./components/question-layout/ques-layout";
import MainScreen from "./screen/quiz/quiz/main";
import MeetingRoom from "./components/test-rooms/meetingRoom";
import ErrorPage from "./components/error-page/ErrorPage";

import LearnerUserReport from "./components/learnerDashboard/report";
import LearnerDashBoard from "./components/learnerDashboard/LearnerDashBoard";
import { Dashboard } from "./components/dashboard";
import { useAppStore } from "./store";
import QuesPreview from "./ques-preview";
import { DownloadLogs } from "./components/download-logs/DownloadLogs";

const LearnerRoute = () => {
	const { session } = useAppStore();
	if (session?.role == Roles.UserRole.USER.toString()) {
		return <LearnerUserReport />;
	}
	return <UserReport />;
};

export const Router = createBrowserRouter(
	createRoutesFromElements(
		<Route path="/" element={<MainScreen />} errorElement={<ErrorPage />}>
			<Route index element={<Dashboard />}></Route>
			<Route
				path="/test-Attempts"
				element={
					<ProtectedRoute
						resource={Roles.RoleResource.USER_DASHBOARD}
						action={Roles.RoleAction.VIEW}
					>
						<LearnerDashBoard />
					</ProtectedRoute>
				}
			></Route>
			{/* <Route path="/permission-denied" element="Permission Denied" /> */}
			<Route
				path="tests"
				element={
					<ProtectedRoute
						resource={Roles.RoleResource.QUIZ}
						action={Roles.RoleAction.LIST}
					>
						<TestsScreen />
					</ProtectedRoute>
				}
			>
				<Route path="cloneQuizByToken/:token" element={<TestLayout />}>
					<Route path="content" element={<TestContent />} />
					<Route
						path="settings"
						element={<TestEditLayout type="cloneQuizByToken" />}
					>
						{testEditRoutes()}
					</Route>
				</Route>
				<Route
					path="cloneQuizByToken"
					element={<CloneQuizRedirectionWrapper />}
				/>
				<Route element={<TestLayout />}>
					<Route path="add" element={<TestEditLayout type="add" />}>
						{testEditRoutes()}
					</Route>
					<Route path=":id/">
						<Route
							path="update"
							element={<TestEditLayout type="update" />}
						>
							{testEditRoutes()}
						</Route>
						<Route
							path="report/:jwtToken?"
							element={<TestReport />}
						/>
						<Route path="content" element={<TestContent />} />
						<Route path="metrics" element={<Metrics />} />
						<Route path="rooms">
							<Route index element={<Rooms />} />
						</Route>
					</Route>
				</Route>
				<Route
					path=":id/user-report/:userId/:tabId?"
					element={<UserReport />}
				></Route>
				<Route index element={<TestList />} />
			</Route>

			{/* This route is needed because of the previous quiz server URL being different from the new URL used above */}
			<Route
				path="useranswer/attemptedQuestion/quiz/:userId/:id/:tabId?"
				element={<LearnerRoute />}
			></Route>

			<Route path="/rooms/:roomId" element={<MeetingRoom />} />

			<Route
				path="questions"
				element={
					<ProtectedRoute
						resource={Roles.RoleResource.QUEST}
						action={Roles.RoleAction.LIST}
					>
						<Outlet />
					</ProtectedRoute>
				}
			>
				<Route index element={<QuestionList />} />
				<Route path="add/:quesId?/:tabId?" element={<QuesLayout />} />
				<Route path="preview/:id" element={<QuesPreview />} />
			</Route>
			<Route path="report">
				<Route index element={<TestReport />}></Route>
			</Route>
			<Route path="attemptPreview">
				<Route index element={<AttemptPreview />}></Route>
			</Route>

			<Route
				path="getTestContentFile/:id/:withoutTestCases?"
				element={<QuizDownloadPreview />}
			></Route>
			<Route path="/report/:id">
				<Route index element={<LearnerUserReport />} />
			</Route>
			<Route path="/downloadLogs/:id" element={<DownloadLogs />}></Route>
		</Route>
	)
);

function testEditRoutes() {
	return (
		<>
			<Route path="general" element={<TestDetailsEditForm />} />
			<Route path="access" element={<TestAccessEditForm />} />
			<Route path="content" element={<TestContentEditForm />} />
			<Route path="proctoring" element={<TestProctoringEditForm />} />
			<Route
				path="notifications"
				element={<TestNotificationsEditForm />}
			/>
			<Route path="*" element={<Navigate to="general" replace />} />
		</>
	);
}

function CloneQuizRedirectionWrapper() {
	const [searchParams] = useSearchParams();
	if (searchParams.has("token")) {
		return (
			<Navigate
				to={`/tests/cloneQuizByToken/${searchParams.get("token")}`}
			/>
		);
	}
	return <Navigate to={"/error"} replace />;
}
