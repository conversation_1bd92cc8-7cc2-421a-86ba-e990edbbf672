import "./App.css";
import { Config<PERSON><PERSON><PERSON>, Flex, message, Spin } from "antd";
import theme from "./themes/default.json" assert { type: "json" };
import { RouterProvider } from "react-router";
import { Router } from "./Router.tsx";
import { useEffect, useLayoutEffect, useState } from "react";
import { useAppStore } from "./store";
import { AppConfig } from "./config/index.ts";
import { HelmetProvider } from "react-helmet-async";

function App() {
	const [isAppInitCompleted, setAppInitCompleted] = useState(false);
	const { init: appStoreInit, setMessageInstance } = useAppStore(); // can cause problem, destructured not recommended
	const [messageInstance, messageContext] = message.useMessage({
		maxCount: 2,
	});

	useLayoutEffect(() => {
		setMessageInstance(messageInstance);
	}, [messageInstance, setMessageInstance]);

	useEffect(
		function () {
			(async () => {
				await appStoreInit();
				setAppInitCompleted(true);
			})();
		},
		[appStoreInit]
	);

	useEffect(() => {
		window.addEventListener(
			"message",
			(event: MessageEvent<{ type: string; message: string }>) => {
				if (event.origin !== AppConfig.quizServerURL) {
					return;
				}
				try {
					if (event.data.type === "react-message") {
						messageInstance.error(event.data.message);
					}
				} catch (error) {
					console.error(error);
				}
			}
		);
	}, []);

	if (!isAppInitCompleted) {
		return (
			<ConfigProvider theme={theme}>
				<Flex
					justify="center"
					align="center"
					style={{ height: "100%" }}
				>
					<Spin size="large" />
				</Flex>
			</ConfigProvider>
		);
	}

	return (
		<>
			{messageContext}
			<HelmetProvider>
				<ConfigProvider theme={theme}>
					<RouterProvider router={Router} />
				</ConfigProvider>
			</HelmetProvider>
		</>
	);
}

export default App;
