import { TestContentResponse } from "@/client/test-add";

export interface ReportDataType extends UserDataArray {
	startTime: string;
	rollNo?: string;
	sessionTime: string;
	isAttempting: boolean;
	enrollmentId?: string;
	totalMarks: number;
	suspiciousScore: number;
	completed: boolean;
	status: string;
}
export interface QuizReport {
	userDataArray: UserDataArray[];
	quiz: Quiz;
	isQuestionWiseReportRequired: boolean;
	userIdsPresent: string[];
	questions: Question[];
}

export interface Root {
	quizReport: QuizReport;
	sampleData: Invited;
	canChangeTestState: boolean;
}

export interface Invited {
	_id: string;
	quizId: string;
	__v: number;
	createdAt: string;
	updatedAt: string;
	invitees: Invitee[];
	emails: AllowedCandidates[];
}

export interface Invitee {
	token: string;
	email: string;
	_id: string;
	isValid: boolean;
	status: string;
	quizAttemptLink: string;
	validTill?: string;
	enrollmentId?: string;
}

export interface AllowedCandidates {
	email: string;
	enrollmentId?: string;
	attemptStatus: boolean;
}

export interface UserDataArray {
	totalAttemptedQuestion: number;
	score: number;
	percentageScore: number;
	timeTaken: string;
	startTime?: string;
	endTime?: string;
	_id: string;
	email: string;
	displayname: string;
	college: string;
	batch: string;
	questionWiseScore: unknown[];
	extraTime: number;
	idleTime: number;
	codePasteCount: number;
	tabSwitchCount: number;
	tabSwitchInCount: number;
	fullScreenInCount: number;
	fullScreenOutCount: number;
	loginCount: number;
	currentIp: string;
	camBlockCount: number;
	qualified: boolean;
	quizUserDetails: unknown[];
	loginDetails: LoginDetail[];
	tabSwitchReport: unknown[];
	fromDB?: boolean;
	sessionTime?: string | null;
	userSessions: UserSession[];
	assignedQuestions: string[];
	userExitCount: number;
	userEnterCount: number;
	multipleUserDetected: number;
	illegalObjectDetected: number;
	temp: boolean;
	rollNo?: string;
	latestPlatform?: {
		browser: string;
		browser_version: string;
		os: string;
	};
	feedback?: {
		userId: string;
		quizId: string;
		timestamp: string;
		rating: number;
		feedbackText: string;
	};
}

export interface LoginDetail {
	ip: string;
	loginTime: string;
	_id: string;
	platform: string;
}

export interface UserSession {
	_id: string;
	startTime: string;
	endTime: string;
	sessionTime: number;
	explicitSubmission?: boolean;
	extraTime: number;
	isAutoSubmit?: boolean;
	isForceSubmit?: boolean;
}

export interface Quiz {
	onlySuspiciousRoom?: boolean | undefined;
	_id: string;
	updatedAt: string;
	createdAt: string;
	title: string;
	description: string;
	keywords?: string;
	quizTime: number | string;
	endTime?: string;
	entryStopTime?: string;
	quizCode: number;
	instructions: string;
	allowedIP?: string;
	cutOffMarks: number;
	poolQuestion: boolean;
	createdBy: string;
	parentIdOfCreator: string;
	__v: number;
	updatedBy: string;
	quizUserDetails: QuizUserDetail[];
	isPublic: boolean;
	quizEmailSetting?: QuizEmailSetting;
	isSignUpAllowed: boolean;
	isPrivate: boolean;
	isCreatedByRecruiter: boolean;
	isCreatedByMentor: boolean;
	sendMailToMentorAllowed: boolean;
	tabSwitchAllowed: boolean;
	copyPasteAllowed: boolean;
	quizParams: unknown[];
	startTime: string;
	quizSegments: QuizSegment[];
	displaystatus: number;
	revisitAllowed: boolean;
	attemptInSequence: boolean;
	languagesAllowed: string[];
	questionId: string[];
	quizContent: QuizContent[];
	orgId: string;
	isTemplate?: boolean;
	isAppOnly: boolean;
	isFullScreen: boolean;
	isWebCamAllowed: boolean;
	isRandomImageCaptureEnabled?: boolean;
	isAIProctoringEnabled?: boolean;
	showResults: boolean;
	tabSwitchAlertLimit: number;
	randomizeQuestion: boolean;
	isRecordingUploadBlockingEnabled?: boolean;
	isVmDetectionEnabled?: boolean;
	toTerminateTestOnVmDetection?: boolean;
	isMarkedAsCheck?: boolean;
	isFrozen?: boolean;
	isRecordingEnabled?: boolean;
	createdByTransfer?: boolean;
	usersInLiveStreamRoom?: unknown[];
	allowClose?: boolean;
	roomEnabled?: boolean;
	isLiveStreamEnabled?: boolean;
	submitTestOnTabSwitchLimitBreach?: boolean;
	progressStep?: number;
	lastUsed?: number;
	fromRam?: boolean;
	userQuizSubmittedSegment?: UserQuizSubmittedSegment;
	quizSubmittedQuestion?: QuizSubmittedQuestion[];
	displayname?: string;
	email?: string;
	enrollmentId?: string;
	canUpdateScore?: number;
	toShuffleMCQOptions?: boolean;
}

export interface QuizUserDetail {
	fieldIsSelected: boolean;
	fieldType: number;
	fieldName: string;
	fieldLabel: string;
	_id: string;
	fieldOptions: unknown[];
	fieldValue?: string;
}

export interface QuizEmailSetting {
	sendMail: boolean;
	testCompletionStudentMail: number;
	quizCompletionIntimationSetting: number;
	mailTemplate: MailTemplate;
	intimationMailTemplate: IntimationMailTemplate;
}

export interface MailTemplate {
	subject: string;
	msg: string[];
}

export interface IntimationMailTemplate {
	msg: string[];
	subject: string;
}

export interface QuizSegment {
	pollNumber?: number;
	title: string;
	_id: string;
	count: number;
}

export interface QuizContent {
	contentType: string;
	isFreeze?: boolean;
	showTail: boolean;
	showHead: boolean;
	executionType: string;
	id: string;
	_id: string;
	showCustomInput: boolean;
	question?: Question;
	quizSubmittedQuestion?: QuizSubmittedQuestion;
	attempts?: Attempts;
}

export interface QuizContentResponse {
	isFrozen: boolean;
	previewOnly: boolean;
	contentModify: boolean;
	quiz: {
		quizContentArray: TestContentResponse[];
		quizObj: {
			_id: string;
			updatedAt: string;
			createdAt: string;
			title: string;
			description: string;
			keywords: string;
			quizTime: number;
			quizCode: number;
			instructions: string;
			allowedIP: string;
			cutOffMarks: number;
			createdBy: string;
			parentIdOfCreator: string;
			orgId: string;
			__v: number;
			randomizeQuestion: boolean;
			poolQuestion: boolean;
			isWebCamAllowed: boolean;
			showResults: boolean;
			updatedBy: unknown;
			isFullScreen: boolean;
			tabSwitchAlertLimit: number;
			quizUserDetails?: Array<{
				fieldIsSelected: boolean;
				fieldType: number;
				fieldName: string;
				fieldLabel: string;
				_id: string;
				fieldOptions: Array<string>;
			}>;
			isPublic: boolean;
			quizEmailSetting: {
				sendMail: boolean;
				intimationMailTemplate: {
					subject: string;
					msg: Array<string>;
				};
				mailTemplate: {
					subject: string;
					msg: Array<string>;
				};
				quizCompletionIntimationSetting: number;
				testCompletionStudentMail: number;
			};
			isSignUpAllowed: boolean;
			isPrivate: boolean;
			isCreatedByRecruiter: boolean;
			isCreatedByMentor: boolean;
			sendMailToMentorAllowed: boolean;
			tabSwitchAllowed: boolean;
			copyPasteAllowed: boolean;
			quizParams: Array<unknown>;
			startTime: string;
			quizSegments: Array<{
				title: string;
				_id: string;
				count: number;
				desc?: string;
				pollNumber?: number;
			}>;
			displaystatus: number;
			revisitAllowed: boolean;
			attemptInSequence: boolean;
			languagesAllowed: Array<unknown>;
			questionId: Array<string>;
			quizContent: Array<{
				contentType: string;
				showTail: boolean;
				showHead: boolean;
				executionType: string;
				id: string;
				_id: string;
				showCustomInput: boolean;
			}>;
			isAppOnly: boolean;
			isTemplate: boolean;
			usersInLiveStreamRoom: Array<string>;
			isLiveStreamEnabled: boolean;
			isRecordingEnabled: boolean;
			submitTestOnTabSwitchLimitBreach: boolean;
			toTerminateTestOnVmDetection: boolean;
			allowClose: boolean;
			isQuiz: boolean;
		};
	};
}

export interface Question {
	_id: string;
	updatedAt: string;
	createdAt: string;
	type: string;
	text: string;
	title: string;
	tags: string;
	askconf: boolean;
	difficultyLevel: string;
	executionTime: string;
	createdBy: string;
	parentIdOfCreator: string;
	isPublic: boolean;
	referenceLinks?: string[] | null;
	files: string[];
	questionTypeCoding?: QuestionTypeCoding | null;
	questionTypeMCQ?: QuestionTypeMcq | null;
	importedby: unknown[];
	displaystatus: number;
	score: number;
	__v: number;
	updatedBy?: string;
	questionTypeSubjective?: QuestionTypeSubjective;
	showhint?: boolean;
	hint?: unknown;
	explanation?: unknown;
	isPremium: boolean;
	orgId: string;
	negativeScore: number;
	questionTypeWeb?: QuestionTypeWeb | null;
	createdByTransfer?: boolean;
	isLocked?: boolean;
	bloomTaxonomy?: string;
	courseOutcomes?: string;
	topic?: string;
	courseId?: string[];
	quizId?: string[];
	isCqDocument?: boolean;
}

export interface QuestionTypeCoding {
	testCase: TestCase[];
	codeproglang: Codeproglang[];
	multipleTestCases?: boolean;
}

export interface TestCase {
	sampleTest: boolean;
	attemptInMultiLine: boolean;
	codeprogexpectedoutput: string;
	codeproginputparams: string;
	_id: string;
	scoreip: number;
	onCloud?: boolean;
}

export interface Codeproglang {
	_id: string;
	language: string;
	defaultTrimmedCode: string;
	codeComponents: CodeComponents;
	defaultCompileResult: DefaultCompileResult;
}

export interface CodeComponents {
	head: string;
	body: string;
	tail: string;
	solution?: string;
}

export interface DefaultCompileResult {
	output: string;
	errors: string;
}

export interface QuestionTypeMcq {
	answer?: string;
	correctAnswers?: number[];
	options: string[];
}

export interface QuestionTypeWeb {
	html: string;
	css: string;
	js: string;
	isHtmlAllowed: boolean;
	isCssAllowed: boolean;
	isJsAllowed: boolean;
	isReactQuestion: boolean;
	reactRendererCode: string;
	testRunnerNeeded: boolean;
	testRunnerFileContent: string;
	testCase: {
		description: string;
		evaluator: string;
		sampleTest: boolean;
		_id: string;
		scoreip: number;
	}[];
}
export interface QuestionTypeSubjective {
	subjectiveanswer: string;
	isFileUpload: boolean;
}

export interface UserReportRoot {
	quiz: Quiz;
	userQuizSubmittedSegmentObj: UserQuizSubmittedSegmentObj;
	userId: string;
	session?: Session;
	role: string;
	email: string;
	attemptedUserId: string;
	contentType: ContentType;
	questionType: QuestionType;
	tabSwitchEventType: TabSwitchEventType;
}

export interface QuizSubmittedQuestion {
	userInputMCQ?: string;
	_id: string;
	questionId: string;
	startTime: string;
	userOutputCoding: unknown[];
	hasSubmitted: boolean;
	submissions: unknown[];
	additionalScore: number;
	scoreUpdationActivity: unknown[];
	score: number;
}

export interface Attempts {
	userAttempts: UserAttempt[];
}

export interface UserAttempt {
	attemptData: AttemptData[];
	questionId: string;
	title: string;
	type: string;
	containerTitle: string;
}

export interface AttemptData {
	totalTestCase: string;
	_id: string;
	userInputMCQ: string;
	webTestCaseStatus: unknown[];
	files: unknown[];
	suspiciousActivityData: unknown[];
	finalSubmission: boolean;
	timeOfCreation: string;
	userCompilationError: string;
	userOutputCoding: unknown[];
	attemptBucketId: string;
	testCasesPassed: number;
	fromRedis?: string;
	submissionId?: string;
}

export interface UserQuizSubmittedSegment {
	startTime: string;
	extraTime: number;
	_id: string;
	quizId: QuizId;
	userId: string;
	__v: number;
	camBlockCount: number;
	codePasteCount: number;
	createdAt: string;
	currentIp: string;
	displayName: string;
	email: string;
	endTime: string;
	explicitSubmission: boolean;
	forceLoginCount: number;
	fullScreenInCount: number;
	fullScreenOutCount: number;
	hasQuizStarted: boolean;
	idleTime: number;
	loginCount: number;
	loginDetails: LoginDetail[];
	quizSubmittedQuestion: QuizSubmittedQuestion[];
	quizSubmittedSegments: unknown[];
	quizUserDetails: QuizUserDetail[];
	rollNo: string;
	submittedQuestions: string[];
	tabSwitchCount: number;
	tabSwitchInCount: number;
	tabSwitchReport: TabSwitchReport[];
	tryTest: boolean;
	updatedAt: string;
	userSessions?: UserSession[];
	segmentSubmissionHistory: SubmissionDataType[];
	questionId?: string[];
}
export interface SubmissionDataType {
	time: string;
	segmentIndex: number;
	submissionType: number;
	createdBy: {
		_id: string;
		displayname: string;
		email: string;
	};
	imageURL: string;
	_id?: string;
}
export interface QuizId {
	_id: string;
	title: string;
	quizTime: number;
	cutOffMarks: number;
	endTime: string;
	startTime: string;
	quizSegments: QuizSegment[];
	displaystatus: number;
	revisitAllowed: boolean;
	attemptInSequence: boolean;
	languagesAllowed: string[];
	quizContent: QuizContent[];
}

export interface LoginDetail {
	ip: string;
	platform: string;
	loginTime: string;
	_id: string;
}

export interface TabSwitchReport {
	tabSwitchType: number;
	tabTime: string;
	_id: string;
}

export interface UserQuizSubmittedSegmentObj {
	_id: string;
	quizId: QuizId;
	userId: string;
	__v: number;
	camBlockCount: number;
	codePasteCount: number;
	createdAt: string;
	currentIp: string;
	displayName: string;
	email: string;
	startTime?: string;
	endTime: string;
	extraTime?: string;
	sessionTime?: string;
	explicitSubmission: boolean;
	forceLoginCount: number;
	fullScreenInCount: number;
	fullScreenOutCount: number;
	hasQuizStarted: boolean;
	idleTime: number;
	loginCount: number;
	loginDetails: LoginDetail[];
	quizSubmittedQuestion: QuizSubmittedQuestion[];
	quizSubmittedSegments: unknown[];
	quizUserDetails: QuizUserDetail[];
	rollNo: string;
	submittedQuestions: string[];
	tabSwitchCount: number;
	tabSwitchInCount: number;
	tabSwitchReport: TabSwitchReport[];
	tryTest: boolean;
	updatedAt: string;
	userSessions: UserSession[];
	segmentSubmissionHistory: SubmissionDataType[];
}

export interface ContentType {
	contentTypeMCQ: string;
	contentTypeSubjective: string;
	contentTypeCoding: string;
	contentTypeStepwise: string;
	contentTypeTutorial: string;
	contentTypeQuiz: string;
	contentTypeProject: string;
	contentTypeWeb: string;
	question: string;
	course: string;
	quiz: string;
	tutorial: string;
}

export interface QuestionType {
	questionTypeMCQ: string;
	questionTypeSubjective: string;
	questionTypeCoding: string;
	questionTypeStepwise: string;
	questionTypeWeb: string;
}

export interface TabSwitchEventType {
	in: number;
	out: number;
}

export interface COReport {
	testTitle: string;
	questionNo: number;
	maxMarks: number;
	coMapped: string[] | string;
	btLevel: string[] | string;
}

export type TestDetailsReportDataType = {
	userScore: number;
	totalScore: number;
	userAttemptQues: number;
	totalQues: number;
	startTime?: string | number | undefined;
	endTime?: string | undefined;
	CompletedIn?: string | undefined;
	tabSwitchInCount?: number | undefined;
	tabSwitchCount?: number | undefined;
	fullScreenInCount?: number | undefined;
	fullScreenOutCount?: number | undefined;
	codePasteCount?: number | undefined;
	loginCount?: number | undefined;
	camBlockCount?: number | undefined;
};
