import React from "react";
import { <PERSON><PERSON>, Card, Divider, Modal } from "antd";
import { ReportDataType } from "./data/data";

interface ModalCompProps {
	title: string;
	name?: string;
	email?: string;
	children?: React.ReactNode;
	onClose: () => void;
	extraTimeLogsModalData?: ReportDataType;
}

const { Meta } = Card;
const LogsModal = ({
	title,
	name,
	email,
	children,
	onClose,
	extraTimeLogsModalData,
}: ModalCompProps) => {
	return (
		<>
			<Modal
				title={title}
				open={extraTimeLogsModalData !== undefined}
				onOk={onClose}
				footer={null}
				onCancel={onClose}
				styles={{ body: { padding: 0 } }}
				destroyOnClose={true}
				width={660}
			>
				<Card
					style={{
						border: "none",
					}}
				>
					<Meta
						avatar={
							<Avatar
								src={`https://api.dicebear.com/5.x/initials/svg?seed=${name}&backgroundColor=DE6834&chars=1`}
								size={48}
							/>
						}
						title={name}
						description={email}
					/>
					<Divider style={{ margin: "16px 0px" }} />
					{children}
				</Card>
			</Modal>
		</>
	);
};

export default LogsModal;
