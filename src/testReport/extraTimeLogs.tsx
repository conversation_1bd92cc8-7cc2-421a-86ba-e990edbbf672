import { DataTable } from "@/components/dataTable/dataTable";
import formatDuration from "@/utils/durationTime";
import timeDate from "@/utils/timeDate";
import { quizClient } from "../store";
import { TableColumnsType } from "antd";
import { useEffect, useState } from "react";

interface ExtraTimeDataTypes {
	extraTime: number;
	expireAt: string;
	createdBy: {
		_id: string;
		displayname: string;
	};
	createdAt: string;
	_id: string;
}

interface ExtrTimeLogsProps {
	testId?: string;
	userId?: string;
	setTabSwitchDataLength?: (_length: number) => void;
	scroll?: { y: number };
}

const columns: TableColumnsType<ExtraTimeDataTypes> = [
	{
		key: "Serial Number",
		render: (_value, _record, index) => index + 1,
		width: 30,
		fixed: "left",
	},
	{
		key: "displayname",
		title: "Added By",
		dataIndex: "displayname",
		render: (_, record) => record?.createdBy?.displayname,
	},
	{
		key: "extraTime",
		title: "Extra Time",
		dataIndex: "extraTime",
		render: value => (value ? formatDuration(value) : "-NA-"),
	},
	{
		key: "createdAt",
		title: "Created At",
		dataIndex: "createdAt",
		render: value => (value ? timeDate(value) : "-NA-"),
	},
];

const ExtraTimeLogs = ({ testId, userId, scroll }: ExtrTimeLogsProps) => {
	const [loading, setLoading] = useState(false);
	const [extraTimelLogsData, setExtraTimelLogsData] = useState<
		ExtraTimeDataTypes[]
	>([]);
	useEffect(() => {
		if (!testId || !userId) return;
		(async function () {
			setLoading(true);
			try {
				const response = await quizClient.getExtraTimeLogs(
					testId,
					userId
				);
				if (response) {
					const data = response?.data?.extraTime;
					if (!data) return;
					setExtraTimelLogsData(data);
				}
			} catch (ex) {
				console.log(ex);
			} finally {
				setLoading(false);
			}
		})();
	}, [testId, userId]);

	return (
		<>
			<DataTable<ExtraTimeDataTypes>
				columns={columns}
				data={extraTimelLogsData}
				loading={loading}
				pagination={false}
				scroll={scroll}
			/>
		</>
	);
};
export default ExtraTimeLogs;
