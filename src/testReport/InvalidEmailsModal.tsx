import { DataTable } from "@/components/dataTable/dataTable";
import { Button, Flex, Modal, TableColumnsType, Typography } from "antd";
import { DownloadOutlined } from "@ant-design/icons";
import * as XLSX from "xlsx-js-style";

const { Text } = Typography;
interface emailData {
	_id: string;
	email: string;
}
const columns: TableColumnsType<emailData> = [
	{
		key: "Serial Number",
		render: (_value, _record, index) => `${index + 1}.`,
		width: 45,
		align: "right",
	},
	{
		key: "email",
		title: "Emails",
		dataIndex: "email",
	},
];
const handleDownload = ({ failedEmails }: { failedEmails: string[] }) => {
	const exportData = [];
	const headers = ["emails"];
	exportData.push(headers);
	failedEmails.forEach((item: string) => {
		const row = [];
		row.push(item);
		exportData.push(row);
	});
	const workbook = XLSX.utils.book_new();
	const worksheet = XLSX.utils.aoa_to_sheet(exportData);

	XLSX.utils.book_append_sheet(workbook, worksheet, `invalidEmails`);
	XLSX.writeFile(workbook, `bulk-allowed-invalid.xlsx`);
};
const InValidEmialsModal = ({
	failedEmails,
	onClose,
}: {
	failedEmails: string[];
	onClose: () => void;
}) => {
	const failedMailData = failedEmails !== undefined;
	const data: Array<emailData> = [];
	if (failedEmails && failedEmails.length > 0) {
		for (const i in failedEmails) {
			data.push({ _id: i, email: failedEmails[i] });
		}
	}
	return (
		<>
			<Modal
				open={failedMailData}
				title="Invalid Emails"
				onCancel={() => onClose()}
				footer={[
					<Button key="close" onClick={() => onClose()}>
						Close
					</Button>,
					<Button
						key="download"
						type="primary"
						icon={<DownloadOutlined />}
						onClick={() => handleDownload({ failedEmails })}
					>
						Download
					</Button>,
				]}
				style={{ gap: "50px" }}
			>
				<Flex vertical gap="middle">
					<Text type="secondary">
						Below are the emails which do not follow correct email
						syntax
					</Text>
					<DataTable<emailData>
						columns={columns}
						data={data}
						pagination={false}
						scroll={{ y: 200 }}
					/>
				</Flex>
			</Modal>
		</>
	);
};

export default InValidEmialsModal;
