import { useCallback, useEffect, useRef, useState } from "react";
import { Modal, Input, Button, Flex, Typography } from "antd";
import type { GetProps } from "antd";
import { useSocketMutation, useSocketQuery } from "@/hooks/socket";
import { quizClient } from "@/store";

type OTPProps = GetProps<typeof Input.OTP>;

export const FinishTestModel = ({
	visible,
	quizId,
	onClose,
	sendOtp,
	onSubmit,
}: {
	visible: boolean;
	onClose?: () => void;
	sendOtp: () => Promise<void>;
	onSubmit?: (_message: string, _type: "success" | "error") => void;
	quizId?: string;
}) => {
	const [otp, setOtp] = useState<string>("");
	const [counter, setCounter] = useState<number>(0);
	const [otpSent, setOtpSent] = useState<boolean>(false);
	const [otpStatus, setOtpStatus] = useState<"" | "error">("");
	const [loading, setLoading] = useState<boolean>(false);
	const [resendLoading, setResendLoading] = useState<boolean>(false);

	// one-shot guard for success path
	const didSubmitRef = useRef<boolean>(false);

	const { get, data } = useSocketQuery<
		"completed",
		Promise<{ error: string }>
	>("completed", async data => {
		console.log("Socket Result: ", data);
		setLoading(false);
		return data;
	});

	useEffect(() => {
		let timer: NodeJS.Timeout | undefined;
		if (counter > 0) {
			timer = setInterval(() => {
				setCounter(prev => prev - 1);
			}, 1000);
		}
		return () => {
			if (timer) clearInterval(timer);
		};
	}, [counter]);

	const handleResendOTP = async () => {
		setResendLoading(true);
		await sendOtp();
		await getResendOTPTime();
		setOtpStatus("");
		setOtpSent(true);
		setResendLoading(false);
	};

	const { mutateAsync } = useSocketMutation<[string, string], void>(
		async (socket, quizId: string, otp: string): Promise<void> => {
			socket.emit("submit_all_test", quizId, otp);
		}
	);

	// reset the one-shot guard when dialog closes or quizId changes
	useEffect(() => {
		if (!visible) {
			didSubmitRef.current = false;
		}
	}, [visible, quizId]);

	// safe JSON parse without any
	const safeParseJSON = (s: string) => {
		try {
			return JSON.parse(s);
		} catch {
			return undefined;
		}
	};

	useEffect(() => {
		if (data == null) return;

		// normalize payload (string or object)
		const parsed = typeof data === "string" ? safeParseJSON(data) : data;

		// if we can’t make sense of it, bail
		if (parsed == null) return;

		console.log("Socket Data: ", parsed);
		setLoading(false);

		// detect error shape safely without `any`
		const maybeRecord: Record<string, unknown> =
			typeof parsed === "object"
				? (parsed as Record<string, unknown>)
				: {};

		if (
			"error" in maybeRecord &&
			typeof maybeRecord.error === "string" &&
			maybeRecord.error
		) {
			setOtpStatus("error");
			return;
		}

		// success → fire once
		if (didSubmitRef.current) return;
		didSubmitRef.current = true;

		setOtpStatus("");
		onSubmit?.("Test submitted successfully", "success");
		setOtpSent(false);
		setOtp("");
		onClose?.();
	}, [data, onClose, onSubmit]);

	const handleVerify = useCallback(async () => {
		if (!quizId) return;
		if (!otp || otp.trim() === "" || otp.length !== 6) {
			setOtpStatus("error");
			return;
		}
		console.log("Submitted OTP:", otp);
		setLoading(true);
		get(async () => {
			await mutateAsync(quizId, otp);
		});
	}, [quizId, otp, get, mutateAsync]);

	const onChange: OTPProps["onChange"] = text => {
		setOtp(text);
	};

	const getResendOTPTime = async () => {
		try {
			const timer = await quizClient.getOtpWaitTime(quizId || "");
			if (timer.waitTime_Sec > 0) setCounter(timer.waitTime_Sec);
			else setCounter(0);
		} catch (error) {
			console.log(error);
			setCounter(0);
		}
	};

	const handleSendOTP = async () => {
		console.log("OTP Sent");
		await sendOtp();
		await getResendOTPTime();
		setOtpSent(true);
	};

	return (
		<Modal
			title="Submit selected Tests"
			open={visible}
			onCancel={() => onClose?.()}
			footer={
				otpSent ? (
					<>
						<Button
							type="primary"
							onClick={handleResendOTP}
							disabled={counter > 0}
							loading={resendLoading}
						>
							{counter > 0
								? `Resend OTP (${counter}s)`
								: "Resend OTP"}
						</Button>
						<Button
							onClick={handleVerify}
							type="primary"
							loading={loading}
						>
							Verify
						</Button>
					</>
				) : (
					<Button
						onClick={handleSendOTP}
						type="primary"
						disabled={counter > 0}
					>
						{counter > 0 ? `Send OTP (${counter}s)` : "Send OTP"}
					</Button>
				)
			}
			style={{ top: 20 }}
		>
			{otpSent ? (
				<>
					<Flex gap={16} vertical style={{ marginBottom: 16 }}>
						<Typography.Text>Enter OTP</Typography.Text>
						<Input.OTP
							style={{ width: "70%" }}
							onChange={onChange}
							value={otp}
							status={otpStatus}
						/>
						{otpStatus === "error" && (
							<Typography.Text
								type="danger"
								style={{ margin: 0 }}
							>
								! Invalid OTP
							</Typography.Text>
						)}
					</Flex>
				</>
			) : (
				<Flex vertical gap={16}>
					<Typography.Text>
						A one-time password (OTP) will be sent to your email
						address.
					</Typography.Text>
				</Flex>
			)}
		</Modal>
	);
};
