import { DataTable } from "@/components/dataTable/dataTable";
import {
	AllowedCandidates,
	Invitee,
	Quiz,
	QuizReport,
	ReportDataType,
	UserDataArray,
} from "./data/data";
import {
	Checkbox,
	Dropdown,
	TableColumnsType,
	MenuProps,
	Button,
	Space,
	Select,
	Flex,
	Layout,
	theme,
	Card,
	Switch,
	message,
	Badge,
	Modal,
	Typography,
	Tooltip,
} from "antd";
// import { data } from "./data/data";
import {
	isValidElement,
	Key,
	useCallback,
	useEffect,
	useMemo,
	useState,
} from "react";
import { Link, useLocation, useNavigate, useParams } from "react-router";
import Search from "antd/es/input/Search";
import Sider from "antd/es/layout/Sider";
import { Content, Header } from "antd/es/layout/layout";
import {
	CalendarOutlined,
	CheckCircleOutlined,
	ClockCircleOutlined,
	CloseOutlined,
	CopyOutlined,
	DeleteOutlined,
	DisconnectOutlined,
	DownOutlined,
	FileDoneOutlined,
	FileImageOutlined,
	<PERSON>Outlined,
	MailOutlined,
	MoreOutlined,
	SettingOutlined,
	UnorderedListOutlined,
	UsergroupAddOutlined,
	UserSwitchOutlined,
	VideoCameraOutlined,
} from "@ant-design/icons";
import FilterMenu, { FilterMenuItem } from "@/components/filterMenu/filterMenu";
import { FilterIcon } from "lucide-react";
import * as XLSX from "xlsx-js-style";
import ModalComp from "@/components/userReports/modal";
import TabSwitch from "@/components/userReports/activityTables/tabSwitch";
import FullScreen from "@/components/userReports/activityTables/fullScreen";
import LoginCount from "@/components/userReports/activityTables/loginCount";
import UserProctoringCount from "@/components/userReports/proctoringTables/userProctoringCount";
import {
	aiProctorScore,
	FeedBackRating,
	PlatformOrganisation,
	ProctoringType,
} from "@/constants";
import SectionControlModal, {
	SectionControlDataType,
} from "@/components/Menu-Components/SectionControl";
import ExtraTime from "@/components/Menu-Components/ExtraTime";
import ExtraTimeLogs from "./extraTimeLogs";
import LogsModal from "./logsModal";
import {
	masterVideoClient,
	quizClient,
	useAppStore,
	useTestStore,
} from "../store";
import FinishTest from "@/components/Menu-Components/FinishTest";
import InviteSidebar from "@/components/invite-sidebar/InviteSidebar";
import timeDate from "@/utils/timeDate";
import { AppConfig } from "@/config";
import dayjs from "dayjs";
import UpdateTime from "@/components/Menu-Components/UpdateTime";
import { useAppMessage } from "@/hooks/message";
import { RoleAction, RoleResource } from "@/constants/roles";
import {
	ReportSearchState,
	useReportPersistentStore,
} from "@/hooks/presistentStorage";
import { MarkQuizAsResolved } from "./markQuizAsResolved";
const fixedColumns = ["Serial Number", "displayname", "email", "Actions"];
const options = [
	{
		value: 1,
		label: "Report",
	},
	{
		value: 2,
		label: "Invited Candidates",
	},
	{
		value: 3,
		label: "Allowed Candidates",
	},
];

export interface InvitedUsers extends Invitee {
	attemptStatus: boolean;
}

export type TestReportFilters = {
	dateRange?: [string, string];
	slider?: [number, number] | null;
	tags: string[];
	attemptType: number;
};

interface SelectedRowsState {
	selectedRowKeys: Key[];
	selectedRows: ReportDataType[] | Invitee[] | AllowedCandidates[];
}

export default function TestReport() {
	const location = useLocation();
	const shouldShowCompleted = !new URLSearchParams(location.search).has(
		"force_hide_completed"
	);
	const params = useParams();
	const testId = params.id as string;
	const { jwtToken } = params;
	const navigate = useNavigate();
	const messageInstance = useAppMessage();
	const hasResourcePermission = useAppStore(
		state => state.hasResourcePermission
	);
	const [reportState, setReportState] = useReportPersistentStore(testId);
	const [marksRange, setMarksRange] = useState<[number, number] | null>(null);
	const [tableData, setTableData] = useState<ReportDataType[]>([]);
	const [quizData, setQuizData] = useState<Quiz>();
	const [canChangeTestMark, setCanChangeTestMark] = useState<boolean>(false);
	const [refreshData, setRefreshData] = useState(0);
	const [loading, setLoading] = useState<boolean>(true);
	const [showColumns, setShowColumns] = useState<boolean>(false);
	const [showFilters, setShowFilters] = useState<boolean>(false);
	const [selectedState, setSelectedState] = useState<SelectedRowsState>({
		selectedRowKeys: [],
		selectedRows: [],
	});
	const [invitedUsers, setInvitedUsers] = useState<InvitedUsers[]>([]);
	const [allowedUsers, setAllowedUsers] = useState<AllowedCandidates[]>([]);
	const [updateTime, setUpdateTime] = useState<InvitedUsers | null>(null);
	const [sectionControlData, setSectionControlData] =
		useState<SectionControlDataType>();
	const [extraTimeModalData, setExtraTimeModalData] = useState<
		ReportDataType[]
	>([]);
	const [finishTestUsers, setFinishTestUsers] = useState<ReportDataType[]>(
		[]
	);

	const [extraTimeLogsModalData, setExtraTimeLogsModalData] =
		useState<ReportDataType>();
	const [selectedInvitee, setSelectedInvitee] = useState<string[]>([]);
	const [isInviteSideBarOpen, setIsInviteSideBarOpen] =
		useState<boolean>(false);
	const [downloadingCoReport, setDownloadingCoReport] =
		useState<boolean>(false);

	const [modal, modalContextHolder] = Modal.useModal();
	const [markQuizAsCheckedState, setMarkQuizAsCheckedState] =
		useState<MarkQuizAsResolvedState>({
			currentState: -1,
			visible: jwtToken ? true : false,
		});

	const newOptions = useMemo(() => {
		let finalOptions = options;
		if (quizData?.isPrivate) {
			finalOptions = finalOptions.filter(option => option.value !== 3);
		}

		return finalOptions;
	}, [quizData?.isPrivate]);

	const openSectionControlModal = useCallback(
		(data: ReportDataType[]) => {
			const segmentNames = quizData?.quizSegments.map(
				segment => segment.title
			);
			const sectionControlData: SectionControlDataType = {
				segmentNames,
				users: data,
			};
			setSectionControlData(sectionControlData);
		},
		[quizData?.quizSegments]
	);
	function closeSectionControlModal() {
		setSectionControlData(undefined);
	}
	function closeExtraTimeModal() {
		setExtraTimeModalData([]);
	}
	function extraTimeModalOpen(data: ReportDataType) {
		setExtraTimeModalData([data]);
	}

	function extraTimeLogsModal(data: ReportDataType) {
		setExtraTimeLogsModalData(data);
	}
	function closeExtraTimeLogsModal() {
		setExtraTimeLogsModalData(undefined);
	}

	const handleBulkAddExtraTime = useCallback(() => {
		setExtraTimeModalData(selectedState.selectedRows as ReportDataType[]);
	}, [selectedState.selectedRows]);

	const handleInviteSidebar = useCallback((emails: string[]) => {
		setSelectedInvitee(emails ?? []);
		setIsInviteSideBarOpen(true);
	}, []);
	const closeInviteSideBar = () => {
		setSelectedInvitee([]);
		setIsInviteSideBarOpen(false);
	};

	const allowedEmails = useTestStore(state =>
		state.getTestAllowedCandidate(testId)
	);

	const updatedInvitedUsers = useTestStore(state =>
		state.getTestInvitedCandidate(testId)
	);

	useEffect(() => {
		if (!allowedEmails) return;
		setAllowedUsers(allowedEmails);
	}, [allowedEmails]);

	useEffect(() => {
		if (!updatedInvitedUsers) return;
		setInvitedUsers(updatedInvitedUsers);
	}, [updatedInvitedUsers]);

	const handleDownloadVideo = useCallback(
		async (userId: string) => {
			try {
				const response = await quizClient.getTokenForVideoDownload(
					testId,
					userId
				);

				const videoDownloadUrl = await getDownloadURL(response.token);
				if (typeof videoDownloadUrl === "string") {
					window.location.href = videoDownloadUrl;
				} else {
					throw "Invalid video download URL";
				}
			} catch (error) {
				console.log(error);
				messageInstance?.error(error as string);
			}
		},
		[testId]
	);

	const getDownloadURL = async (jwt: string) => {
		const response = await masterVideoClient.checkVideoStatus(jwt);

		if (!("error" in response) && response.status === "complete") {
			return response.presignedGetURL;
		}
		messageInstance?.loading({
			content: "Video is being processed...",
			key: "downloadVideo",
			duration: 0,
		});
		const data = await new Promise((resolve, reject) => {
			const intervalId = setInterval(async () => {
				try {
					const response =
						await masterVideoClient.checkVideoStatus(jwt);
					if ("error" in response) {
						clearInterval(intervalId);
						reject(response.error);
					} else if (response.status === "complete") {
						clearInterval(intervalId);
						resolve(response.presignedGetURL);
					}
				} catch (error) {
					console.log(error);
					messageInstance?.loading({
						content: "Failed to download video",
						key: "downloadVideo",
						duration: 0,
					});
				} finally {
					messageInstance?.destroy();
				}
			}, 2000);
		});
		messageInstance?.success("Video downloaded successfully");
		messageInstance?.destroy();
		return data;
	};

	const customFieldColumns: TableColumnsType<ReportDataType> = useMemo(() => {
		return (quizData?.quizUserDetails ?? []).map(field => ({
			title: (
				<Typography.Text ellipsis>{field.fieldLabel}</Typography.Text>
			),
			key: field._id,
			dataIndex: field._id,
			width: 100,
			onHeaderCell: () => ({ style: { zIndex: "0" } }),
			render: value => {
				return value ? (
					<Tooltip title={value}>
						<Typography.Text ellipsis>{value}</Typography.Text>
					</Tooltip>
				) : (
					"-"
				);
			},
		}));
	}, [quizData?.quizUserDetails]);

	const bulkItems: MenuProps["items"] = useMemo(() => {
		const isAllCompleted = (selectedState.selectedRows ?? []).every(row => {
			if (row && "completed" in row) {
				return row.completed;
			}
			return false;
		});
		return [
			hasResourcePermission(
				RoleResource.QUIZ,
				RoleAction.PROVIDE_EXTRA_TIME
			) && {
				key: "1",
				label: "Add Extra Time",
				icon: <ClockCircleOutlined style={{ fontSize: "16px" }} />,
				onClick: handleBulkAddExtraTime,
			},
			!isAllCompleted &&
				hasResourcePermission(
					RoleResource.QUIZ,
					RoleAction.PROVIDE_EXTRA_TIME
				) && { type: "divider" },
			!isAllCompleted && {
				key: "2",
				label: "Section Control",
				icon: <UserSwitchOutlined style={{ fontSize: "16px" }} />,
				onClick: () =>
					openSectionControlModal(
						selectedState.selectedRows as ReportDataType[]
					),
			},
			!isAllCompleted &&
				hasResourcePermission(
					RoleResource.QUIZ,
					RoleAction.ALLOW_ALL_TEST_SUBMISSION
				) && { type: "divider" },
			!isAllCompleted &&
				hasResourcePermission(
					RoleResource.QUIZ,
					RoleAction.ALLOW_ALL_TEST_SUBMISSION
				) && {
					key: "3",
					label: "Finish Test",
					icon: <CheckCircleOutlined style={{ fontSize: "16px" }} />,
					onClick: () =>
						setFinishTestUsers(
							selectedState.selectedRows as ReportDataType[]
						),
				},
		].filter(Boolean) as MenuProps["items"];
	}, [
		handleBulkAddExtraTime,
		openSectionControlModal,
		selectedState.selectedRows,
	]);

	const reportColumns: TableColumnsType<ReportDataType> = useMemo(() => {
		return [
			{
				key: "Serial Number",
				render: (_value, _record, index) =>
					(reportState.currentPage - 1) * reportState.pageSize +
					index +
					1,
				width: 50,
				fixed: "left",
				onCell: () => ({ style: { zIndex: "1" } }),
				onHeaderCell: () => ({ style: { zIndex: "1" } }),
			},
			{
				title: "Name",
				dataIndex: "displayname",
				key: "displayname",
				fixed: "left",
				width: 130,
				sorter: (a, b) => a.displayname.localeCompare(b.displayname),
				showSorterTooltip: false,
				render: (value, data) => {
					return (
						<Link
							to={`/tests/${quizData?._id}/user-report/${data._id}`}
						>
							{value}
						</Link>
					);
				},
				onCell: () => ({ style: { zIndex: "1" } }),
				onHeaderCell: () => ({ style: { zIndex: "1" } }),
			},
			{
				title: "Email",
				dataIndex: "email",
				key: "email",
				fixed: "left",
				width: 150,
				ellipsis: true,
				onCell: () => ({ style: { zIndex: "1" } }),
				onHeaderCell: () => ({ style: { zIndex: "1" } }),
			},
			{
				title: "Roll No",
				dataIndex: "enrollmentId",
				key: "rollNo",
				width: 100,
				align: "center",
				showSorterTooltip: false,
				sorter: (a, b) => {
					const aValue = a?.enrollmentId ?? "";
					const bValue = b?.enrollmentId ?? "";
					return aValue.localeCompare(bValue);
				},
				hidden:
					AppConfig.platformOrganisation === PlatformOrganisation.CQ
						? true
						: false,
				render: value => {
					if (Number(value) === 0) return "-";
					return value ?? "-";
				},
				onHeaderCell: () => ({ style: { zIndex: "0" } }),
			},
			{
				title: "Start Time",
				dataIndex: "startTime",
				key: "startTime",
				width: 150,
				align: "center",
				showSorterTooltip: false,
				sorter: (a, b) => {
					const parseTime = (val: string | number) => {
						if (!val) return 0;
						if (/^\d+$/.test(val.toString())) {
							return new Date(Number(val)).getTime();
						}
						const time = new Date(val).getTime();
						return isNaN(time) ? 0 : time;
					};

					const aTime = parseTime(a.startTime);
					const bTime = parseTime(b.startTime);

					return aTime - bTime;
				},

				render: value => {
					if (!value) return "-NA-";
					let dateObj: Date;

					if (/^\d+$/.test(value)) {
						dateObj = new Date(Number(value));
					} else {
						dateObj = new Date(value);
					}
					if (isNaN(dateObj.getTime())) return "-NA-";

					const date = dateObj.toLocaleDateString();
					const time = dateObj.toLocaleTimeString([], {
						hour: "2-digit",
						minute: "2-digit",
						hour12: false,
					});

					return (
						<Space direction="vertical" align="center" size="small">
							{date}
							{time}
						</Space>
					);
				},
				onHeaderCell: () => ({ style: { zIndex: "0" } }),
			},
			{
				title: "Status",
				dataIndex: "status",
				key: "status",
				width: 120,
				align: "center",
				showSorterTooltip: false,
				sorter: (a, b) => a.status.localeCompare(b.status),
				onHeaderCell: () => ({ style: { zIndex: "0" } }),
			},

			{
				title: "End Time",
				dataIndex: "endTime",
				key: "endTime",
				width: 150,
				align: "center",
				showSorterTooltip: false,
				sorter: (a, b) =>
					new Date(a.endTime ?? 0).getTime() -
					new Date(b.endTime ?? 0).getTime(),
				render: value => {
					if (!value) return "-NA-";
					let dateObj: Date;

					if (/^\d+$/.test(value)) {
						dateObj = new Date(Number(value));
					} else {
						dateObj = new Date(value);
					}
					if (isNaN(dateObj.getTime())) return "-NA-";

					const date = dateObj.toLocaleDateString();
					const time = dateObj.toLocaleTimeString([], {
						hour: "2-digit",
						minute: "2-digit",
						hour12: false,
					});

					return (
						<Space direction="vertical" align="center" size="small">
							{date}
							{time}
						</Space>
					);
				},
				onHeaderCell: () => ({ style: { zIndex: "0" } }),
			},
			{
				title: "Time Taken",
				dataIndex: "timeTaken",
				key: "timeTaken",
				width: 200,
				align: "center",
				sorter: (a, b) => Number(a.timeTaken) - Number(b.timeTaken),
				showSorterTooltip: false,
				render: (value, record) => {
					const minutes = Math.floor(Number(value));
					const seconds = Math.round((Number(value) - minutes) * 60);
					if (!record.completed) {
						return "-NA-";
					}
					return (
						<>
							{minutes} min {seconds > 0 ? `${seconds} sec` : ""}
						</>
					);
				},
				onHeaderCell: () => ({ style: { zIndex: "0" } }),
			},
			{
				title: "Total Marks",
				dataIndex: "totalMarks",
				key: "totalMarks",
				width: 200,
				align: "center",
				showSorterTooltip: false,
				onHeaderCell: () => ({ style: { zIndex: "0" } }),
			},
			{
				title: "Marks Obtained",
				dataIndex: "score",
				key: "score",
				width: 200,
				align: "center",
				sorter: (a, b) => a.score - b.score,
				showSorterTooltip: false,
				onHeaderCell: () => ({ style: { zIndex: "0" } }),
			},
			{
				title: "Attempted Questions",
				dataIndex: "totalAttemptedQuestion",
				key: "totalAttemptedQuestion",
				width: 200,
				align: "center",
				onHeaderCell: () => ({ style: { zIndex: "0" } }),
			},
			{
				title: "Tried Copy-Paste",
				dataIndex: "codePasteCount",
				key: "codePasteCount",
				width: 180,
				align: "center",
				showSorterTooltip: false,

				sorter: (a, b) => a.codePasteCount - b.codePasteCount,
				onHeaderCell: () => ({ style: { zIndex: "0" } }),
			},
			{
				title: "Tab Switch Out",
				dataIndex: "tabSwitchCount",
				key: "tabSwitchCount",
				width: 150,
				align: "center",
				showSorterTooltip: false,

				sorter: (a, b) => a.tabSwitchCount - b.tabSwitchCount,
				render: (text, data) => (
					<ModalComp
						title="Tab Switch Out Count"
						val={text}
						name={data.displayname}
						email={data.email}
					>
						<TabSwitch
							testId={quizData?._id}
							userId={data._id}
							scroll={{ y: 200 }}
						/>
					</ModalComp>
				),
				onHeaderCell: () => ({ style: { zIndex: "0" } }),
			},
			{
				title: "Tab Switch In",
				dataIndex: "tabSwitchInCount",
				key: "tabSwitchInCount",
				width: 150,
				align: "center",
				showSorterTooltip: false,

				sorter: (a, b) => a.tabSwitchInCount - b.tabSwitchInCount,
				render: (text, data) => (
					<ModalComp
						title="Tab Switch In Count"
						val={text}
						name={data.displayname}
						email={data.email}
					>
						<TabSwitch
							testId={quizData?._id}
							userId={data._id}
							scroll={{ y: 200 }}
						/>
					</ModalComp>
				),
				onHeaderCell: () => ({ style: { zIndex: "0" } }),
			},
			{
				title: "Full Screen In",
				dataIndex: "fullScreenInCount",
				key: "fullScreenInCount",
				width: 150,
				align: "center",
				showSorterTooltip: false,

				sorter: (a, b) => a.fullScreenInCount - b.fullScreenInCount,
				render: (text, data) => (
					<ModalComp
						title="Full Screen Count In"
						val={text}
						name={data.displayname}
						email={data.email}
					>
						<FullScreen
							testId={quizData?._id}
							userId={data._id}
							scroll={{ y: 200 }}
						/>
					</ModalComp>
				),
				onHeaderCell: () => ({ style: { zIndex: "0" } }),
			},
			{
				title: "Full Screen Out",
				dataIndex: "fullScreenOutCount",
				key: "fullScreenOutCount",
				width: 150,
				align: "center",
				showSorterTooltip: false,

				sorter: (a, b) => a.fullScreenOutCount - b.fullScreenOutCount,
				render: (text, data) => (
					<ModalComp
						title="Full Screen Count Out"
						val={text}
						name={data.displayname}
						email={data.email}
					>
						<FullScreen
							testId={quizData?._id}
							userId={data._id}
							scroll={{ y: 200 }}
						/>
					</ModalComp>
				),
				onHeaderCell: () => ({ style: { zIndex: "0" } }),
			},
			{
				title: "Login Count",
				dataIndex: "loginCount",
				key: "loginCount",
				width: 140,
				align: "center",
				showSorterTooltip: false,

				sorter: (a, b) => a.loginCount - b.loginCount,
				render: (text, data) => (
					<ModalComp
						title="Login Count"
						val={text}
						name={data.displayname}
						email={data.email}
					>
						<LoginCount
							testId={quizData?._id}
							userId={data._id}
							scroll={{ y: 200 }}
						/>
					</ModalComp>
				),
				onHeaderCell: () => ({ style: { zIndex: "0" } }),
			},
			{
				title: "Cam Block",
				dataIndex: "camBlockCount",
				key: "camBlockCount",
				width: 120,
				align: "center",
				showSorterTooltip: false,

				sorter: (a, b) => a.camBlockCount - b.camBlockCount,
				onHeaderCell: () => ({ style: { zIndex: "0" } }),
			},
			{
				title: "Qualified",
				dataIndex: "qualified",
				key: "qualified",
				width: 100,
				align: "center",
				render: text => (text ? "Pass" : "Fail"),
				onHeaderCell: () => ({ style: { zIndex: "0" } }),
			},
			{
				title: "User Exit",
				dataIndex: "userExitCount",
				key: "userExitCount",
				width: 120,
				align: "center",
				showSorterTooltip: false,

				sorter: (a, b) => a.userExitCount - b.userExitCount,
				render: (text, data) => (
					<ModalComp
						title="AI Protoring"
						val={text}
						name={data.displayname}
						email={data.email}
					>
						<UserProctoringCount
							testId={quizData?._id}
							userId={data._id}
							defaultSubTypeVal={ProctoringType.USER_EXIT}
						/>
					</ModalComp>
				),
				onHeaderCell: () => ({ style: { zIndex: "0" } }),
			},
			{
				title: "User Enter",
				dataIndex: "userEnterCount",
				key: "userEnterCount",
				width: 120,
				align: "center",
				showSorterTooltip: false,

				sorter: (a, b) => a.userEnterCount - b.userEnterCount,
				render: (text, data) => (
					<ModalComp
						title="AI Protoring"
						val={text}
						name={data.displayname}
						email={data.email}
					>
						<UserProctoringCount
							testId={quizData?._id}
							userId={data._id}
							defaultSubTypeVal={ProctoringType.USER_ENTER}
						/>
					</ModalComp>
				),
				onHeaderCell: () => ({ style: { zIndex: "0" } }),
			},
			{
				title: "Multiple Person",
				dataIndex: "multipleUserDetected",
				key: "multipleUserDetected",
				width: 150,
				align: "center",
				showSorterTooltip: false,

				sorter: (a, b) =>
					a.multipleUserDetected - b.multipleUserDetected,
				render: (text, data) => (
					<ModalComp
						title="AI Protoring"
						val={text}
						name={data.displayname}
						email={data.email}
					>
						<UserProctoringCount
							testId={quizData?._id}
							userId={data._id}
							defaultSubTypeVal={ProctoringType.MULTIPLE_PERSON}
						/>
					</ModalComp>
				),
				onHeaderCell: () => ({ style: { zIndex: "0" } }),
			},
			{
				title: "Prohibited items",
				dataIndex: "illegalObjectDetected",
				key: "illegalObjectDetected",
				width: 150,
				align: "center",
				showSorterTooltip: false,

				sorter: (a, b) =>
					a.illegalObjectDetected - b.illegalObjectDetected,
				render: (text, data) => (
					<ModalComp
						title="AI Protoring"
						val={text}
						name={data.displayname}
						email={data.email}
					>
						<UserProctoringCount
							testId={quizData?._id}
							userId={data._id}
							defaultSubTypeVal={ProctoringType.PROHIBITED_ITEMS}
						/>
					</ModalComp>
				),
				onHeaderCell: () => ({ style: { zIndex: "0" } }),
			},
			{
				title: "Suspicious Score",
				dataIndex: "suspiciousScore",
				key: "suspiciousScore",
				width: 160,
				align: "center",
				showSorterTooltip: false,

				sorter: (a, b) => a.suspiciousScore - b.suspiciousScore,
				onHeaderCell: () => ({ style: { zIndex: "0" } }),
			},
			...customFieldColumns,
			{
				title: "Rating",
				dataIndex: "feedback",
				key: "feedback",
				width: 120,
				render: (_value, record) => {
					if (!record.feedback || !record.feedback.rating) return "-";
					return <>{FeedBackRating[record.feedback.rating]}</>;
				},
				onHeaderCell: () => ({ style: { zIndex: "0" } }),
			},
			{
				title: (
					<>
						<Dropdown
							trigger={["click"]}
							disabled={selectedState.selectedRows.length === 0}
							placement="bottomLeft"
							menu={{ items: bulkItems }}
						>
							{selectedState.selectedRows.length === 0 ? (
								"Actions"
							) : (
								<Button
									type="text"
									icon={
										<MoreOutlined
											style={{
												padding: 0,
												fontSize: "18px",
											}}
										/>
									}
								></Button>
							)}
						</Dropdown>
					</>
				),
				render: (_value, data: ReportDataType) => {
					const menuItems: MenuProps["items"] = [
						{
							key: "0",
							label: (
								<Link
									to={`/tests/${testId}/user-report/${data._id}`}
								>
									<Space>
										<FileDoneOutlined /> User Report
									</Space>
								</Link>
							),
						},
						{
							type: "divider",
						},
						quizData?.isLiveStreamEnabled
							? {
									key: "livestream",
									label: "Join Meeting",
									onClick: () => {
										window.open(
											`${AppConfig.quizServerURL}/meeting/${data._id}/${testId}`
										);
									},
									icon: (
										<UsergroupAddOutlined
											style={{ fontSize: "14px" }}
										/>
									),
								}
							: null,
						quizData?.isLiveStreamEnabled
							? { type: "divider" }
							: null,
						quizData?.isWebCamAllowed
							? {
									key: "1",
									label: (
										<Link
											to={`/tests/${testId}/user-report/${data._id}/proctoring?tabId=proctoring`}
											state={{ fromTestReport: true }}
										>
											<Space>
												<FileImageOutlined /> Proctoring
												Gallery
											</Space>
										</Link>
									),
								}
							: null,
						quizData?.isWebCamAllowed
							? {
									type: "divider",
								}
							: null,
						{
							key: "2",
							label: (
								<Space>
									<SettingOutlined /> Section Control
								</Space>
							),
							onClick: () => openSectionControlModal([data]),
						},
						hasResourcePermission(
							RoleResource.QUIZ,
							RoleAction.PROVIDE_EXTRA_TIME
						)
							? {
									type: "divider",
								}
							: null,
						hasResourcePermission(
							RoleResource.QUIZ,
							RoleAction.PROVIDE_EXTRA_TIME
						)
							? {
									key: "3",
									label: (
										<Space>
											<ClockCircleOutlined /> Add Extra
											Time
										</Space>
									),
									onClick: () => extraTimeModalOpen(data),
								}
							: null,
						{
							type: "divider",
						},
						{
							key: "4",
							label: (
								<Space>
									<UnorderedListOutlined /> Extra Time Logs
								</Space>
							),
							onClick: () => extraTimeLogsModal(data),
						},
						quizData?.isWebCamAllowed &&
						quizData?.isRecordingEnabled &&
						quizData.isAppOnly
							? {
									type: "divider",
								}
							: null,
						quizData?.isWebCamAllowed &&
						quizData?.isRecordingEnabled &&
						quizData.isAppOnly
							? {
									key: "downloadVideo",
									label: "Download Video",
									icon: (
										<VideoCameraOutlined
											style={{ fontSize: "14px" }}
										/>
									),
									onClick: () =>
										handleDownloadVideo(data._id),
								}
							: null,
						!data.completed &&
						hasResourcePermission(
							RoleResource.QUIZ,
							RoleAction.ALLOW_ALL_TEST_SUBMISSION
						)
							? {
									type: "divider",
								}
							: null,
						!data.completed &&
						hasResourcePermission(
							RoleResource.QUIZ,
							RoleAction.ALLOW_ALL_TEST_SUBMISSION
						)
							? {
									key: "finishTest",
									label: "Finish Test",
									icon: (
										<CheckCircleOutlined
											style={{ fontSize: "16px" }}
										/>
									),
									onClick: () => setFinishTestUsers([data]),
								}
							: null,
					];
					return (
						<Dropdown
							menu={{ items: menuItems }}
							trigger={["click"]}
							placement="bottomRight"
						>
							<Button
								type="text"
								icon={
									<MoreOutlined
										style={{ fontSize: "18px" }}
									/>
								}
							/>
						</Dropdown>
					);
				},
				fixed: "right",
				width: 80,
				key: "Actions",
				ellipsis: true,
				align: "center",
				onCell: () => ({ style: { zIndex: "0" } }),
				onHeaderCell: () => ({ style: { zIndex: "1" } }),
			},
		];
	}, [
		bulkItems,
		customFieldColumns,
		handleDownloadVideo,
		openSectionControlModal,
		quizData?._id,
		quizData?.isAppOnly,
		quizData?.isLiveStreamEnabled,
		quizData?.isRecordingEnabled,
		quizData?.isWebCamAllowed,
		reportState.currentPage,
		reportState.pageSize,
		selectedState.selectedRows,
		testId,
	]);

	const reportFilters: FilterMenuItem[] = useMemo(
		() => [
			{
				type: "dateRange",
				key: "dateRange",
				title: "Start Time:",
				value: reportState.dateRange,
			},
			{
				type: "slider",
				key: "slider",
				title: "Marks Obtained:",
				minValue: marksRange?.[0] ?? 0,
				maxMarks: marksRange?.[1] ?? 0,
				value: reportState.marksRange ?? marksRange ?? [0, 0],
			},
			{
				type: "tags",
				key: "tags",
				title: "Search multiple emails",
				placeholder: "eg. <EMAIL>,<EMAIL>,etc",
				value: reportState.multiEmailSearch ?? [],
			},
		],
		[
			marksRange,
			reportState.dateRange,
			reportState.marksRange,
			reportState.multiEmailSearch,
		]
	);

	const removeUser = async (email: string[], invited: boolean) => {
		modal.confirm({
			title: "Remove User",
			content: "Are you sure you want to remove this user?",
			okText: "Yes",
			cancelText: "No",
			onOk: async () => {
				try {
					let payload;
					if (invited) {
						payload = {
							emailId: email,
							isInvitee: 1,
							quizId: testId,
						};
					} else {
						payload = {
							emailId: email,
							quizId: testId,
						};
					}
					await quizClient.removeUser(payload);
					if (email.length > 1) {
						messageInstance?.success("Users removed successfully");
					} else {
						messageInstance?.success("User removed successfully");
					}
					// Clear selected state
					setSelectedState({
						selectedRowKeys: [],
						selectedRows: [],
					});
					setRefreshData(prev => prev + 1);
				} catch (error) {
					messageInstance?.error(error as string);
				}
			},
		});
	};

	const toggleIsValid = async (email: string, isValid: boolean) => {
		modal.confirm({
			title: `${isValid ? "Disable" : "Enable"} Link`,
			content: `Are you sure you want to ${isValid ? "disable" : "enable"} this user's link?`,
			okText: "Yes",
			cancelText: "No",
			onOk: async () => {
				try {
					await quizClient.toggleIsValid({
						emailId: email,
						isValid: isValid ? 0 : 1,
						quizId: testId,
					});
					if (isValid) {
						messageInstance?.success("Link disabled successfully");
					} else {
						messageInstance?.success("Link enabled successfully");
					}
					setRefreshData(prev => prev + 1);
				} catch (error) {
					messageInstance?.error(error as string);
				}
			},
		});
	};

	const invitedColumns: TableColumnsType<InvitedUsers> = useMemo(
		() => [
			{
				key: "Serial Number",
				// render: (_value, _record, index) => index + 1,
				render: (_value, _record, index) => {
					return (
						(reportState.currentPage - 1) * reportState.pageSize +
						index +
						1
					);
				},
				width: 50,
				fixed: "left",
				onCell: () => ({ style: { zIndex: "1" } }),
				onHeaderCell: () => ({ style: { zIndex: "1" } }),
			},
			{
				title: "Email",
				dataIndex: "email",
				key: "email",
				ellipsis: true,
			},
			{
				title: "Roll No",
				dataIndex: "enrollmentId",
				key: "rollNo",
				align: "center",
				render: value => {
					if (Number(value) === 0 || !value) return "-";
					return value;
				},
			},
			{
				title: "Status",
				dataIndex: "status",
				key: "status",
				align: "center",
				showSorterTooltip: false,
				sorter: (a, b) => a.status.localeCompare(b.status),
			},
			{
				title: "Expire Time",
				dataIndex: "validTill",
				key: "validTill",
				align: "center",
				render: value => {
					if (!value) return "-NA-";
					return timeDate(value);
				},
			},
			{
				title: "Attempt Status",
				dataIndex: "attemptStatus",
				key: "attemptStatus",
				align: "center",
				sorter: (a, b) => {
					if (a.attemptStatus && b.attemptStatus) {
						return 0;
					}
					if (a.attemptStatus) {
						return 1;
					}
					if (b.attemptStatus) {
						return -1;
					}
					return 0;
				},
				render: value => {
					return value ? "Attempted" : "Not Attempted";
				},
			},
			{
				title: "Actions",
				key: "Actions",
				align: "center",
				render: (_, record) => {
					const menuItems: MenuProps["items"] = [
						{
							key: "0",
							label: (
								<Space>
									<DeleteOutlined /> Remove User
								</Space>
							),
							onClick: () => removeUser([record.email], true),
						},
						{
							type: "divider",
						},
						{
							key: "1",
							label: (
								<Space>
									{record.isValid ? (
										<>
											<DisconnectOutlined /> Disable Link
										</>
									) : (
										<>
											<LinkOutlined /> Enable Link
										</>
									)}
								</Space>
							),
							onClick: () =>
								toggleIsValid(record.email, record.isValid),
						},
						{
							type: "divider",
						},
						{
							key: "2",
							label: (
								<Space>
									<CopyOutlined /> Copy Link
								</Space>
							),
							onClick: async () => {
								try {
									await navigator.clipboard.writeText(
										`${record.quizAttemptLink}`
									);
									messageInstance?.success(
										"Link copied successfully"
									);
								} catch {
									messageInstance?.error(
										"Unable to copy link"
									);
								}
							},
						},
						{
							type: "divider",
						},
						{
							key: "3",
							label: (
								<Space onClick={() => setUpdateTime(record)}>
									<CalendarOutlined /> Update Time
								</Space>
							),
						},
						{
							type: "divider",
						},
						{
							key: "4",
							label: (
								<Space
									onClick={() =>
										handleInviteSidebar([record.email])
									}
								>
									<MailOutlined /> Resend Invite
								</Space>
							),
						},
					];
					return (
						<Dropdown
							menu={{ items: menuItems }}
							trigger={["click"]}
							placement="bottomRight"
						>
							<Button type="text" icon={<MoreOutlined />} />
						</Dropdown>
					);
				},
			},
		],
		[handleInviteSidebar, reportState.currentPage, reportState.pageSize]
	);

	const allowedCandidates = useMemo<TableColumnsType<AllowedCandidates>>(
		() => [
			{
				key: "Serial Number",
				width: 50,
				render: (_value, _record, index) => {
					return (
						(reportState.currentPage - 1) * reportState.pageSize +
						index +
						1
					);
				},
				fixed: "left",
				onCell: () => ({ style: { zIndex: "1" } }),
				onHeaderCell: () => ({ style: { zIndex: "1" } }),
			},
			{
				title: "Email",
				dataIndex: "email",
				key: "email",
				ellipsis: true,
			},
			{
				title: "Roll No",
				dataIndex: "enrollmentId",
				key: "rollNo",
				align: "center",
				render: value => {
					if (Number(value) === 0 || !value) return "-";
					return value;
				},
			},
			{
				title: "Attempt Status",
				key: "attemptStatus",
				dataIndex: "attemptStatus",
				align: "center",
				sorter: (a, b) => {
					if (a.attemptStatus && b.attemptStatus) {
						return 0;
					}
					if (a.attemptStatus) {
						return 1;
					}
					if (b.attemptStatus) {
						return -1;
					}
					return 0;
				},
				render: (value: boolean) => {
					return value ? "Attempted" : "Not Attempted";
				},
			},
			{
				title: "Actions",
				key: "actions",
				render: (_, record) => {
					const menuItems: MenuProps["items"] = [
						{
							key: "0",
							label: (
								<Space>
									<DeleteOutlined /> Remove User
								</Space>
							),
							onClick: () => removeUser([record.email], false),
						},
					];
					return (
						<Dropdown
							menu={{ items: menuItems }}
							trigger={["click"]}
							placement="bottomRight"
						>
							<Button type="text" icon={<MoreOutlined />} />
						</Dropdown>
					);
				},
			},
		],
		[handleInviteSidebar, reportState.currentPage, reportState.pageSize]
	);

	const invitedFilters: FilterMenuItem[] = [
		{
			type: "select",
			key: "attemptType",
			title: "Attempted",
			items: [
				{
					value: 0,
					label: "All",
				},
				{
					value: 1,
					label: "Attempted",
				},
				{
					value: 2,
					label: "Not Attempted",
				},
			],
			value: reportState.attemptType,
		},
		{
			type: "tags",
			key: "tags",
			title: "Search multiple emails",
			placeholder: "eg. <EMAIL>,<EMAIL>,etc",
			value: reportState.multiEmailSearch ?? [],
		},
	];

	const allowedFilters: FilterMenuItem[] = [
		{
			type: "select",
			key: "attemptType",
			title: "Attempted",
			items: [
				{
					value: 0,
					label: "All",
				},
				{
					value: 1,
					label: "Attempted",
				},
				{
					value: 2,
					label: "Not Attempted",
				},
			],
			value: reportState.attemptType,
		},
		{
			type: "tags",
			key: "tags",
			title: "Search multiple emails",
			placeholder: "eg. <EMAIL>,<EMAIL>,etc",
			value: reportState.multiEmailSearch ?? [],
		},
	];

	useEffect(() => {
		setLoading(true);
		if (testId === undefined) {
			setLoading(false);
			return;
		}
		(async function () {
			try {
				const data = await quizClient.getTestReport(testId);
				updateReportData(
					data.quizReport,
					data.sampleData.invitees || [],
					data.sampleData?.emails || []
				);
				setQuizData(data.quizReport.quiz);
				setCanChangeTestMark(data.canChangeTestState);
			} catch (error) {
				messageInstance?.error(error as string);
			} finally {
				setLoading(false);
			}
		})();
	}, [refreshData]);

	const sendOTP = useCallback(async () => {
		try {
			if (!quizData?._id) {
				throw new Error("Something went wrong quizId is not available");
			}
			await quizClient.sendOtp(quizData?._id);
		} catch (error) {
			if (error instanceof Error) {
				messageInstance?.error(error.message);
				return;
			}
			if (typeof error === "string") {
				messageInstance?.error(error);
				return;
			}
			messageInstance?.error("Something went wrong");
		}
	}, [messageInstance, quizData?._id]);

	function updateReportData(
		data: QuizReport,
		invitedUsers: Invitee[],
		allowedEmails: AllowedCandidates[]
	) {
		const userData = data.userDataArray;
		const questions = data.questions;
		const finalData: ReportDataType[] = [];
		const customFieldLabelMap = Object.fromEntries(
			data.quiz?.quizUserDetails.map(
				field => [field.fieldLabel, field._id] as const
			) ?? []
		);

		let tMarks = 0;
		let minMarks = 0;

		questions.forEach(question => {
			tMarks += question.score;
		});

		const questionIdToScoreMap = new Map();
		if (data.quiz.poolQuestion) {
			questions.forEach(question => {
				questionIdToScoreMap.set(
					question._id.toString(),
					question.score
				);
			});
		}

		const totalMarksForQuiz = tMarks;

		const setOfUsersAttemptedTest = new Set();
		for (const currentUserData of userData) {
			let marksObtained = 0;
			let completed = false;
			let timeTaken = "";
			let startTime = "";
			let extraTime = 0;
			let suspiciousScore = 0;

			let totalMarks = totalMarksForQuiz;
			if (data.quiz.poolQuestion) {
				totalMarks = 0;
				(currentUserData.assignedQuestions ?? []).forEach(question => {
					totalMarks += questionIdToScoreMap.get(question) ?? 0;
				});
			}

			currentUserData.questionWiseScore.forEach(val => {
				if (typeof val === "number") {
					marksObtained += val;
				}
			});

			if (currentUserData.endTime) {
				completed = true;
			} else if (
				(currentUserData.startTime &&
					Number(currentUserData.startTime) == 0) ||
				!currentUserData.startTime
			) {
				completed = true;
			}

			if (shouldShowCompleted) {
				if (!completed) {
					if (currentUserData.startTime) {
						startTime = currentUserData.startTime;
						extraTime = currentUserData.extraTime;
					} else if (currentUserData.userSessions.length) {
						startTime =
							currentUserData.userSessions[
								currentUserData.userSessions.length - 1
							].startTime;
						extraTime =
							currentUserData.userSessions[
								currentUserData.userSessions.length - 1
							].extraTime;
					}
					if (startTime) {
						if (new Date(startTime).toString() !== "Invalid Date") {
							startTime = new Date(startTime)
								.getTime()
								.toString();
						}
						if (
							new Date(Number(startTime)).getTime() +
								extraTime +
								parseInt(data.quiz.quizTime.toString()) *
									1000 *
									60 +
								1 * 60 * 1000 <
							new Date().getTime()
						) {
							completed = true;
						}
					}
					if (completed) {
						currentUserData.timeTaken = (
							Number(data.quiz.quizTime) +
							currentUserData.extraTime / 60
						).toString();
						currentUserData.userSessions.forEach(element => {
							currentUserData.timeTaken += element.extraTime ?? 0;
						});
						currentUserData.endTime = new Date(
							Number(startTime) +
								Number(currentUserData.timeTaken) * 1000 * 60
						)
							.getTime()
							.toString();
						if (
							currentUserData.userSessions &&
							currentUserData.userSessions.length
						) {
							currentUserData.endTime = new Date(
								Number(startTime) +
									Number(
										currentUserData.sessionTime ??
											currentUserData.extraTime
									) *
										1000
							)
								.getTime()
								.toString();
							currentUserData.userSessions.push({
								endTime: currentUserData.endTime,
								startTime: currentUserData.startTime ?? "",
								_id: "",
								sessionTime: 0,
								extraTime: 0,
							});
						} else {
							currentUserData.startTime = startTime;
						}
					}
				}
			}
			if (
				currentUserData.userSessions &&
				currentUserData.userSessions[0] &&
				currentUserData.userSessions[0].startTime
			) {
				startTime = currentUserData.userSessions[0].startTime;
			}
			if (
				currentUserData.endTime &&
				currentUserData.startTime &&
				(!currentUserData.userSessions ||
					currentUserData.userSessions.length == 0)
			) {
				if (!currentUserData.idleTime) currentUserData.idleTime = 0;

				const msDifference =
					new Date(+currentUserData.endTime).getTime() -
						new Date(+currentUserData.startTime).getTime() || 0;

				const minutes = +(msDifference / 1000 / 60).toFixed(2) || 0;
				timeTaken = minutes.toString();

				currentUserData.endTime = new Date(+currentUserData.startTime)
					.getTime()
					.toString();
				currentUserData.endTime = (
					Number(currentUserData.endTime) +
					Number(timeTaken) * 60_000
				).toString();
			} else {
				if (
					currentUserData.userSessions &&
					!Array.isArray(currentUserData.userSessions)
				) {
					currentUserData.userSessions = JSON.parse(
						currentUserData.userSessions
					);
				}
				if (
					completed &&
					currentUserData.userSessions &&
					Array.isArray(currentUserData.userSessions) &&
					currentUserData.userSessions.length
				) {
					let totalsMins = 0;
					currentUserData.userSessions.forEach(val => {
						const TimeOne = +val.startTime;
						const Timetwo = +val.endTime;

						const dateOne = isNaN(TimeOne)
							? val.startTime
							: TimeOne;
						const dateTwo = isNaN(Timetwo) ? val.endTime : Timetwo;

						if (dateOne && dateTwo) {
							const msDifference =
								+(
									new Date(dateTwo).getTime() -
									new Date(dateOne).getTime()
								) || 0;
							const minutes =
								+(msDifference / 1000 / 60).toFixed(2) || 0;
							totalsMins += minutes;
						}
					});
					currentUserData.endTime =
						currentUserData.userSessions[
							currentUserData.userSessions.length - 1
						].endTime;
					timeTaken = totalsMins.toString();
				}
			}

			if (marksObtained > totalMarks) {
				totalMarks = marksObtained;
			}
			if (marksObtained < minMarks) {
				minMarks = marksObtained;
			}
			const customField = currentUserData.quizUserDetails.reduce(
				(
					acc: { [key: string]: string | undefined },
					currentValue: unknown
				): { [key: string]: string | undefined } => {
					const { fieldLabel, fieldValue } = currentValue as {
						fieldLabel: string;
						fieldValue: string;
					};
					acc[customFieldLabelMap[fieldLabel]] = fieldValue;
					return acc;
				},
				{}
			);

			suspiciousScore = calculateSuspiciousScore(currentUserData);

			finalData.push({
				...currentUserData,
				...(typeof customField === "object" && customField !== null
					? customField
					: {}),
				timeTaken: timeTaken,
				score: marksObtained,
				startTime: startTime,
				extraTime: extraTime,
				sessionTime: "",
				isAttempting: false,
				totalMarks: totalMarks,
				suspiciousScore: suspiciousScore,
				completed: completed,
				status: completed ? "Completed" : "InProgress",
			});
			setOfUsersAttemptedTest.add(currentUserData.email);
		}
		const finalInvitedUsers: InvitedUsers[] = invitedUsers.map(user => {
			return {
				...user,
				attemptStatus: setOfUsersAttemptedTest.has(user.email),
			};
		});
		setTableData(finalData.map(data => ({ ...data })));
		setFilteredReportData(finalData);
		setMarksRange([minMarks, totalMarksForQuiz]);
		setReportState(prev => {
			const toSet = { ...prev };
			if (toSet.marksRange == null) {
				toSet.marksRange = [minMarks, totalMarksForQuiz];
			}
			return toSet;
		});
		setInvitedUsers(finalInvitedUsers);
		setAllowedUsers(
			allowedEmails.map(user => {
				return {
					...user,
					attemptStatus: setOfUsersAttemptedTest.has(user.email),
				};
			})
		);

		return finalData;
	}

	const handleVisibility = (key: string) => {
		setReportState(prev => ({
			...prev,
			visibleColumns: prev.visibleColumns.includes(key)
				? prev.visibleColumns.filter(item => item !== key)
				: [...prev.visibleColumns, key],
		}));
	};

	const handleSearch = (value: string) => {
		setLoading(true);
		setReportState(prev => ({ ...prev, searchValue: value }));
		setLoading(false);
	};

	const onSubmit = (message: string, type: "success" | "error") => {
		if (type === "success") {
			messageInstance?.success(message);
		} else {
			messageInstance?.error(message);
		}
		setRefreshData(prev => prev + 1);
	};

	const handleFilterClose = () => {
		setShowFilters(false);
	};

	const handleFilterReset = () => {
		setReportState(prev => ({
			...prev,
			searchValue: "",
			marksRange: null,
			dateRange: ["", ""],
			currentPage: 1,
			multiEmailSearch: [],
		}));
	};

	const filteredColumns = reportState.questionWiseMarks
		? [
				...reportColumns.filter(
					col =>
						reportState.visibleColumns.includes(
							col.key as string
						) && col.key !== "Actions"
				),
				...Array.from(
					new Set(
						tableData.flatMap(user =>
							user.questionWiseScore.map((_, index) => index + 1)
						)
					)
				).map(index => ({
					title: `Q ${index}`,
					key: `#ques-key-index-${index}`,
					width: 70,
					dataIndex: `#ques-key-index-${index}`,
					render: (_: unknown, record: UserDataArray) => {
						const questionScore =
							record.questionWiseScore[index - 1] ?? "-";
						return questionScore;
					},
				})),
				...reportColumns.filter(col => col.key === "Actions"),
			]
		: reportColumns.filter(col =>
				reportState.visibleColumns.includes(col.key as string)
			);

	const [filteredReportData, setFilteredReportData] =
		useState<ReportDataType[]>(tableData);

	useEffect(() => {
		const filteredByMarksAndDate = tableData?.filter(item => {
			const score = item.score;
			const [minMarks, maxMarks] = reportState.marksRange ??
				marksRange ?? [0, 0];
			const [startDate, endDate] = reportState.dateRange;
			const isWithinMarksRange = score >= minMarks && score <= maxMarks;

			const itemStartTime = new Date(item.startTime).getTime();
			const filterStartTime = startDate
				? new Date(startDate).getTime()
				: null;
			const filterEndTime = endDate ? new Date(endDate).getTime() : null;

			const isWithinDateRange =
				(!filterStartTime || itemStartTime >= filterStartTime) &&
				(!filterEndTime || itemStartTime <= filterEndTime);

			return isWithinMarksRange && isWithinDateRange;
		});

		const filteredBySearch = filteredByMarksAndDate.filter(item => {
			const searchValue = reportState.searchValue.trim().toLowerCase();
			return (
				(item.displayname || "").toLowerCase().includes(searchValue) ||
				(item.email || "").toLowerCase().includes(searchValue) ||
				(item.enrollmentId || "").toLowerCase().includes(searchValue)
			);
		});

		if (reportState?.multiEmailSearch?.length > 0) {
			setFilteredReportData(
				filteredBySearch.filter(item =>
					reportState.multiEmailSearch.includes(item.email)
				)
			);
		} else {
			setFilteredReportData(filteredBySearch);
		}
	}, [
		reportState.marksRange,
		reportState.dateRange,
		reportState.searchValue,
		reportState.multiEmailSearch,
		tableData,
	]);

	useEffect(() => {
		if (quizData?.isPrivate && reportState.dropDownValue === 3) {
			setReportState(prev => ({
				...prev,
				dropDownValue: 1,
				currentPage: 1,
				pageSize: 10,
			}));
			setShowColumns(false);
			setShowFilters(false);
		}
	}, [quizData?.isPrivate]);

	const filteredInvitedUsers = useMemo(() => {
		const filteredData = reportState.searchValue
			? invitedUsers?.filter(item => {
					return (
						(item.email || "")
							.toLowerCase()
							.includes(reportState.searchValue) ||
						(item.enrollmentId || "")
							.toLowerCase()
							.includes(reportState.searchValue)
					);
				})
			: invitedUsers;

		const emailFiltered =
			reportState?.multiEmailSearch?.length > 0
				? filteredData.filter(item =>
						reportState.multiEmailSearch.includes(item.email)
					)
				: filteredData;

		if (reportState.attemptType === 0) {
			return emailFiltered;
		} else if (reportState.attemptType === 1) {
			return emailFiltered.filter(val => val.attemptStatus);
		} else {
			return emailFiltered.filter(val => !val.attemptStatus);
		}
	}, [
		reportState.searchValue,
		reportState.multiEmailSearch,
		reportState.attemptType,
		invitedUsers,
	]);

	const filteredAllowedUsers = useMemo(() => {
		const filteredData = reportState.searchValue
			? allowedUsers?.filter(item => {
					return (
						(item.email || "")
							.toLowerCase()
							.includes(reportState.searchValue) ||
						(item.enrollmentId || "")
							.toLowerCase()
							.includes(reportState.searchValue)
					);
				})
			: allowedUsers;

		const emailFiltered =
			reportState?.multiEmailSearch?.length > 0
				? filteredData.filter(item =>
						reportState.multiEmailSearch.includes(item.email)
					)
				: filteredData;

		if (reportState.attemptType === 0) {
			return emailFiltered;
		} else if (reportState.attemptType === 1) {
			return emailFiltered.filter(val => val.attemptStatus);
		} else {
			return emailFiltered.filter(val => !val.attemptStatus);
		}
	}, [
		reportState.searchValue,
		reportState.multiEmailSearch,
		reportState.attemptType,
		allowedUsers,
	]);

	const columnVisibilityMenu = () => {
		const finalColumns =
			AppConfig.platformOrganisation === PlatformOrganisation.CHITKARA
				? reportColumns.filter(col => col.key !== "Serial Number")
				: reportColumns.filter(
						col =>
							col.key !== "Serial Number" && col.key !== "rollNo"
					);
		return finalColumns.map(col => (
			<div
				key={col.key as string}
				style={{ marginBottom: "0.5em", padding: "0.25em" }}
			>
				<Checkbox
					checked={reportState.visibleColumns.includes(
						col.key as string
					)}
					onChange={() => handleVisibility(col.key as string)}
					disabled={fixedColumns.includes(col.key as string)}
					style={{ width: "100%" }}
				>
					{col.title as string}
				</Checkbox>
			</div>
		));
	};
	const setOfUserEmailsAttemptedTest = new Set(
		tableData.map(val => val.email)
	);

	const handleRowSelection = (
		selectedRowKeys: Key[],
		selectedRows: ReportDataType[] | Invitee[] | AllowedCandidates[]
	) => {
		setSelectedState({
			selectedRowKeys: [],
			selectedRows: [],
		});
		setSelectedState({
			selectedRowKeys,
			selectedRows,
		});
	};

	const handleQuestionWiseMarks = () => {
		setReportState(prev => ({
			...prev,
			questionWiseMarks: !prev.questionWiseMarks,
		}));
	};

	const handleDropdownChange = (value: number) => {
		setReportState({
			dropDownValue: value,
			dropDownOnly: true,
		});
		setShowColumns(false);
		setShowFilters(false);
		setSelectedState({
			selectedRowKeys: [],
			selectedRows: [],
		});
	};

	const handleFilterApply = (rowData: unknown) => {
		const data: TestReportFilters = {
			attemptType: 1,
			dateRange: ["", ""],
			slider: [0, 0],
			tags: [],
		};

		if (rowData && typeof rowData === "object") {
			if (
				"attemptType" in rowData &&
				typeof rowData.attemptType === "number"
			) {
				data.attemptType = rowData.attemptType;
			}
			if (
				"tags" in rowData &&
				Array.isArray(rowData.tags) &&
				rowData.tags.every((tag: unknown) => typeof tag === "string")
			) {
				data.tags = rowData.tags as string[];
			}
			if ("dateRange" in rowData && Array.isArray(rowData.dateRange)) {
				data.dateRange = rowData.dateRange.map(date => {
					if (dayjs.isDayjs(date)) {
						return date.toString();
					}
					return "";
				}) as [string, string];
			}
			if (
				"slider" in rowData &&
				Array.isArray(rowData.slider) &&
				rowData.slider.length === 2 &&
				rowData.slider.every((val: unknown) => typeof val === "number")
			) {
				data.slider = rowData.slider as [number, number];
				if (
					marksRange &&
					data.slider[0] === marksRange?.[0] &&
					data.slider[1] === marksRange[1]
				) {
					data.slider = null;
				}
			}
		}
		setReportState(prev => ({
			...prev,
			marksRange: data.slider ?? null,
			dateRange: data.dateRange ?? ["", ""],
			multiEmailSearch: data?.tags ?? [],
			attemptType: data.attemptType,
		}));
	};

	const exportCoReport = useCallback(
		async (skipDownload?: boolean) => {
			try {
				setDownloadingCoReport(true);
				const quizId = quizData?._id;
				if (!quizId) {
					return;
				}
				const data = await quizClient.getCoReport(quizId);
				if (data.length == 0) {
					message.error("There is no record to export.");
					return;
				}
				const dataToDownload = data.map(item => {
					return {
						"Test title": item.testTitle,
						"Question no.": item.questionNo.toString(),
						"Maximum Marks": item.maxMarks.toString(),
						"CO mapped": Array.isArray(item.coMapped)
							? (item?.coMapped ?? [])
									.map(ele => ele.toUpperCase())
									.join(", ")
							: item.coMapped.toUpperCase(),
						"BT(Bloom's Level)": Array.isArray(item.btLevel)
							? (item?.btLevel ?? []).join(", ")
							: item.btLevel,
					};
				});
				if (!skipDownload) {
					downloadExcel(
						`${quizData.title}-CO-Report`,
						dataToDownload
					);
					message.success("CO report downloaded");
				} else {
					return dataToDownload;
				}
			} catch (error) {
				console.error(error);
				if (error instanceof Error) {
					message.error(error.message);
					return;
				}
				if (typeof error === "string") {
					message.error(error);
					return;
				}
				message.error(
					"Unable to download CO report, please try again later."
				);
			} finally {
				setDownloadingCoReport(false);
			}
		},
		[quizData?._id, quizData?.title]
	);

	const exportToCSV = () => {
		const finalData: Array<object> = [];

		if (reportState.dropDownValue === 3) {
			if (allowedUsers.length <= 0) {
				messageInstance?.error("There is no record to export");
				return;
			}
			allowedUsers.forEach(val => {
				const isAttempted = setOfUserEmailsAttemptedTest.has(val.email);
				if (
					AppConfig.platformOrganisation ===
					PlatformOrganisation.CHITKARA
				) {
					finalData.push({
						Email: val.email,
						"Roll No": val.enrollmentId,
						"Attempt Status": isAttempted
							? "Attempted"
							: "Not Attempted",
					});
				} else {
					finalData.push({
						Email: val.email,
						"Attempt Status": isAttempted
							? "Attempted"
							: "Not Attempted",
					});
				}
			});
			const csvData = convertToCsv(finalData);
			downloadCSV(csvData, quizData?.title);
			return;
		}
		if (reportState.dropDownValue === 2) {
			if (invitedUsers.length <= 0) {
				messageInstance?.error("There is no record to export.");
				return;
			}
			invitedUsers.forEach(val => {
				const isAttempted = setOfUserEmailsAttemptedTest.has(val.email);
				if (
					AppConfig.platformOrganisation ===
					PlatformOrganisation.CHITKARA
				) {
					finalData.push({
						Email: val.email,
						Status: val.status,
						"Roll No": val.enrollmentId,
						Expired: convert(val.validTill || ""),
						"Attempt Status": isAttempted
							? "Attempted"
							: "Not Attempted",
					});
				} else {
					finalData.push({
						Email: val.email,
						Status: val.status,
						Expired: convert(val.validTill || ""),
						"Attempt Status": isAttempted
							? "Attempted"
							: "Not Attempted",
					});
				}
			});

			const csvData = convertToCsv(finalData);
			downloadCSV(csvData, quizData?.title);
			return;
		}
		if (tableData.length <= 0) {
			messageInstance?.error("There is no record to export");
			return;
		}
		downloadReport();
	};

	async function buildWorkbookFromSnapshot({
		quizData,
		reportColumns,
		reportState,
		tableData,
		skipDownload,
	}: {
		quizData: Quiz | undefined;
		reportColumns: TableColumnsType<ReportDataType>;
		reportState: ReportSearchState;
		tableData: Array<ReportDataType>;
		skipDownload: boolean;
	}) {
		try {
			if (!quizData) return;

			const allColumns = [...reportColumns];
			if (reportState.questionWiseMarks) {
				const questionColumns =
					quizData.quizContent.map((_, index: number) => ({
						title: `Q ${index + 1}`,
						key: `#ques-key-index-${index + 1}`,
						dataIndex: `#ques-key-index-${index + 1}`,
					})) || [];

				const actionColumnIndex = allColumns.findIndex(
					col => col.key === "Actions"
				);
				if (actionColumnIndex !== -1) {
					allColumns.splice(actionColumnIndex, 0, ...questionColumns);
				} else {
					allColumns.push(...questionColumns);
				}
			}

			const columnsToExport = allColumns
				.map(col => {
					let isInclude;
					if (
						AppConfig.platformOrganisation !==
						PlatformOrganisation.CHITKARA
					) {
						isInclude =
							col.key !== "Actions" &&
							col.key !== "Serial Number" &&
							col.key !== "rollNo";
					} else {
						isInclude =
							col.key !== "Actions" &&
							col.key !== "Serial Number";
					}
					if (!isInclude) return null;

					if (typeof col.title === "string") return col;

					if (isValidElement(col.title)) {
						const childText = col.title.props?.children;
						if (typeof childText === "string") {
							return { ...col, title: childText };
						}
					}
					return null;
				})
				.filter(Boolean);

			const formattedData = tableData.map(row => {
				const rowData: Record<string, unknown> = {};
				columnsToExport?.forEach(col => {
					if (!col?.title) return null;
					if (col.key?.toString().startsWith("#ques-key-index-")) {
						const questionIndex =
							parseInt(col.title?.toString().split(" ")[1]) - 1;
						rowData[col.title as string] =
							row.questionWiseScore?.[questionIndex] ?? "-";
						return;
					}
					if ("dataIndex" in col) {
						const dataIndex = col.dataIndex as keyof ReportDataType;
						let value = row[dataIndex];
						switch (dataIndex) {
							case "startTime":
							case "endTime":
								if (!value) return "-";
								if (/^\d+$/.test(value.toString())) {
									value = new Date(Number(value)).getTime();
								} else {
									value = new Date(value as string).getTime();
								}
								value = value ? timeDate(value) : "-";
								break;
							case "timeTaken": {
								const minutes = Math.floor(Number(value));
								const seconds = Math.round(
									(Number(value) - minutes) * 60
								);
								value = value
									? `${minutes} min ${seconds > 0 ? `${seconds} sec` : ""}`
									: "-";
								break;
							}
							case "score":
								value =
									typeof value === "number"
										? value.toString()
										: "-";
								break;
							case "status":
								value = value || "Not Attempted";
								break;
							case "displayname":
							case "email":
								value = value || "-";
								break;
							case "feedback":
								if (
									value &&
									typeof value === "object" &&
									"feedbackText" in value
								) {
									rowData["Feedback"] = value.feedbackText;
								}
								if (
									value &&
									typeof value === "object" &&
									"rating" in value
								) {
									value = FeedBackRating[value.rating];
								} else {
									value = "-";
								}
								break;
							case "qualified":
								value = value ? "Pass" : "Fail";
								break;
							case "loginCount": {
								if (!value) return "-";
								const platform = row.latestPlatform;
								value = value + ` (${row.currentIp}) `;
								if (platform && typeof platform === "string") {
									try {
										const parsedPlatform =
											JSON.parse(platform);
										value =
											value +
											`${parsedPlatform.os || "Unknown"}/${parsedPlatform.browser === "Electron" ? "App" : parsedPlatform.browser}`;
									} catch (error) {
										console.log(error);
									}
								}
								break;
							}
							default:
								value = value ?? "-";
						}
						rowData[col.title as string] = value;
					}
				});
				rowData["_id"] = row._id;
				return rowData;
			});

			const headerRow1: string[] = [];
			const headerRow2: string[] = [];
			const mergeRanges: XLSX.Range[] = [];

			const otherHeaders = columnsToExport.filter(col => {
				if (!col) return false;
				return !col.key?.toString().startsWith("#ques-key-index-");
			});

			otherHeaders.forEach(col => {
				if (!col?.title) return;
				headerRow1.push(col?.title?.toString());
				headerRow2.push("");
			});

			let currentCol = otherHeaders.length;
			const subHeaders: { key: string; title: string }[] = [];

			if (reportState.questionWiseMarks) {
				let index = 1;
				Object.values(quizData.quizSegments).forEach(segment => {
					const { title, count } = segment;
					const safeCount = Math.max(Number(count), 0);

					if (safeCount === 0) {
						headerRow1.push(title);
						headerRow2.push("");
						subHeaders.push({ key: "", title: "" });
						currentCol += 1;
					} else {
						headerRow1.push(
							title,
							...Array(safeCount - 1).fill("")
						);
						for (let i = 0; i < safeCount; i++) {
							const qTitle = `Q ${i + 1}`;
							headerRow2.push(qTitle);
							subHeaders.push({
								key: "Q " + index++,
								title: qTitle,
							});
						}
						if (safeCount > 1) {
							mergeRanges.push({
								s: { r: 0, c: currentCol },
								e: { r: 0, c: currentCol + safeCount - 1 },
							});
						}
						currentCol += safeCount;
					}
				});
			}

			headerRow1.push("Feedback Message");
			headerRow2.push("");
			headerRow1.push("Report");
			headerRow2.push("");

			const worksheetData = reportState.questionWiseMarks
				? [
						headerRow1,
						headerRow2,
						...formattedData.map(row => {
							const feedback = row["Feedback"];
							const dashboardLink = `${window.location.origin}/tests/${quizData?._id}/user-report/${row._id}`;
							return [
								...otherHeaders.map(col => {
									if (!col?.title) return;
									return col.title
										? row[
												col.title.toString()
											]?.toString() || ""
										: "";
								}),
								...subHeaders.map(({ key }) =>
									key ? (row[key]?.toString() ?? "-") : ""
								),
								feedback,
								{
									t: "s",
									v: "View",
									l: { Target: dashboardLink },
									s: {
										font: {
											underline: "single",
											color: { rgb: "0000FF" },
										},
									},
								},
							];
						}),
					]
				: [
						headerRow1,
						...formattedData.map(row => {
							const feedback = row["Feedback"];
							const dashboardLink = `${window.location.origin}/tests/${quizData?._id}/user-report/${row._id}`;
							return [
								...otherHeaders.map(col => {
									if (!col?.title) return;
									return col.title
										? row[
												col.title.toString()
											]?.toString() || ""
										: "";
								}),
								feedback,
								{
									t: "s",
									v: "View",
									l: { Target: dashboardLink },
									s: {
										font: {
											underline: "single",
											color: { rgb: "0000FF" },
										},
									},
								},
							];
						}),
					];

			const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);
			worksheet["!merges"] = mergeRanges;

			if (reportState.dropDownValue === 1) {
				if (!quizData?.isMarkedAsCheck && !skipDownload) {
					const markAsCheckedLink =
						await quizClient.getReportAproveLink(quizData._id);
					const indexWhereTheLinkWasInserted =
						worksheetData.length + 2;
					XLSX.utils.sheet_add_aoa(
						worksheet,
						[
							[
								{
									t: "s",
									v: "Mark quiz as checked.",
									l: { Target: markAsCheckedLink },
									s: {
										font: {
											bold: true,
											italic: true,
											underline: "single",
											color: { rgb: "003399" },
										},
									},
								},
							],
						],
						{ origin: `A${indexWhereTheLinkWasInserted}` }
					);
					if (!worksheet["!cols"]) worksheet["!cols"] = [];
					worksheet["!cols"][0] = { wch: 30 };
				}
			}

			const workbook = XLSX.utils.book_new();
			XLSX.utils.book_append_sheet(workbook, worksheet, "Test Report");

			const testTitle =
				quizData?.title?.replace(/[^a-zA-Z0-9]/g, "_") || "test_report";
			const fileName = `${testTitle}.xlsx`;

			if (skipDownload) return workbook;

			XLSX.writeFile(workbook, fileName);
			messageInstance?.success?.("Report downloaded successfully");
		} catch (error) {
			console.error("Error downloading report:", error);
			messageInstance?.error?.(
				"Failed to download result. Please try again."
			);
		}
	}

	const downloadReport = useCallback(
		async (skipDownload?: boolean) => {
			try {
				if (!quizData) return;

				const allColumns = [...reportColumns];
				if (reportState.questionWiseMarks) {
					const questionColumns =
						quizData.quizContent.map((_, index) => ({
							title: `Q ${index + 1}`,
							key: `#ques-key-index-${index + 1}`,
							dataIndex: `#ques-key-index-${index + 1}`,
						})) || [];

					const actionColumnIndex = allColumns.findIndex(
						col => col.key === "Actions"
					);

					if (actionColumnIndex !== -1) {
						allColumns.splice(
							actionColumnIndex,
							0,
							...questionColumns
						);
					} else {
						allColumns.push(...questionColumns);
					}
				}

				const columnsToExport = allColumns
					.map(col => {
						let isInclude;
						if (
							AppConfig.platformOrganisation !==
							PlatformOrganisation.CHITKARA
						) {
							isInclude =
								col.key !== "Actions" &&
								col.key !== "Serial Number" &&
								col.key !== "rollNo";
						} else {
							isInclude =
								col.key !== "Actions" &&
								col.key !== "Serial Number";
						}
						if (!isInclude) return null;

						if (typeof col.title === "string") return col;

						if (isValidElement(col.title)) {
							const childText = col.title.props?.children;
							if (typeof childText === "string") {
								return { ...col, title: childText };
							}
						}

						return null;
					})
					.filter(Boolean);

				const formattedData = tableData.map(row => {
					const rowData: Record<string, unknown> = {};
					columnsToExport?.forEach(col => {
						if (!col?.title) return null;
						if (
							col.key?.toString().startsWith("#ques-key-index-")
						) {
							const questionIndex =
								parseInt(col.title?.toString().split(" ")[1]) -
								1;
							rowData[col.title as string] =
								row.questionWiseScore?.[questionIndex] ?? "-";
							return;
						}

						if ("dataIndex" in col) {
							const dataIndex =
								col.dataIndex as keyof ReportDataType;
							let value = row[dataIndex];
							// Handle different data types here
							switch (dataIndex) {
								case "startTime":
								case "endTime":
									if (!value) return "-";
									if (/^\d+$/.test(value.toString())) {
										value = new Date(
											Number(value)
										).getTime();
									} else {
										value = new Date(
											value as string
										).getTime();
									}
									value = value ? timeDate(value) : "-";
									break;
								case "timeTaken": {
									const minutes = Math.floor(Number(value));
									const seconds = Math.round(
										(Number(value) - minutes) * 60
									);
									value = value
										? `${minutes} min ${seconds > 0 ? `${seconds} sec` : ""}`
										: "-";
									break;
								}
								case "score":
									value =
										typeof value === "number"
											? value.toString()
											: "-";
									break;
								case "status":
									value = value || "Not Attempted";
									break;
								case "displayname":
									value = value || "-";
									break;
								case "email":
									value = value || "-";
									break;
								case "feedback":
									if (
										value &&
										typeof value === "object" &&
										"feedbackText" in value
									) {
										rowData["Feedback"] =
											value.feedbackText;
									}
									if (
										value &&
										typeof value === "object" &&
										"rating" in value
									) {
										value = FeedBackRating[value.rating];
									} else {
										value = "-";
									}
									break;

								case "qualified":
									value = value ? "Pass" : "Fail";
									break;

								case "loginCount": {
									if (!value) return "-";
									const platform = row.latestPlatform;
									value = value + ` (${row.currentIp}) `;

									if (
										platform &&
										typeof platform === "string"
									) {
										try {
											const parsedPlatform =
												JSON.parse(platform);
											value =
												value +
												`${parsedPlatform.os || "Unknown"}/${parsedPlatform.browser === "Electron" ? "App" : parsedPlatform.browser}`;
										} catch (error) {
											console.log(error);
										}
									}
									break;
								}

								default:
									value = value ?? "-";
							}

							rowData[col.title as string] = value;
						}
					});
					rowData["_id"] = row._id;
					return rowData;
				});

				const headerRow1: string[] = [];
				const headerRow2: string[] = [];
				const mergeRanges: XLSX.Range[] = [];

				const otherHeaders = columnsToExport.filter(col => {
					if (!col) return false;
					return !col.key?.toString().startsWith("#ques-key-index-");
				});

				otherHeaders.forEach(col => {
					if (!col?.title) return;
					headerRow1.push(col?.title?.toString());
					headerRow2.push(""); // leave sub-header blank
				});

				let currentCol = otherHeaders.length;
				const subHeaders: {
					key: string;
					title: string;
				}[] = [];

				if (reportState.questionWiseMarks) {
					let index = 1;
					Object.values(quizData.quizSegments).forEach(segment => {
						const { title, count } = segment;
						const safeCount = Math.max(Number(count), 0); // Ensure no negative count

						if (safeCount === 0) {
							headerRow1.push(title);
							headerRow2.push("");
							subHeaders.push({
								key: "",
								title: "",
							});
							currentCol += 1;
						} else {
							headerRow1.push(
								title,
								...Array(safeCount - 1).fill("")
							);

							for (let i = 0; i < safeCount; i++) {
								const qTitle = `Q ${i + 1}`;
								headerRow2.push(qTitle);
								subHeaders.push({
									key: "Q " + index++,
									title: qTitle,
								});
							}

							if (safeCount > 1) {
								mergeRanges.push({
									s: { r: 0, c: currentCol },
									e: { r: 0, c: currentCol + safeCount - 1 },
								});
							}
							currentCol += safeCount;
						}
					});
				}
				headerRow1.push("Feedback Message");
				headerRow2.push("");
				headerRow1.push("Report");
				headerRow2.push("");
				const worksheetData = reportState.questionWiseMarks
					? [
							headerRow1,
							headerRow2,
							...formattedData.map(row => {
								const feedback = row["Feedback"];
								const dashboardLink = `${window.location.origin}/tests/${quizData?._id}/user-report/${row._id}`;
								return [
									...otherHeaders.map(col => {
										if (!col?.title) return;
										return col.title
											? row[
													col.title.toString()
												]?.toString() || ""
											: "";
									}),
									...subHeaders.map(({ key }) => {
										return key
											? (row[key]?.toString() ?? "-")
											: "";
									}),
									feedback,
									{
										t: "s",
										v: "View",
										l: { Target: dashboardLink },
										s: {
											font: {
												underline: "single",
												color: { rgb: "0000FF" },
											},
										},
									},
								];
							}),
						]
					: [
							headerRow1,
							...formattedData.map(row => {
								const feedback = row["Feedback"];
								const dashboardLink = `${window.location.origin}/tests/${quizData?._id}/user-report/${row._id}`;
								return [
									...otherHeaders.map(col => {
										if (!col?.title) return;
										return col.title
											? row[
													col.title.toString()
												]?.toString() || ""
											: "";
									}),
									feedback,
									{
										t: "s",
										v: "View",
										l: { Target: dashboardLink },
										s: {
											font: {
												underline: "single",
												color: { rgb: "0000FF" },
											},
										},
									},
								];
							}),
						];

				const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);
				worksheet["!merges"] = mergeRanges;

				if (reportState.dropDownValue === 1) {
					if (!quizData?.isMarkedAsCheck && !skipDownload) {
						const markAsCheckedLink =
							await quizClient.getReportAproveLink(quizData._id);
						const indexWhereTheLinkWasInserted =
							worksheetData.length + 2;

						XLSX.utils.sheet_add_aoa(
							worksheet,
							[
								[
									{
										t: "s",
										v: "Mark quiz as checked.",
										l: { Target: markAsCheckedLink },
										s: {
											font: {
												bold: true,
												italic: true,
												underline: "single",
												color: { rgb: "003399" },
											},
										},
									},
								],
							],
							{ origin: `A${indexWhereTheLinkWasInserted}` }
						);

						if (!worksheet["!cols"]) worksheet["!cols"] = [];
						worksheet["!cols"][0] = { wch: 30 };
					}
				}
				const workbook = XLSX.utils.book_new();
				XLSX.utils.book_append_sheet(
					workbook,
					worksheet,
					"Test Report"
				);

				const testTitle =
					quizData?.title?.replace(/[^a-zA-Z0-9]/g, "_") ||
					"test_report";
				const fileName = `${testTitle}.xlsx`;

				if (skipDownload) {
					return workbook;
				}

				XLSX.writeFile(workbook, fileName);
				messageInstance?.success("Report downloaded successfully");
			} catch (error) {
				console.error("Error downloading report:", error);
				messageInstance?.error(
					"Failed to download result. Please try again."
				);
			}
		},
		[
			messageInstance,
			quizData,
			reportColumns,
			reportState.dropDownValue,
			reportState.questionWiseMarks,
			tableData,
		]
	);

	const onSubmitUpdateTime = async (value: { expireTime: string }) => {
		if (!updateTime?.email || !quizData?._id) return;
		try {
			await quizClient.updateExpireTime(
				updateTime?.email,
				quizData?._id,
				value.expireTime
			);
			messageInstance?.success("Expire time updated successfully");
			setUpdateTime(null);
			setRefreshData(prev => prev + 1);
		} catch (error) {
			if (error instanceof Error) {
				messageInstance?.error(error.message);
				return;
			}
			if (typeof error === "string") {
				messageInstance?.error(error);
				return;
			}
			messageInstance?.error("Something went wrong");
		}
	};

	const openMarkQuizAsChecked = useCallback(async () => {
		if (markQuizAsCheckedState.visible || !quizData?._id) {
			return false;
		}
		setMarkQuizAsCheckedState(prev => ({
			...prev,
			visible: true,
			currentState: -1,
		}));
	}, [markQuizAsCheckedState.visible, quizData?._id]);

	const handleCloseMarkedQuiz = useCallback(() => {
		if (
			markQuizAsCheckedState.currentState === -1 ||
			markQuizAsCheckedState.error ||
			markQuizAsCheckedState.currentState === 3
		) {
			if (jwtToken) {
				navigate("/");
			}
			setMarkQuizAsCheckedState(_ => ({
				currentState: -1,
				visible: false,
			}));
			return;
		}
	}, [
		jwtToken,
		markQuizAsCheckedState.currentState,
		markQuizAsCheckedState.error,
		navigate,
	]);

	const markQuizAsChecked = async () => {
		console.log("Marked: ", markQuizAsCheckedState, quizData?._id);
		if (!markQuizAsCheckedState.visible || !quizData?._id) {
			return false;
		}
		try {
			setMarkQuizAsCheckedState(prev => ({
				...prev,
				visible: true,
				currentState: 0,
			}));
			const jwt = await quizClient.getReportAproveLink(quizData?._id);
			if (!jwt) {
				setMarkQuizAsCheckedState(prev => ({
					...prev,
					error: `Oh, you are not allowed to mark this quiz as checked!`,
				}));
				return;
			}

			setMarkQuizAsCheckedState(prev => ({
				...prev,
				jwt,
				currentState: 1,
			}));
		} catch (error) {
			if (error instanceof Error) {
				setMarkQuizAsCheckedState(prev => ({
					...prev,
					error: error?.message,
				}));
				messageInstance?.error(error.message);
				return;
			}
			setMarkQuizAsCheckedState(prev => ({
				...prev,
				error: "Something went wrong",
			}));
		}
	};

	const handleReportUpload = useCallback(async () => {
		try {
			const jwt = markQuizAsCheckedState.jwt;
			if (!jwt || !quizData?._id) {
				return;
			}
			setMarkQuizAsCheckedState(prev => ({
				...prev,
				currentState: 2,
			}));
			const data = await quizClient.getTestReport(quizData?._id);
			const tableData = updateReportData(
				data.quizReport,
				data.sampleData.invitees || [],
				data.sampleData?.emails || []
			);
			setQuizData(data.quizReport.quiz);
			setCanChangeTestMark(data.canChangeTestState);

			const formData = new FormData();
			const file: XLSX.WorkBook | undefined =
				await buildWorkbookFromSnapshot({
					quizData: quizData,
					reportColumns: reportColumns,
					reportState: reportState,
					skipDownload: true,
					tableData: tableData,
				});
			if (file) {
				const reportBlob = new Blob(
					[
						XLSX.write(file, {
							bookType: "xlsx",
							type: "array",
						}),
					],
					{
						type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
					}
				);
				formData.append("report", reportBlob, "report.xlsx");
			}
			if (
				AppConfig.platformOrganisation === PlatformOrganisation.CHITKARA
			) {
				const coReportData = await exportCoReport(true);
				if (coReportData) {
					const coWorkbook = XLSX.utils.book_new();
					const coWorksheet = XLSX.utils.json_to_sheet(coReportData);
					XLSX.utils.book_append_sheet(
						coWorkbook,
						coWorksheet,
						"CO Report"
					);

					const coReportBlob = new Blob(
						[
							XLSX.write(coWorkbook, {
								bookType: "xlsx",
								type: "array",
							}),
						],
						{
							type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
						}
					);
					formData.append("coReport", coReportBlob, "coReport.xlsx");
				} else {
					return;
				}
			}
			const res = await quizClient.markQuizAsChecked(
				quizData?._id,
				formData
			);
			if ("error" in res) {
				throw res.error;
			}
			setRefreshData(prev => prev + 1);
			setMarkQuizAsCheckedState(_ => ({
				currentState: 3,
				visible: true,
			}));
		} catch (error) {
			if (error instanceof Error) {
				setMarkQuizAsCheckedState(prev => ({
					...prev,
					error: error.message,
				}));
				return;
			}
			setMarkQuizAsCheckedState(prev => ({
				...prev,
				error: "Something went wrong",
			}));
		}
	}, [
		buildWorkbookFromSnapshot,
		exportCoReport,
		markQuizAsCheckedState.jwt,
		quizData,
		reportColumns,
		reportState,
		updateReportData,
	]);

	const markReportAsUnresolved = async () => {
		try {
			if (!quizData?._id || filteredReportData.length === 0) return;
			await quizClient.markReportAsUnresolved(quizData?._id);
			messageInstance?.success("This quiz is marked as unresolved");
			setRefreshData(prev => prev + 1);
		} catch (error) {
			console.log(error);
			messageInstance?.error(error as string);
		}
	};

	const {
		token: { colorBgContainer },
	} = theme.useToken();

	const filterCount = useMemo(() => {
		let count = 0;
		if (reportState.dropDownValue === 1) {
			count += reportState.dateRange[0] ? 1 : 0;
			count += reportState.dateRange[1] ? 1 : 0;
			if (marksRange && reportState.marksRange) {
				if (reportState.marksRange[0] !== marksRange[0]) {
					count += 1;
				}
				if (reportState.marksRange[1] !== marksRange[1]) {
					count += 1;
				}
			}
			count += reportState.multiEmailSearch?.length > 0 ? 1 : 0;
		} else {
			count += reportState.attemptType !== 0 ? 1 : 0;
			count += reportState.multiEmailSearch?.length > 0 ? 1 : 0;
		}
		return count;
	}, [reportState, marksRange]);

	const handleReportDownload = async (id: string) => {
		try {
			await quizClient.downloadTestReport(id);
			exportToCSV();
		} catch (error) {
			messageInstance?.error("Something went wrong");
			console.log(error);
		}
	};

	return (
		<>
			{modalContextHolder}
			<MarkQuizAsResolved
				quizTitle={quizData?.title ?? ""}
				error={markQuizAsCheckedState.error}
				quizId={quizData?._id ?? ""}
				currentState={markQuizAsCheckedState.currentState}
				visible={markQuizAsCheckedState.visible}
				sendOtp={sendOTP}
				onOk={markQuizAsChecked}
				onCancel={handleCloseMarkedQuiz}
				onTestFinish={handleReportUpload}
			/>
			<Layout style={{ padding: "0.5em", height: "100%" }}>
				<Sider
					collapsed={!showFilters}
					collapsedWidth={0}
					theme="light"
					style={{
						overflow: "hidden auto",
						maxHeight: "100vh",
					}}
					width="18%"
				>
					<FilterMenu
						componentsData={
							reportState.dropDownValue === 1
								? reportFilters
								: reportState.dropDownValue === 2
									? invitedFilters
									: allowedFilters
						}
						onChange={handleFilterApply}
						onReset={handleFilterReset}
						onClose={handleFilterClose}
					></FilterMenu>
				</Sider>
				<Layout>
					<Header
						style={{
							backgroundColor: colorBgContainer,
							padding: "0 0.5em 0 0.5em",
						}}
					>
						<Flex
							align="center"
							justify="space-between"
							style={{ height: "100%" }}
							gap={20}
						>
							<Flex gap={10}>
								<Badge size="small" count={filterCount}>
									<Button
										type={
											!showFilters ? "default" : "primary"
										}
										icon={<FilterIcon size={14} />}
										onClick={() =>
											setShowFilters(prev => !prev)
										}
									></Button>
								</Badge>
								<Select
									options={newOptions}
									size="middle"
									value={reportState.dropDownValue}
									style={{ width: "300px" }}
									onChange={handleDropdownChange}
								></Select>
								<Search
									width="400px"
									size="middle"
									placeholder="Search here..."
									value={reportState.searchValue}
									allowClear
									onChange={e => handleSearch(e.target.value)}
									loading={loading}
								/>
							</Flex>
							<Flex gap={10}>
								{reportState.dropDownValue !== 1 &&
									selectedState.selectedRows.length !== 0 && (
										<>
											{reportState.dropDownValue !==
												3 && (
												<Button
													icon={<CalendarOutlined />}
													onClick={() =>
														handleInviteSidebar(
															selectedState.selectedRows.map(
																row => row.email
															)
														)
													}
												>
													Update Time
												</Button>
											)}
											<Button
												icon={<DeleteOutlined />}
												onClick={() =>
													removeUser(
														selectedState.selectedRows.map(
															row => row.email
														),
														reportState.dropDownValue ===
															2
															? true
															: false
													)
												}
											>
												Remove Users
											</Button>
										</>
									)}
								{canChangeTestMark &&
									reportState.dropDownValue === 1 &&
									filteredReportData.length !== 0 && (
										<Button
											icon={<MailOutlined />}
											onClick={() =>
												quizData?.isMarkedAsCheck
													? markReportAsUnresolved()
													: openMarkQuizAsChecked()
											}
										>
											{quizData?.isMarkedAsCheck
												? "Mark quiz as unchecked"
												: "Mark quiz as checked"}
										</Button>
									)}

								{PlatformOrganisation.CQ ===
								AppConfig.platformOrganisation ? (
									<Button
										loading={downloadingCoReport}
										onClick={() =>
											handleReportDownload(testId)
										}
									>
										Download Result
									</Button>
								) : (
									<Dropdown.Button
										loading={downloadingCoReport}
										icon={<DownOutlined />}
										menu={{
											items: [
												{
													label: "Download CO Report",
													key: "1",
													onClick: () =>
														exportCoReport(),
												},
											],
										}}
										onClick={exportToCSV}
									>
										Download Result
									</Dropdown.Button>
								)}

								{reportState.dropDownValue === 1 && (
									<>
										<Button
											title="Invite Candidates"
											onClick={handleQuestionWiseMarks}
											icon={
												<Switch
													size="small"
													checked={
														reportState.questionWiseMarks
													}
												/>
											}
										>
											Question Wise Marks
										</Button>
										<Button
											onClick={() =>
												setShowColumns(prev => !prev)
											}
											type={
												!showColumns
													? "default"
													: "primary"
											}
										>
											Columns
										</Button>
									</>
								)}
							</Flex>
						</Flex>
					</Header>
					<Content>
						<div
							style={{
								height: "100vh",
								overflow: "auto",
								backgroundColor: "#FBFBFB",
							}}
						>
							{reportState.dropDownValue === 1 && (
								<DataTable<ReportDataType>
									data={filteredReportData}
									columns={filteredColumns}
									loading={loading}
									enableRowSelection={true}
									onRowSelection={handleRowSelection}
									selectedKeys={selectedState.selectedRowKeys}
									scroll={{
										x: 1600,
										y: "calc(100vh - 420px)",
									}}
									pagination={{
										current: reportState.currentPage,
										pageSize: reportState.pageSize,
										onChange: (current, pageSize) =>
											setReportState(prev => ({
												...prev,
												currentPage: current,
												pageSize,
											})),
										style: {
											position: "sticky",
											bottom: "0px",
											padding: "0.75em",
											backgroundColor: "white",
											margin: 0,
											zIndex: "2",
											width: "100%",
										},
										position: ["bottomCenter"],
										showSizeChanger: true,
									}}
								/>
							)}
							{reportState.dropDownValue === 2 && (
								<DataTable<InvitedUsers>
									data={filteredInvitedUsers}
									columns={invitedColumns}
									loading={loading}
									enableRowSelection={true}
									selectedKeys={selectedState.selectedRowKeys}
									onRowSelection={handleRowSelection}
									scroll={{
										x: 1600,
										y: "calc(100vh - 420px)",
									}}
									pagination={{
										current: reportState.currentPage,
										pageSize: reportState.pageSize,
										onChange: (current, pageSize) =>
											setReportState(prev => ({
												...prev,
												currentPage: current,
												pageSize: pageSize,
											})),
										style: {
											position: "sticky",
											bottom: "0px",
											padding: "0.75em",
											backgroundColor: "white",
											margin: 0,
											zIndex: "2",
											width: "100%",
										},
										position: ["bottomCenter"],
										showSizeChanger: true,
									}}
								/>
							)}
							{reportState.dropDownValue === 3 && (
								<DataTable<AllowedCandidates>
									data={filteredAllowedUsers}
									columns={allowedCandidates}
									loading={loading}
									enableRowSelection={true}
									selectedKeys={selectedState.selectedRowKeys}
									onRowSelection={handleRowSelection}
									scroll={{
										x: 1600,
										y: "calc(100vh - 420px)",
									}}
									pagination={{
										current: reportState.currentPage,
										pageSize: reportState.pageSize,
										onChange: (current, pageSize) =>
											setReportState(prev => ({
												...prev,
												currentPage: current,
												pageSize: pageSize,
											})),
										style: {
											position: "sticky",
											bottom: "0px",
											padding: "0.75em",
											backgroundColor: "white",
											margin: 0,
											zIndex: "2",
											width: "100%",
										},
										position: ["bottomCenter"],
										showSizeChanger: true,
									}}
								/>
							)}
						</div>
					</Content>
				</Layout>
				<Sider
					collapsed={!showColumns}
					collapsedWidth={0}
					theme="light"
					style={{
						overflow: "hidden auto",
						maxHeight: "100vh",
					}}
					width="18%"
				>
					<Card
						styles={{
							header: {
								position: "sticky",
								top: 0,
								zIndex: 1,
								background: "white",
							},
						}}
						title="Columns List"
						extra={
							<Button
								type="text"
								icon={<CloseOutlined />}
								onClick={() => setShowColumns(false)}
							></Button>
						}
					>
						{columnVisibilityMenu()}
					</Card>
				</Sider>
			</Layout>
			{
				<SectionControlModal
					onClose={closeSectionControlModal}
					sectionControlData={sectionControlData}
					quizId={testId}
					onSubmit={onSubmit}
				/>
			}
			{
				<ExtraTime
					onClose={closeExtraTimeModal}
					users={extraTimeModalData}
					quizId={testId}
					quizTime={Number(quizData?.quizTime)}
					onSubmit={onSubmit}
				/>
			}
			{console.log("MarkQuiz As Resolved: ", markQuizAsCheckedState)}
			{finishTestUsers.length && (
				<FinishTest
					users={finishTestUsers}
					onClose={() => setFinishTestUsers([])}
					quizId={testId}
					sendOtp={sendOTP}
					onSubmit={onSubmit}
				/>
			)}
			{extraTimeLogsModalData && (
				<LogsModal
					title="Extra Time Logs"
					name={extraTimeLogsModalData.displayname}
					email={extraTimeLogsModalData.email}
					onClose={closeExtraTimeLogsModal}
					extraTimeLogsModalData={extraTimeLogsModalData}
				>
					<ExtraTimeLogs
						testId={quizData?._id}
						userId={extraTimeLogsModalData._id}
						scroll={{ y: 200 }}
					/>
				</LogsModal>
			)}

			{isInviteSideBarOpen && (
				<InviteSidebar
					testTitle={quizData?.title ?? "test"}
					userData={selectedInvitee}
					onClose={closeInviteSideBar}
					isInviteSideBarOpen={isInviteSideBarOpen}
					startTime={
						quizData?.startTime
							? dayjs(quizData?.startTime)
							: undefined
					}
					endTime={
						quizData?.endTime ? dayjs(quizData?.endTime) : undefined
					}
					entryStopTime={
						quizData?.entryStopTime
							? dayjs(quizData?.entryStopTime)
							: undefined
					}
				/>
			)}

			{updateTime !== null && (
				<UpdateTime
					data={updateTime}
					onClose={() => setUpdateTime(null)}
					onSubmit={onSubmitUpdateTime}
					endTime={
						quizData?.endTime ? dayjs(quizData?.endTime) : undefined
					}
				/>
			)}
		</>
	);
}

const convertToCsv = (data: Array<object>) => {
	const csvRows = [];
	const header = Object.keys(data[0]);
	csvRows.push(header.join(","));
	for (const row of data) {
		csvRows.push(Object.values(row).join(","));
	}
	return csvRows.join("\n");
};

function downloadCSV(data: string, testTitle: string | undefined) {
	const blob = new Blob([data], { type: "text/csv" });
	const url = window.URL.createObjectURL(blob);
	const a = document.createElement("a");
	a.setAttribute("hidden", "");
	a.setAttribute("href", url);
	const fileName = testTitle || "report";
	a.setAttribute("download", `${fileName}.csv`);
	document.body.appendChild(a);
	a.click();
	document.body.removeChild(a);
}

function downloadExcel(
	fileName: string,
	data: Array<Record<string, string>>,
	config?: { width: number }
) {
	const workbook = XLSX.utils.book_new();
	const worksheet = XLSX.utils.json_to_sheet(data);
	XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");
	const columnWidths = Object.keys(data[0]).map(() => ({
		wch: config?.width ?? 20,
	}));
	worksheet["!cols"] = columnWidths;
	XLSX.writeFile(workbook, `${fileName}.xlsx`);
}

function convert(data: string | number): string {
	if (!data) {
		return "-NA-";
	}
	const timeDate = new Date(
		typeof data === "string" && !isNaN(Number(data)) ? Number(data) : data
	);
	if (isNaN(timeDate.getTime())) {
		return "-NA-";
	}

	let hours: number | string = timeDate.getHours();
	let minutes: number | string = timeDate.getMinutes();
	hours = hours < 10 ? "0" + hours : hours;
	minutes = minutes < 10 ? "0" + minutes : minutes;
	const strTime = hours + ":" + minutes;
	let date: number | string = timeDate.getDate();
	if (date < 10) {
		date = `0${timeDate.getDate()}`;
	}
	let month: number | string = timeDate.getMonth() + 1;
	if (month < 10) {
		month = `0${timeDate.getMonth() + 1}`;
	}
	return date + "-" + month + "-" + timeDate.getFullYear() + " " + strTime;
}

function calculateSuspiciousScore(data: UserDataArray) {
	let score = data.illegalObjectDetected * aiProctorScore.illegalObject;
	score += data.userExitCount * aiProctorScore.userExit;
	score += data.userEnterCount * aiProctorScore.userEnter;
	score += data.multipleUserDetected * aiProctorScore.multipleUser;
	return Math.floor(score);
}
