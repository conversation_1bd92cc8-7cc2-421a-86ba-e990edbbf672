import { Modal, Steps } from "antd";
import { useMemo } from "react";
import { markQuizAsResolvedStaged } from "../constants/test";
import { FinishTestModel } from "./finishTest";

interface MarkQuizAsResolvedProps {
	quizTitle: string;
	quizId: string;
	visible: boolean;
	error?: string;
	currentState: number;
	sendOtp: () => Promise<void>;
	onOk: () => Promise<false | undefined>;
	onCancel: () => void;
	onTestFinish: () => Promise<void>;
}

export const MarkQuizAsResolved = (props: MarkQuizAsResolvedProps) => {
	const {
		quizTitle,
		quizId,
		visible,
		error,
		currentState,
		sendOtp,
		onOk,
		onCancel,
		onTestFinish,
	} = props;

	const markQuizAsResolvedSteps = useMemo(() => {
		const steps = structuredClone(markQuizAsResolvedStaged);
		for (let i = 0; i < currentState; i++) {
			if (steps[i]) steps[i].status = "finish";
		}
		if (steps[currentState]) {
			steps[currentState].status = error ? "error" : "process";
		}
		for (let i = currentState + 1; i < steps.length; i++) {
			if (steps[i]) steps[i].status = "wait";
		}
		return steps;
	}, [currentState, error]);

	const isLoading = useMemo(() => {
		if (currentState == -1 || error || currentState == 3) {
			return false;
		}
		return true;
	}, [currentState, error]);

	return (
		<Modal
			title={`Mark ${quizTitle} as Checked`}
			open={visible}
			okText="Submit"
			onOk={onOk}
			okButtonProps={{
				loading: isLoading,
				disabled: isLoading || currentState === 3,
			}}
			cancelButtonProps={{
				loading: isLoading,
				disabled: isLoading,
			}}
			onCancel={onCancel}
		>
			<FinishTestModel
				quizId={quizId}
				visible={currentState === 1}
				sendOtp={sendOtp}
				onSubmit={onTestFinish}
			/>
			<Steps
				direction="vertical"
				current={currentState}
				items={markQuizAsResolvedSteps}
			/>
		</Modal>
	);
};
