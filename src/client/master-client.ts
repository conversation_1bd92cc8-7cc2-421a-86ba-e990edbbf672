import axios, {
	AxiosError,
	<PERSON><PERSON>os<PERSON>nstance,
	AxiosRequestConfig,
	AxiosResponse,
} from "axios";

export class MasterServiceClient {
	private __mainApi: AxiosInstance;
	#origin;
	constructor(url: string) {
		this.#origin = url;
		this.__mainApi = axios.create({
			withCredentials: false,
			headers: {
				"Content-Type": "application/json",
				"X-Requested-From": "app-0.0.1",
			},
			baseURL: url,
		});
	}

	get origin() {
		return this.#origin;
	}

	protected async fetch<T, D = unknown>(
		config: AxiosRequestConfig<D> & { data?: D }
	) {
		try {
			const response: AxiosResponse<T> = await this.__mainApi(config);
			return response;
		} catch (error) {
			if (error instanceof AxiosError === false) {
				throw error;
			}
			if (error.status === 401) {
				// window.location.reload();
			}
			throw error;
		}
	}

	async checkVideoStatus(token: string) {
		try {
			const res = await this.fetch<
				| {
						status: string;
						presignedGetURL?: string;
				  }
				| { error: string }
			>({
				method: "GET",
				url: `/getVideo/${token}`,
			});
			return res.data;
		} catch (ex) {
			console.log(ex);
			throw "Unable to get video status";
		}
	}
}
