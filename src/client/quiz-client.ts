import axios, {
	AxiosError,
	AxiosInstance,
	AxiosRequestConfig,
	AxiosResponse,
} from "axios";

export class QuizClient {
	private __mainApi: AxiosInstance;
	#origin;
	constructor(url: string) {
		this.#origin = url;
		this.__mainApi = axios.create({
			withCredentials: true,
			headers: {
				"Content-Type": "application/json",
				"X-Requested-From": "app-0.0.1",
			},
			baseURL: url,
		});
	}

	get origin() {
		return this.#origin;
	}

	async request(
		method: string,
		url: string,
		config?: Omit<AxiosRequestConfig, "url" | "method">
	) {
		let toLogout = false;
		try {
			const response = await this.__mainApi({
				method: method,
				url: url,
				...config,
			});
			return response;
		} catch (error) {
			if (error instanceof AxiosError) {
				if (error.status === 401) {
					toLogout = true;
				} else {
					throw error;
				}
			} else {
				throw error;
			}
		}
		if (toLogout) {
			window.location.reload();
		}
	}

	protected async fetch<T, D = unknown>(
		config: AxiosRequestConfig<D> & { data?: D }
	) {
		try {
			const response: AxiosResponse<T> = await this.__mainApi(config);
			if (
				response.data &&
				typeof response.data === "object" &&
				"error" in response.data
			) {
				throw response.data.error;
			}
			return response;
		} catch (error) {
			if (error instanceof AxiosError === false) {
				throw error;
			}
			if (error.status === 401) {
				window.location.reload();
			}
			console.log(error);
			if (
				error.response?.data &&
				typeof error.response.data === "object" &&
				"error" in error.response.data
			) {
				throw new Error(error.response.data.error);
			}
			throw error;
		}
	}

	async getSession() {
		try {
			const res = await this.fetch<{ session: Session }>({
				method: "GET",
				url: "/quiz-api/session/data",
			});
			return res.data.session;
		} catch (ex) {
			console.log(ex);
			return null;
		}
	}

	async getQuiz(id: string) {
		try {
			const res = await this.request("GET", `/quiz/${id}`);
			if (res?.data.error) {
				throw new Error(res.data.error);
			}
			return res?.data as Quiz;
		} catch (error) {
			console.trace(error);
			if (error instanceof AxiosError) {
				throw new Error(error?.message ?? "Internal Server Error");
			}
			if (error instanceof Error) {
				throw new Error(error?.message);
			}
			throw new Error("Something went wrong");
		}
	}
}
