import { EventEmitter } from "events";
import TypedEmitter from "typed-emitter";
import { io } from "socket.io-client";
import { CQSocket, ListeningEvent } from "@/@types/client";

type ClientEvents = {
	"socket-connected": () => void;
	"socket-disconnected": () => void;
	auth_succeed: () => void;
};

export default class SocketClientHandler extends (EventEmitter as new () => TypedEmitter<ClientEvents>) {
	private url: string;
	private path?: string;
	private numberOfConnections: number;
	private connectionInitiated: boolean;
	private socketClient?: CQSocket;
	private isSocketAuthenticated: boolean;
	constructor(url: string, path?: string) {
		super();
		this.url = url;
		this.path = path;
		this.numberOfConnections = 0;
		this.connectionInitiated = false;
		this.isSocketAuthenticated = false;
	}

	#handleConnectionEvents() {
		if (!this.socketClient) {
			return;
		}
		this.socketClient.on("connect", () => {
			this.emit("socket-connected");
		});
		this.socketClient.on("disconnect", () => {
			this.emit("socket-disconnected");
		});
		this.socketClient.on("auth_succeed", () => {
			this.isSocketAuthenticated = true;
			this.emit("auth_succeed");
		});
	}

	#createConnection() {
		if (this.connectionInitiated) {
			return;
		}
		this.connectionInitiated = true;
		this.socketClient = io(this.url, {
			path: this.path,
			transports: ["websocket"],
		});
		this.#handleConnectionEvents();
	}

	#disconnect() {
		if (this.connectionInitiated && this.socketClient) {
			this.connectionInitiated = false;
			this.isSocketAuthenticated = false;
			console.log("Close socket connection");
			this.socketClient.disconnect();
		}
	}

	#register() {
		this.numberOfConnections++;
		if (!this.connectionInitiated) {
			this.#createConnection();
		}
	}

	#unregister() {
		this.numberOfConnections--;
		if (this.numberOfConnections <= 0) {
			this.#disconnect();
		}
	}

	getSocket(): CQSocket {
		// eslint-disable-next-line @typescript-eslint/no-this-alias
		const classInstance = this;
		if (!this.socketClient) {
			this.#createConnection();
		}
		const sessionProxy = new Proxy(this.socketClient!, {
			get: (_target, p, receiver) => {
				if (p === "connect") {
					return classInstance.#register.bind(classInstance);
				}
				if (p === "disconnect") {
					return classInstance.#unregister.bind(classInstance);
				}
				if (p == "on" || p == "once") {
					return (
						event: keyof ListeningEvent,
						listener: () => void // FIX THIS TYPE
					) => {
						if (event === "connect") {
							if (this.socketClient?.connected) {
								setTimeout(listener);
							}
						}
						if (event === "auth_succeed") {
							if (this.isSocketAuthenticated) {
								setTimeout(listener);
							}
						}
						return this.socketClient![p](event, listener);
					};
				}
				return Reflect.get(this.socketClient!, p, receiver);
			},
		});
		return sessionProxy;
	}
}
