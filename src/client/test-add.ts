import {
	COReport,
	Invited,
	Quiz,
	QuizContentResponse,
	QuizEmailSetting,
	QuizReport,
	Root,
	UserReportRoot,
} from "@/testReport/data/data";
import { QuizClient } from "./quiz-client";
import { QuizList } from "@/testList/data/data";
import dayjs, { Dayjs } from "dayjs";
import { statsResponseDataType } from "@/components/test-metrics/metrics";
import { AppConfig } from "@/config";
import { Question } from "@/components/add-question-modal/AddQuestionModal";
import axios, { AxiosError } from "axios";
import {
	QuestionAddReqBodyDataType,
	QuestionAddRoot,
	QuestionEditRoot,
} from "@/components/question-layout/data";
import { Room } from "@/components/test-rooms/meetingRoom";
import { defaultFieldKeys } from "@/constants/test";
import { LearnerAttemptRoot } from "@/components/learnerDashboard/LearnerDashBoard";

export type QuizRoomResponse = {
	id: string;
	roomName: string;
	users: {
		_id: string;
		displayname: string;
		email: string;
	}[];
	invigilators: {
		_id: string;
		displayname: string;
		email: string;
	}[];
};

export interface BulkRoomReqDataType {
	roomName: string;
	rollNumbers: string[];
	userEmails: string[];
	invigilatorEmails: string[];
}

export interface BulkUploadResponse {
	roomName: string;
	rollNumbers: string | undefined[];
	userEmails: string[];
	invigilatorEmails: string[];
	invalidInvigilatorEmails: string[];
	invalidUserEmails: string[];
	index: number;
	error: string;
}

export type QuizRoomRequest = {
	roomId: string;
	roomName: string;
	invigilatorEmails: string[];
	userEmails: string[];
};

export const enum QuestionType {
	MCQ = "1",
	SUBJECTIVE = "2",
	CODING = "4",
	MULTIPLE = "5",
	WEB = "9",
}

const quizContentType = {
	[QuestionType.MCQ]: "1",
	[QuestionType.CODING]: "3",
	[QuestionType.SUBJECTIVE]: "2",
	[QuestionType.MULTIPLE]: "4",
	[QuestionType.WEB]: "9",
};

export const QuestionTypesMap: { [K in QuestionType]: string } = {
	[QuestionType.MCQ]: "MCQ",
	[QuestionType.SUBJECTIVE]: "Subjective",
	[QuestionType.CODING]: "Coding",
	[QuestionType.MULTIPLE]: "Multiple Questions",
	[QuestionType.WEB]: "Web",
};

export const enum QuestionDificulty {
	EASY,
	MEDIUM,
	HARD,
}

export type TestContentQuestionType = {
	_id: string;
	title: string;
	description?: string;
	dificulty?: QuestionDificulty;
	score: number;
	createdBy?: string;
	orgId?: string;
} & (
	| {
			type: Exclude<QuestionType, QuestionType.CODING | QuestionType.WEB>;
			settings?: never;
	  }
	| {
			type: QuestionType.CODING;
			isOnCloud?: boolean;
			settings?: {
				result: string;
				contentHead: boolean;
				contentTail: boolean;
				customInput: boolean;
			};
	  }
	| {
			type: QuestionType.WEB;
			settings?: never;
	  }
);

export type TestContentType = {
	testId?: string;
	key: string;
	title: string;
	poolCount?: number;
	questions: TestContentQuestionType[];
	description?: string;
	expanded?: boolean;
	isPoolQuestion?: boolean;
};

export type testListRequest = {
	quizType: number;
	[key: string]: unknown;
	length: number;
	start: number;
	search: { value: string };
	sorting: unknown;
	keywords: string[];
	fltrCreatedBy?: unknown[];
	isTemplate?: number;
};

export type questionListRequest = {
	[key: string]: unknown;
	length: number;
	start: number;
	search: { value: string };
	sorting: unknown;
	fltrCreatedBy: string[];
	keywords: string[];
};

export type testCaseRequest = {
	input: string;
	output: string;
	quesId: string;
	sampleTest: boolean;
	score: number;
	testId?: string;
};

export type extraTimeResponse =
	| {
			msg: string;
			updatedUsers: unknown[];
			totalUsers: number;
			notUpdatedUsers: unknown[];
	  }
	| {
			error: string;
	  };

export type resetSectionResponse =
	| {
			failed: unknown[];
	  }
	| {
			error: string;
	  };

export const enum TestSaveProgressStep {
	GENERAL = 1,
	ACCESS = 5,
	CONTENT = 10,
	PROCTORING = 15,
	NOTIFICATIONS = 20,
}

export type TestContent = {
	sections: TestContentType[];
	isFrozen: boolean;
	isPreviewOnly: boolean;
	canModifyContent: boolean;
	testScore: number;
};

export type TestData = {
	_id: string;
	isFrozen: boolean;
	isTemplate: boolean;
	progressStep: TestSaveProgressStep;
	alreadyClonedThroughThisLink?: boolean;
	tabs: TabsDataFormatType;
};

export type TestListResponse = {
	draw: number | null;
	recordsTotal: number;
	recordsFiltered: number;
	data: QuizList[];
};

export type TestContentResponse = {
	_id: string;
	type: "tutorial" | "question";
	title: string;
	score: number;
	qtype: QuestionType;
	isOnCloud: boolean;
	executionType: string;
	showHead: boolean;
	showTail: boolean;
	showCustomInput: boolean;
	createdBy: string;
	orgId: string;
};

export type questionListResponse = {
	draw: number | null;
	recordsTotal: number;
	recordsFiltered: number;
	data: Question[];
};

export type meetingRoomResponse = {
	quizReport: QuizReport;
	sampleData: Invited;
	room: Room;
};

export type ScoreUpdationActivityListDataType = {
	originalScore: string;
	newScore: number;
	updatedBy: string;
	updatedAt: number;
};

export type ScoreUpdationActivityResponse = {
	scoreUpdationActivityList: ScoreUpdationActivityListDataType[];
	userMap: Record<string, string>;
};

export type InviteCandidateTestResponse = {
	error?: string;
	data: {
		emailAlreadyAdded: string[];
		inviteesEmailsAdded: string[];
		errorWhileCreatingUser: string[];
		error?: string;
	};
};

export interface GetEndPointsMap {
	"/tests": {
		requestParams: never;
		responseBody: { _id: string }[];
		requestBody: never;
	};
	"/test/add/:id": {
		requestParams: { id: string };
		requestBody: never;
		responseBody: {
			quiz: Quiz;
			quizEmailSetting: QuizEmailSetting;
		};
	};
	"/quest/getPresignedUrlForTestCase/:questionId/:testCaseId": {
		requestParams: { questionId: string; testCaseId: string };
		requestBody: never;
		responseBody: string;
	};
	"/test/addQuizContent/:id": {
		requestParams: { id: string };
		requestBody: never;
		responseBody: QuizContentResponse;
	};
	"/quest/getDescription/:id": {
		requestParams: { id: string };
		requestBody: never;
		responseBody: {
			success: boolean;
			data: {
				title: string;
				testCase?: number;
			};
		};
	};
	"/quiz-api/getActivityLogs": {
		requestParams: { quizId: string; type?: number };
		requestBody: never;
		responseBody: {
			logs: Array<{
				_id: string;
				updatedAt: string;
				createdAt: string;
				data: {
					ip?: string;
					failedUserIds?: Array<string>;
					userIds?: Array<string>;
					error?: number;
				};
				type: number;
				createdBy: string;
				orgId: string;
				quizId: string;
				version: number;
				subType: number;
				activityTime: string;
				__v: number;
			}>;
			userData: Array<{
				_id: string;
				displayname: string;
				email: string;
			}>;
		};
	};
	"/test/viewLink/:id": {
		requestParams: never;
		responseBody: { link: string };
		requestBody: never;
	};
	"/test/quizName": {
		requestParams: { name: string };
		responseBody: string | { error: object };
		requestBody: never;
	};
	"/useranswer/userReport/:quizId/:userId": {
		requestParams: { testId: string; userId: string };
		responseBody: UserReportRoot;
		requestBody: never;
	};
	"/test/aiProctorReport/:testId/:userId": {
		requestParams: { testId: string; userId: string };
		responseBody: {
			imageUrl: string | null;
			src?: string;
			time: string;
			subType: number;
			_id: string;
			illegalObject: never[];
		}[];
		requestBody: never;
	};
	"/test/tabSwitchReport:/testId/:userId": {
		requestParams: { testId: string; userId: string };
		responseBody: {
			timeStampsArr: {
				tabSwitchType: number;
				tabTime: string;
				_id: string;
			}[];
		};
		requestBody: never;
	};
	"/test/fullScreenReport:/testId/:userId": {
		requestParams: { testId: string; userId: string };
		responseBody: {
			timeStampsArr: {
				eventType: number;
				time: string;
				_id: string;
			}[];
		};
		requestBody: never;
	};
	"/test/loginTimestamps:/testId/:userId": {
		requestParams: { testId: string; userId: string };
		responseBody: {
			timeStampsArr: {
				ip: string;
				platform: string;
				loginTime: string;
				_id: string;
			}[];
		};
		requestBody: never;
	};
	"/test/users/:id": {
		requestParams: { id: string };
		requestBody: never;
		responseBody: Root;
	};
	"/coBtlCSV/:id": {
		requestParams: { id: string };
		responseBody: { data: Array<COReport> };
		requestBody: never;
	};
	"/test/quizStats/:testId": {
		requestParams: { testId: string };
		responseBody: statsResponseDataType | { error: string };
		requestBody: never;
	};
	"/test/quizRoom/:testId": {
		requestParams: { testId: string };
		responseBody: {
			rooms: {
				_id: string;
				createdAt: string;
				createdBy: string;
				displaystatus: string;
				invigilatorIds: string[];
				orgId: string;
				quizId: string;
				roomName: string;
				updatedAt: string;
				updatedBy: string;
				userIds: string[];
			}[];
			userData: {
				displayname: string;
				email: string;
				_id: string;
			}[];
		};
		requestBody: never;
	};
	"/test/deleteQuizRoom/:roomId": {
		requestParams: { roomId: string };
		responseBody: {
			msg: string;
		};
		requestBody: never;
	};
	"test/extraTimeLog": {
		requestParams: { quizId: string; userId: string };
		requestBody: never;
		responseBody: {
			data: {
				_id: string;
				extraTime: {
					extraTime: number;
					expireAt: string;
					createdBy: {
						_id: string;
						displayname: string;
					};
					createdAt: string;
					_id: string;
				}[];
			};
		};
	};
	"/tags/filterdata": {
		requestParams: { from: string };
		requestBody: never;
		responseBody:
			| {
					keywords: { _id: string; tag: string }[];
					users: { _id: string; displayname: string }[];
			  }
			| { error: string };
	};
	"test/templates/list": {
		requestParams: never;
		requestBody: never;
		responseBody:
			| {
					templateQuizArr: {
						_id: string;
						title: string;
					}[];
			  }
			| { error: string };
	};

	"/quiz-api/otpForForceSubmission/:id": {
		requestParams: { id: string };
		requestBody: never;
		responseBody: never;
	};
	"/test/delquiz/:id": {
		requestParams: { id: string };
		requestBody: never;
		responseBody: { msg: string } | { error: string };
	};
	"/quest/delques/:id": {
		requestParams: { id: string };
		requestBody: never;
		responseBody: { msg: string } | { error: string };
	};
	"/quest/lockQuestion": {
		requestParams: { quesId: string; isLocked: 0 | 1 };
		requestBody: never;
		responseBody: { msg: string } | { error: string };
	};
	"/quest/updateQuestion": {
		requestParams: { questionId: string; isPublic?: boolean };
		requestBody: never;
		responseBody: { msg: string } | { error: string };
	};
	"/test/getJWTForDownload/:quizId/:userId": {
		requestParams: { quizId: string; userId: string };
		requestBody: never;
		responseBody: { token: string };
	};
	"/test/freezeQuiz/:id": {
		requestParams: { quizId: string; userId: string };
		requestBody: never;
		responseBody: { message: string } | { error: string };
	};
	"/test/unfreezeQuiz/:id": {
		requestParams: { quizId: string; userId: string };
		requestBody: never;
		responseBody: { message: string } | { error: string };
	};
	"/quest/add": {
		requestParams: { quesId?: string };
		requestBody: never;
		responseBody: QuestionAddRoot | QuestionEditRoot;
	};
	"/quest/exportQuestion/:quesId": {
		requestParams: { quesId: string };
		requestBody: never;
		responseBody: { msg: string } | { error: string };
	};
	"/test/recentSubmissions/list": {
		requestParams: never;
		responseBody: {
			submissionArr: {
				quizId: string;
				quizName: string;
				userId: string;
				userName: string;
				score: number;
				submitTime: number;
				quizCreatedBy: string;
				creatorName: string;
				passingMarks?: number;
			}[];
		};
		requestBody: never;
	};
	"/test/dashboard": {
		requestParams: never;
		responseBody: {
			dashboardData: {
				_id: string;
				title: string;
				quizTime: number;
				quizCode?: number;
				createdBy: string;
				parentIdOfCreator?: string;
				startTime: string;
				userIds: string[];
				studentsCount: number;
			}[];
			activeUsersCount: number;
		};
		requestBody: never;
	};
	"/meeting/rooms/:id": {
		requestParams: never;
		requestBody: never;
		responseBody: meetingRoomResponse | { error: string };
	};
	"/user-dashboard/user-quiz?fromRedis=1": {
		requestParams: never;
		requestBody: never;
		responseBody: LearnerAttemptRoot | { error: string };
	};
	"/test/getJWTForMarkingQuizAsVerified/:id": {
		requestParams: never;
		requestBody: never;
		responseBody: { token: string } | { error: string };
	};
	"/test/markReportAsUnresolved/:id": {
		requestParams: never;
		requestBody: never;
		responseBody: { success: boolean } | { error: string };
	};
	"/quiz-api/otpwaittime/:id": {
		requestParams: never;
		requestBody: never;
		responseBody: { waitTime_Sec: number } | { error: string };
	};
	"/test/cloneQuizByToken/:token": {
		requestParams: { token: string };
		requestBody: never;
		responseBody: {
			quiz: Quiz;
			quizEmailSetting: QuizEmailSetting;
			alreadyClonedThroughThisLink: boolean;
		};
	};
	"/test/contentWithToken": {
		requestParams: { token: string };
		requestBody: never;
		responseBody: QuizContentResponse;
	};
	"/quest/preview/:id": {
		requestParams: { quesId?: string };
		requestBody: never;
		responseBody: QuestionEditRoot;
	};
	"/test/download/:id": {
		requestParams: { quesId?: string };
		requestBody: never;
		responseBody: { msg: string };
	};
}

export interface TabsDataFormatType {
	general: {
		title: string;
		quizTime: number;
		instructions: string;
	};
	access: {
		isNotPrivate: boolean;
		link?: string;
		quizCode?: number;
	} & {
		startTime: Dayjs;
		endTime?: Dayjs;
		entryStopTime?: Dayjs;
		allowedIP: string[];
		showResults: boolean;
	};
	content: {
		cutOffMarks?: number;
		languagesAllowed: string[];
		keywords: string[];
		shuffleQuestions: boolean;
		randomQuestions: boolean;
		revisitSections: boolean;
		ShuffleMCQOptions: boolean;
		customFields: {
			key: string;
			label: string;
			type: number;
			active: boolean;
			values?: string[];
			editable: boolean;
		}[];
	};
	proctoring: {
		copyPasteAllowed?: boolean;
		isFullScreen?: boolean;
		isSignUpAllowed?: boolean;
	} & (
		| { preventTabSwitch?: true }
		| {
				preventTabSwitch: false;
				tabSwitchAlertLimit?: number;
				submitTestOnTabSwitchLimitBreach?: boolean;
		  }
	) &
		(
			| { isWebCamAllowed?: false }
			| {
					isWebCamAllowed: true;
					isRandomImageCaptureEnabled?: boolean;
					isAIProctoringEnabled?: boolean;
					onlySuspiciousRoom?: boolean;
					isLiveStreamEnabled?: boolean;
			  }
		) &
		(
			| { isAppOnly?: false }
			| {
					isAppOnly: true;
					allowClose?: boolean;
					isVmDetectionEnabled?: boolean;
					isTerminateStudentEnable?: boolean;
			  }
		) &
		(
			| { isAppOnly?: false; isWebCamAllowed?: false }
			| {
					isWebCamAllowed: true;
					isAppOnly: true;
					isRecordingEnabled?: boolean;
					isRecordingUploadBlockingEnabled?: boolean;
			  }
		);
	notifications: {
		"candidate.sendEmail": boolean;
		"candidate.subject": string;
		"candidate.message": string;
		"organisation.sendEmail": boolean;
		"organisation.subject": string;
		"organisation.message": string;
	};
}

export interface TestUpdateEndPointsMap {
	general: {
		requestParams: never;
		responseBody: { test: Quiz };
		requestBody: {
			title: string;
			quizTime: number;
			instructions: string;
		};
	};
	access: {
		requestParams: never;
		responseBody: { _id: string };
		requestBody: (
			| {
					private: "off";
					testLink?: string;
					quizCode?: number;
			  }
			| { private: "on" | "off" }
		) & {
			startTime: Dayjs;
			endTime?: Dayjs;
			entryStopTime?: Dayjs;
			allowedIP: string[];
			showResults: boolean;
		};
	};
	content: {
		requestParams: never;
		responseBody: { _id: string };
		requestBody: {
			cutOffMarks?: number;
			languagesAllowed: string[];
			keywords: string;
			randomizeQuestion: boolean;
			poolQuestion: boolean;
			revisitAllowed: boolean;
			quizUserDetails: string;
			toShuffleMCQOptions: boolean;
			// quizUserDetails: {
			// 	fieldName: string;
			// 	fieldLabel: string;
			// 	fieldType: number;
			// 	fieldIsSelected: boolean;
			// 	fieldOptions?: string[];
			// 	// editable: boolean;
			// }[];
		};
	};
	proctoring: {
		requestParams: never;
		responseBody: { _id: string };
		requestBody: {
			copyPasteAllowed?: boolean;
			isFullScreen?: boolean;
			isSignUpAllowed?: "on" | boolean;
		} & (
			| { tabSwitchAllowed?: true }
			| {
					tabSwitchAllowed: false;
					tabSwitchAlertLimit?: number;
					submitTestOnTabSwitchLimitBreach?: boolean;
			  }
		) &
			(
				| { isWebCamAllowed?: false }
				| {
						isWebCamAllowed: true;
						isLiveStreamEnabled?: boolean;
				  }
			) &
			(
				| { isAppOnly?: false }
				| { isAppOnly: true; allowClose?: boolean }
			) &
			(
				| { isAppOnly?: false; isWebCamAllowed?: false }
				| {
						isWebCamAllowed: true;
						isAppOnly: true;
						isTerminateStudentEnabled?: boolean;
						isRecordingEnabled?: boolean;
				  }
			);
	};
	notifications: {
		requestParams: never;
		requestBody: {
			quizEmailSetting: string;
			// quizEmailSetting: {
			// 	sendMail: boolean;
			// 	testCompletionStudentMail: number;
			// 	quizCompletionIntimationSetting: number;
			// 	mailTemplate: {
			// 		msg: string[];
			// 		subject: string;
			// 	};
			// 	intimationMailTemplate: {
			// 		msg: string[];
			// 		subject: string;
			// 	};
			// };
		};
		responseBody: { _id: string };
	};
}

export interface PostEndPointsMap {
	"/newtest": TestUpdateEndPointsMap["general"];

	// not used
	"/test/:testId/content/section/create": {
		requestParams: { testId: string };
		responseBody: { _id: string; index: number };
		requestBody: {
			insertPosition?: { index: number; at: "after" | "before" }; // not used
			title: string;
		};
	};
	"/test/addCourseContent/createSegment": {
		requestParams: never;
		requestBody: { quizId: string; title: string };
		responseBody: {
			segmentIndex: number;
			segment: {
				title: string;
				_id: string;
				count: number;
				desc?: string;
				pollNumber?: number;
			};
			msg: string;
		};
	};
	"/test/addCourseContent/addContentInSegment": {
		requestParams: never;
		requestBody: {
			quizId: string;
			isPoolQuestion: boolean;
			pollNumber: number;
			segmentCount: string | number;
			selectedIds: string;
			// selectedIds: { id: string; type: QuestionType }[]; // non-stringified version
		};
		responseBody: never;
	};
	"/test/addCourseContent/addSegmentPollNumber": {
		requestParams: never;
		requestBody: {
			quizId: string;
			isPoolQuestion: boolean;
			pollNumber: string | number;
			segmentCount: string | number;
		};
		responseBody: never;
	};
	"/test/addQuizContent/saveorder/:testId": {
		requestParams: never;
		requestBody: {
			segments: {
				_id?: string;
				startIndex: number;
				title: string;
				skipAllowed: number;
				share: number;
				desc: string;
				pollNumber: number;
				endIndex: number;
				count: number;
			}[];
			sequence: {
				id: string;
				executionType: string;
				showHead: number;
				showTail: number;
				skipAllowed: string;
				showCustomInput: number;
				isFreeze: boolean;
				contentType: string;
			}[];
			questionId: string[];
		};
		responseBody: Quiz;
	};
	"/test/addCourseContent/removeContentFromSegment": {
		requestParams: never;
		requestBody: {
			quizId: string;
			segmentCount: string | number;
			selectedIds: string;
			// selectedIds: string[]; // stringified ?
		};
		responseBody: never;
	};

	"/test/updateQuizRoom": {
		requestParams: never;
		responseBody: QuizRoomResponse;
		requestBody: QuizRoomRequest;
	};
	"/test/createNewRoom/:testId": {
		requestParams: never;
		responseBody: QuizRoomResponse | { error: string };
		requestBody: QuizRoomRequest;
	};
	"/test/addExtraTime": {
		requestParams: never;
		requestBody: {
			quizId: string;
			time: number;
			extraTimeExpire?: number;
			userId: string[];
		};
		responseBody: extraTimeResponse;
	};
	"test/resetUserSection/:quizId": {
		requestParams: never;
		requestBody: { userId: string[]; resetUpto: number };
		responseBody: resetSectionResponse;
	};
	"test/addAllowedEmailIds": {
		requestParams: never;
		requestBody: { quizId: string; allowedEmailIds: string[] };
		responseBody: never | { error: string };
	};
	"test/inviteEmails": {
		requestParams: never;
		requestBody: {
			quizId: string;
			emailIds: string;
			mailContent: string;
			mailSubject: string;
			expireTime?: string;
		};
		responseBody: InviteCandidateTestResponse;
	};
	"/bulkUploadMCQ": {
		requestParams: never;
		requestBody: {
			quizId?: string;
			segmentIndex?: string;
			finalData: object;
		};
		responseBody: {
			failed: Array<Record<string, unknown>>;
		};
	};
	"/test/pagination": {
		requestParams: never;
		requestBody: testListRequest;
		responseBody: TestListResponse | { error: string };
	};
	"/test/cloneQuiz/:id": {
		requestParams: never;
		requestBody:
			| {
					isClone: boolean;
					randomizeQuestion: boolean;
					poolQuestion: boolean;
					quizId: string;
			  }
			| { title: string };
		responseBody: { _id: string } | { error: string };
	};
	"/quest/pagination": {
		requestParams: never;
		requestBody: questionListRequest;
		responseBody: questionListResponse | { error: string };
	};
	"/test/makeTemplate": {
		requestParams: never;
		requestBody: { isMakeTemplate: boolean; quizId: string };
		responseBody: { success: string };
	};
	"/quest/cloneQuest/:id": {
		requestParams: { id: string };
		requestBody: never;
		responseBody: { msg: string } | { error: string };
	};
	"/quest/addTestCase": {
		requestParams: never;
		requestBody: testCaseRequest;
		responseBody: {
			error?: string;
			msg?: string;
			testId?: string;
		};
	};
	"/quest/deleteTestCase": {
		requestParams: never;
		requestBody: {
			quesId: string;
			testId: string;
		};
		responseBody: {
			error?: string;
			msg?: string;
		};
	};
	"/quest/add/:id": {
		requestParams: { quizId?: string; segmentIndex?: string };
		requestBody: FormData;
		responseBody: { error: string } | QuestionEditRoot;
	};
	"/quest/removeFile": {
		requestParams: never;
		requestBody: { fileName: string; quesId: string };
		responseBody: { msg: string } | { error: string };
	};
	"/quest/delques": {
		requestParams: never;
		requestBody: { ids: string[] };
		responseBody: { ids: string[] } | { error: string };
	};
	"/quest/updateSolution": {
		requestParams: never;
		requestBody: { quesId: string; multiLanguage: Record<number, string> };
		responseBody: { msg: string } | { error: string };
	};
	"/test/bulkUploadRoom/:id": {
		requestParams: never;
		requestBody: { finalData: BulkRoomReqDataType[] };
		responseBody: BulkUploadResponse[];
	};
	"/test/quizTemplateAccess": {
		requestParams: never;
		requestBody: { isPublic: string; quizId: string };
		responseBody: { msg: string };
	};
	"test/addmarks/:quizId/:quesId/:userId": {
		requestParams: never;
		requestBody: FormData;
		responseBody: { success: boolean } | { error: string };
	};
	"api/getScoreUpdationActivity": {
		requestParams: never;
		requestBody: {
			questionId: string;
			quizId: string;
			userId: string;
		};
		responseBody: ScoreUpdationActivityResponse | { error: string };
	};
	"/test/quizEmail/toggleIsValid": {
		requestParams: never;
		requestBody: {
			emailId: string;
			isValid: number;
			quizId: string;
		};
		responseBody: { msg: string };
	};
	"test/quizEmail/deleteEmail": {
		requestParams: never;
		requestBody: {
			emailId: string[];
			isInvitee?: number;
			quizId: string;
		};
		responseBody: { msg: string };
	};
	"/test/quizEmail/updateExpireTime": {
		requestParams: never;
		requestBody: {
			emailId: string;
			quizId: string;
			updatedTime: string;
		};
		responseBody: { msg: string } | { error: string };
	};
	"/test/markQuizAsChecked/:jwt": {
		requestParams: never;
		requestBody: FormData;
		responseBody: { message: string } | { error: string };
	};
	"/api/sendQuizClone": {
		requestParams: never;
		requestBody: {
			emails: string[];
			quizId: string;
		};
		responseBody: { errors: string[]; userWhereEmailNotSent: string[] };
	};
	"/test/cloneQuizByToken": {
		requestParams: never;
		requestBody: {
			randomizeQuestion: boolean;
			poolQuestion: boolean;
			isClone: boolean;
			token: string;
		};
		responseBody: { _id: string } | { error: string };
	};
}

// type ExtractParams<T extends string> =
// 	T extends `${string}:${infer Param}/${infer Rest}`
// 		? {
// 				[K in Param extends `${infer Key}?` ? Key : Param]: string;
// 			} & ExtractParams<`/${Rest}`>
// 		: T extends `${string}:${infer Param}`
// 			? { [K in Param extends `${infer Key}?` ? Key : Param]: string }
// 			: { [key: string]: string };
// type SimplifiedExtractParams<T extends string, P = ExtractParams<T>> = {
// 	[K in keyof P]: P[K];
// };

export type PutEndPointsMap = {
	[K in keyof TestUpdateEndPointsMap as `/newtest/:id/${K}`]: TestUpdateEndPointsMap[K];
};

type EndPointsMap = GetEndPointsMap & PostEndPointsMap & PutEndPointsMap;

export type EndPointResponseBody<T extends EndPointsMap[keyof EndPointsMap]> =
	T["responseBody"];

export type EndPointRequestBody<T extends EndPointsMap[keyof EndPointsMap]> =
	T["requestBody"];

export type EndPointRequestParams<T extends EndPointsMap[keyof EndPointsMap]> =
	T["requestParams"];

type ExtractEndpoints<T extends string> =
	T extends `${infer _Prefix}:${string}/${infer Rest}`
		? `${_Prefix}${string}${ExtractEndpoints<`/${Rest}`>}`
		: T extends `${infer _Prefix}:${string}`
			? `${_Prefix}${string}`
			: T;

export default class TestAddClient extends QuizClient {
	async get<K extends keyof GetEndPointsMap>(
		url: ExtractEndpoints<K>,
		queryParams?: EndPointRequestParams<GetEndPointsMap[K]>
	) {
		const response = await this.fetch<
			EndPointResponseBody<GetEndPointsMap[K]>
		>({ url, params: queryParams });
		return response?.data ?? null;
	}

	async post<K extends keyof PostEndPointsMap>(
		url: ExtractEndpoints<K>,
		requestData?: {
			data: EndPointRequestBody<PostEndPointsMap[K]>;
			params?: EndPointRequestParams<PostEndPointsMap[K]>;
		}
	) {
		const response = await this.fetch<
			EndPointResponseBody<PostEndPointsMap[K]>
		>({
			method: "POST",
			url,
			data: requestData?.data,
			params: requestData?.params,
		});
		return response?.data ?? null;
	}

	async postFormData<K extends keyof PostEndPointsMap>(
		url: ExtractEndpoints<K>,
		requestData?: {
			data: EndPointRequestBody<PostEndPointsMap[K]>;
			params?: EndPointRequestParams<PostEndPointsMap[K]>;
		}
	) {
		const response = await this.fetch<
			EndPointResponseBody<PostEndPointsMap[K]>
		>({
			method: "POST",
			headers: {
				"Content-Type": "multipart/form-data",
				Accept: "application/json",
			},
			url,
			data: requestData?.data,
			params: requestData?.params,
		});
		return response?.data ?? null;
	}

	async put<K extends keyof PutEndPointsMap>(
		url: ExtractEndpoints<K>,
		requestData?: {
			data: EndPointRequestBody<PutEndPointsMap[K]>;
			params?: EndPointRequestParams<PutEndPointsMap[K]>;
		}
	) {
		const response = await this.fetch<
			EndPointResponseBody<PutEndPointsMap[K]>
		>({
			method: "PUT",
			url,
			data: requestData?.data,
			params: requestData?.params,
		});
		return response?.data ?? null;
	}

	async getTestData(id: string): Promise<TestData> {
		try {
			const { quiz } = await this.get<"/test/add/:id">(`/test/add/${id}`);
			console.log({ quiz });
			let link;
			try {
				const response = await this.get<"/test/viewLink/:id">(
					`/test/viewLink/${id}`
				);
				link = response.link;
			} catch (ex) {
				console.log(ex);
			}
			console.log(link, AppConfig.testAttemptUrlBase);
			return {
				_id: quiz._id,
				progressStep: quiz.progressStep ?? TestSaveProgressStep.GENERAL,
				isFrozen: quiz.isFrozen ?? false,
				isTemplate: quiz.isTemplate ?? false,
				tabs: {
					general: {
						title: quiz.title,
						quizTime: +quiz.quizTime,
						instructions: quiz.instructions,
					},
					access: {
						isNotPrivate: !quiz.isPrivate,
						link: (link ?? Date.now().toString()).replace(
							AppConfig.testAttemptURL + "/",
							""
						),
						quizCode: quiz.quizCode,
						startTime: dayjs(quiz.startTime),
						...(quiz.endTime
							? { endTime: dayjs(quiz.endTime) }
							: {}),
						...(quiz.entryStopTime
							? { entryStopTime: dayjs(quiz.entryStopTime) }
							: {}),
						allowedIP:
							quiz.allowedIP
								?.split(",")
								.filter(text => text !== "") ?? [],
						showResults: quiz.showResults,
					},
					content: {
						cutOffMarks: quiz.cutOffMarks,
						languagesAllowed: quiz.languagesAllowed,
						keywords:
							quiz.keywords
								?.split(",")
								.filter(text => text !== "") ?? [],
						randomQuestions: quiz.poolQuestion,
						shuffleQuestions: quiz.randomizeQuestion,
						revisitSections: quiz.revisitAllowed,
						ShuffleMCQOptions: quiz.toShuffleMCQOptions ?? false,
						customFields: (quiz.quizUserDetails ?? []).map(
							field => ({
								key: field.fieldName,
								label: field.fieldLabel,
								type: field.fieldType,
								active: field.fieldIsSelected,
								values: field.fieldOptions as string[],
								editable: !defaultFieldKeys.has(
									field.fieldName
								),
							})
						),
					},
					proctoring: {
						copyPasteAllowed: quiz.copyPasteAllowed,
						isFullScreen: quiz.isFullScreen,
						preventTabSwitch: !quiz.tabSwitchAllowed as false,
						tabSwitchAlertLimit: quiz.tabSwitchAlertLimit,
						submitTestOnTabSwitchLimitBreach:
							quiz.submitTestOnTabSwitchLimitBreach,
						isWebCamAllowed: quiz.isWebCamAllowed as true,
						isRandomImageCaptureEnabled:
							quiz.isRandomImageCaptureEnabled,
						isAIProctoringEnabled: quiz.isAIProctoringEnabled,
						isLiveStreamEnabled: quiz.isLiveStreamEnabled,
						isAppOnly: quiz.isAppOnly as true,
						allowClose: quiz.allowClose,
						isVmDetectionEnabled: quiz.isVmDetectionEnabled,
						isTerminateStudentEnable:
							quiz.toTerminateTestOnVmDetection,
						isRecordingEnabled: quiz.isRecordingEnabled,
						isRecordingUploadBlockingEnabled:
							quiz.isRecordingUploadBlockingEnabled,
						onlySuspiciousRoom: quiz.onlySuspiciousRoom,
						isSignUpAllowed: quiz.isSignUpAllowed ?? false,
					},

					notifications: {
						"candidate.sendEmail":
							(quiz.quizEmailSetting?.sendMail ?? false) &&
							Boolean(
								quiz.quizEmailSetting?.testCompletionStudentMail
							),
						"candidate.message":
							quiz.quizEmailSetting?.mailTemplate?.msg?.join(
								""
							) ?? "",
						"candidate.subject":
							quiz.quizEmailSetting?.mailTemplate?.subject ?? "",
						"organisation.sendEmail":
							(quiz.quizEmailSetting?.sendMail ?? false) &&
							Boolean(
								quiz.quizEmailSetting
									?.quizCompletionIntimationSetting
							),
						"organisation.message":
							quiz.quizEmailSetting?.intimationMailTemplate?.msg?.join(
								""
							) ?? "",
						"organisation.subject":
							quiz.quizEmailSetting?.intimationMailTemplate
								?.subject ?? "",
					},
				},
			};
		} catch (ex) {
			console.log("Unable to get test data", ex);
			throw "Unable to get test data";
		}
	}

	async getTestCaseFromCloud(questionId: string, testCaseId: string) {
		try {
			const url =
				await this.get<"/quest/getPresignedUrlForTestCase/:questionId/:testCaseId">(
					`/quest/getPresignedUrlForTestCase/${questionId}/${testCaseId}`,
					{
						questionId: questionId,
						testCaseId: testCaseId,
					}
				);
			const input = await axios.get(url);
			return input.data as string;
		} catch (error) {
			console.log(error);
			throw error;
		}
	}
	async getTestContent(id: string): Promise<TestContent> {
		try {
			const { quiz, isFrozen, previewOnly, contentModify } =
				await this.get<"/test/addQuizContent/:id">(
					`/test/addQuizContent/${id}`
				);
			const questionsArray = quiz.quizContentArray ?? [];
			const sectionsArray = quiz.quizObj.quizSegments ?? [];
			const testScore = questionsArray.reduce(
				(result, question) => result + question.score,
				0
			);
			const sections: TestContent["sections"] = [];
			let questionsIterationCounter = 0;
			console.log(questionsArray);
			for (const [, section] of sectionsArray.entries()) {
				const startIndex = questionsIterationCounter;
				const endIndex = questionsIterationCounter + section.count;
				const typesCount: {
					[K in
						| Exclude<TestContentResponse["type"], "question">
						| TestContentResponse["qtype"]]: number;
				} = {
					tutorial: 0,
					[QuestionType.MCQ]: 0,
					[QuestionType.SUBJECTIVE]: 0,
					[QuestionType.MULTIPLE]: 0,
					[QuestionType.CODING]: 0,
					[QuestionType.WEB]: 0,
				};
				// let segmentContentLength = 0;
				// let sectionScore = 0;
				const questions: TestContentQuestionType[] = [];
				for (let q = startIndex; q < endIndex; q++) {
					if (q >= questionsArray.length) {
						break;
					}
					// segmentContentLength++;
					const question = questionsArray[q];
					switch (question.type) {
						case "tutorial":
							typesCount.tutorial++;
							break;
						case "question":
							// sectionScore += question.score;
							typesCount[question.qtype]++;
					}
					questions.push({
						_id: question._id,
						score: question.score,
						title: question.title,
						createdBy: question.createdBy,
						orgId: question.orgId,
						...(question.qtype === QuestionType.CODING
							? {
									type: question.qtype,
									isOnCloud: Boolean(question.isOnCloud),
									settings: {
										result: question.executionType,
										contentHead: question.showHead,
										contentTail: question.showTail,
										customInput: question.showCustomInput,
									},
								}
							: { type: question.qtype }),
					});
				}
				questionsIterationCounter += section.count;
				sections.push({
					key: section._id,
					title: section.title,
					questions,
					testId: id,
					description: section.desc,
					poolCount: section.pollNumber ?? 0,
					isPoolQuestion: quiz.quizObj.poolQuestion,
				});
			}

			return {
				sections,
				testScore,
				isFrozen,
				isPreviewOnly: previewOnly,
				canModifyContent: contentModify,
			};
		} catch (ex) {
			console.log(ex);
			throw "Unable to get content";
		}
	}

	async getDescription(id: string) {
		try {
			const response = await this.get<"/quest/getDescription/:id">(
				`/quest/getDescription/${id}`
			);
			if (!response.success) {
				throw "Unable to get description";
			}
			return response.data;
		} catch (ex) {
			console.log(ex);
			throw "Unable to get description";
		}
	}
	async getFinishDescriptionLogs(quizId: string) {
		try {
			const response = await this.get("/quiz-api/getActivityLogs", {
				quizId,
				type: 1,
			});
			return response;
		} catch (ex) {
			console.log(ex);
			throw "Unable to get finish description logs";
		}
	}

	async checkLinkAvailability(name: string) {
		try {
			const response = await this.get("/test/quizName", { name });
			if (typeof response === "string") {
				return true;
			}
			return "error" in response === false;
		} catch (ex) {
			console.log(ex);
			return false;
		}
	}

	async createTestDetails(
		data: EndPointRequestBody<PostEndPointsMap["/newtest"]>,
		id?: string
	) {
		try {
			if (id) {
				return await this.put<"/newtest/:id/general">(
					`/newtest/${id}/general`,
					{ data }
				);
			}
			return await this.post("/newtest", { data });
		} catch (ex) {
			console.log(ex);
			throw "Unable to create test";
		}
	}

	async saveTestContent(id: string, data: TabsDataFormatType["content"]) {
		try {
			return await this.put<"/newtest/:id/content">(
				`/newtest/${id}/content`,
				{
					data: {
						quizUserDetails: JSON.stringify(
							data.customFields.map(field => ({
								isSelected: field.active,
								fieldLabel: field.label,
								fieldName: field.key,
								fieldOptions: field.values?.join(","),
								fieldType: field.type,
							}))
						),
						keywords: data.keywords.join(","),
						languagesAllowed: data.languagesAllowed,
						poolQuestion: data.randomQuestions,
						revisitAllowed: data.revisitSections,
						randomizeQuestion: data.shuffleQuestions,
						cutOffMarks: data.cutOffMarks,
						toShuffleMCQOptions: data.ShuffleMCQOptions,
					},
				}
			);
		} catch (ex) {
			console.log(ex);
			throw (
				(ex as Error)?.message ?? "Unable to save test content settings"
			);
		}
	}

	async saveTestAccess(id: string, data: TabsDataFormatType["access"]) {
		try {
			return await this.put<"/newtest/:id/access">(
				`/newtest/${id}/access`,
				{
					data: {
						allowedIP: data.allowedIP,
						private: !data.isNotPrivate ? "on" : "off",
						testLink: data.link,
						quizCode: data.quizCode,
						showResults: data.showResults,
						startTime: data.startTime,
						endTime: data.endTime,
						entryStopTime: data.entryStopTime,
					},
				}
			);
		} catch (ex) {
			throw (
				(ex as Error)?.message ?? "Unable to save test access settings"
			);
		}
	}

	async saveTestProctoring(
		id: string,
		data: TabsDataFormatType["proctoring"]
	) {
		try {
			return await this.put<"/newtest/:id/proctoring">(
				`/newtest/${id}/proctoring`,
				{
					data: {
						...data,
						tabSwitchAllowed: !data.preventTabSwitch,
						isSignUpAllowed:
							data.isSignUpAllowed === true ? "on" : false,
					},
				}
			);
		} catch (ex) {
			console.log(ex);
			throw "Unable to save test proctoring settings";
		}
	}

	async saveTestNotifications(
		id: string,
		data: TabsDataFormatType["notifications"]
	) {
		console.log({ id, data });
		try {
			return await this.put<"/newtest/:id/notifications">(
				`/newtest/${id}/notifications`,
				{
					data: {
						quizEmailSetting: JSON.stringify({
							sendMail:
								data["candidate.sendEmail"] ||
								data["organisation.sendEmail"],
							// mailTemplate: data["candidate.subject"]
							testCompletionStudentMail: Number(
								data["candidate.sendEmail"]
							),
							quizCompletionIntimationSetting: Number(
								data["organisation.sendEmail"]
							),
							mailTemplate: {
								msg: convertToArray(data["candidate.message"]),
								subject: data["candidate.subject"] ?? "",
							},
							intimationMailTemplate: {
								msg: convertToArray(
									data["organisation.message"]
								),
								subject: data["organisation.subject"] ?? "",
							},
						}),
					},
				}
			);
		} catch (ex) {
			console.log(ex);
			throw "Unable to save test notifications setting";
		}
	}

	async createTestContentSection(testId: string, sectionTitle: string) {
		try {
			const { segment, segmentIndex } = await this.post(
				"/test/addCourseContent/createSegment",
				{
					data: { quizId: testId, title: sectionTitle },
				}
			);
			return { index: segmentIndex, section: segment };
		} catch (ex) {
			console.log(ex);
			throw "Failed to create section";
		}
	}

	async addTestContentSectionQuestions(
		testId: string,
		sectionIndex: string,
		questions: { _id: string; type: QuestionType }[],
		poolCount: number,
		isPoolQuestion: boolean
	) {
		try {
			await this.post("/test/addCourseContent/addContentInSegment", {
				data: {
					quizId: testId,
					isPoolQuestion: isPoolQuestion,
					pollNumber: poolCount ?? 0,
					segmentCount: sectionIndex,
					selectedIds: JSON.stringify(
						questions.map(question => ({
							id: question._id,
							type: quizContentType[question.type],
						}))
					),
				},
			});
		} catch (ex) {
			console.log(ex);
			throw "Failed to add question";
		}
	}

	async removeTestContentSectionQuestions(
		testId: string,
		sectionIndex: number,
		questionIds: string[]
	) {
		try {
			await this.post("/test/addCourseContent/removeContentFromSegment", {
				data: {
					quizId: testId,
					segmentCount: sectionIndex.toString(),
					selectedIds: JSON.stringify(questionIds),
				},
			});
		} catch (ex) {
			console.log(ex);
			throw "Failed to remove section content";
		}
	}

	async updateTestContentSectionPoolCount(
		testId: string,
		sectionIndex: string,
		poolCount: number,
		isPoolQuestion: boolean
	) {
		try {
			await this.post("/test/addCourseContent/addSegmentPollNumber", {
				data: {
					quizId: testId,
					isPoolQuestion: isPoolQuestion,
					pollNumber: poolCount,
					segmentCount: sectionIndex,
				},
			});
		} catch (ex) {
			console.log(ex);
			throw "Failed to update pool count";
		}
	}

	async updateTestContentOrder(testId: string, sections: TestContentType[]) {
		const questions = sections.flatMap(section => section.questions);
		const questionIds = questions.map(question => question._id);

		type SegmentType = EndPointRequestBody<
			PostEndPointsMap["/test/addQuizContent/saveorder/:testId"]
		>["segments"][number];
		type SequenceType = EndPointRequestBody<
			PostEndPointsMap["/test/addQuizContent/saveorder/:testId"]
		>["sequence"][number];

		const segments = sections.map<SegmentType>((section, sectionIndex) => ({
			_id: section.key,
			count: section.questions.length,
			title: section.title,
			desc: section.description ?? "",
			pollNumber: section.poolCount ?? 0,
			// unknown properties
			share: 0,
			skipAllowed: 0,
			startIndex: sectionIndex,
			endIndex: sectionIndex,
		}));

		const sequence = questions.map<SequenceType>(question => {
			let settings: Pick<
				SequenceType,
				| "executionType"
				| "showCustomInput"
				| "showHead"
				| "showTail"
				| "skipAllowed"
			>;
			if (question.type === QuestionType.CODING) {
				settings = {
					executionType: question.settings?.result ?? "0",
					showCustomInput: Number(
						question.settings?.customInput ?? false
					),
					showHead: Number(question.settings?.contentHead ?? false),
					showTail: Number(question.settings?.contentTail ?? false),
					skipAllowed: question.settings?.result ?? "0", // ????
				};
			} else {
				settings = {
					executionType: "2",
					showCustomInput: 0,
					showHead: 0,
					showTail: 0,
					skipAllowed: "0",
				};
			}
			const contentType = quizContentType[question.type];
			return {
				id: question._id,
				contentType: contentType,
				...settings,
				// unknown properties
				isFreeze: false,
			};
		});

		try {
			return await this.post<"/test/addQuizContent/saveorder/:testId">(
				`/test/addQuizContent/saveorder/${testId}`,
				{ data: { questionId: questionIds, segments, sequence } }
			);
		} catch (ex) {
			console.log(ex);
			throw "Failed to save content";
		}
	}

	async moveTestContentSection(testId: string) {
		return testId;
	}

	async getUserReport(testId: string, userId: string) {
		try {
			return await this.get(
				`useranswer/userReport/${testId}/${userId}` as "/useranswer/userReport/:quizId/:userId"
			);
		} catch (ex) {
			console.log(ex);
			throw "Unable to get submission history report";
		}
	}
	async getAiProctoringReport(testId: string, userId: string) {
		try {
			return await this.get(
				`/test/aiProctorReport/${testId}/${userId}` as "/test/aiProctorReport/:testId/:userId"
			);
		} catch (ex) {
			console.log(ex);
			throw "Unable to get AI Proctoring report";
		}
	}
	async getTabSwitchReport(testId: string, userId: string) {
		try {
			return await this.get(
				`/test/tabSwitchReport/${testId}/${userId}` as "/test/tabSwitchReport:/testId/:userId"
			);
		} catch (ex) {
			console.log(ex);
			throw "Unable to get tab switch report";
		}
	}
	async getFullScreenReport(testId: string, userId: string) {
		try {
			return await this.get(
				`/test/fullScreenReport/${testId}/${userId}` as "/test/fullScreenReport:/testId/:userId"
			);
		} catch (ex) {
			console.log(ex);
			throw "Unable to get Full Screen report";
		}
	}
	async getloginTimestampsReport(testId: string, userId: string) {
		try {
			return await this.get(
				`/test/loginTimestamps/${testId}/${userId}` as "/test/loginTimestamps:/testId/:userId"
			);
		} catch (ex) {
			console.log(ex);
			throw "Unable to get Login Time Stamps report";
		}
	}

	async getTestReport(id: string) {
		try {
			const response = await this.get<"/test/users/:id">(
				`/test/users/${id}`
			);
			return response;
		} catch (er) {
			console.log(er);
			throw "Unable to get test report";
		}
	}
	async getCoReport(id: string) {
		const response = await this.get<"/coBtlCSV/:id">(`/coBtlCSV/${id}`);
		return response?.data ?? [];
	}
	async getRecentSubmissionsList() {
		try {
			const res = await this.get<"/test/recentSubmissions/list">(
				"/test/recentSubmissions/list"
			);
			if ("error" in res) {
				throw res.error;
			}
			return res;
		} catch (er) {
			console.log(er);
			if (er instanceof AxiosError) {
				throw "Unable to get recent submissions list";
			}
			throw er;
		}
	}

	async getActiveTest() {
		try {
			const res = await this.get<"/test/dashboard">("/test/dashboard");
			if ("error" in res) {
				throw res.error;
			}
			return res;
		} catch (er) {
			console.log(er);
			if (er instanceof AxiosError) {
				throw "Unable to get recent submissions list";
			}
			throw er;
		}
	}
	async getTestStats(testId: string) {
		try {
			return await this.get(
				`/test/quizStats/${testId}` as "/test/quizStats/:testId"
			);
		} catch (ex) {
			console.log(ex);
			throw "Unable to get metrics report";
		}
	}
	async getTestRooms(testId: string) {
		try {
			return await this.get(
				`/test/quizRoom/${testId}` as "/test/quizRoom/:testId"
			);
		} catch (ex) {
			console.log(ex);
			throw "Unable to get Rooms";
		}
	}

	async quizRoomPostRequest<T extends keyof PostEndPointsMap>(
		endpoint: T,
		data: EndPointRequestBody<PostEndPointsMap[T]>
	) {
		try {
			return await this.post(endpoint as ExtractEndpoints<T>, { data });
		} catch (ex) {
			console.error(`Error while calling ${endpoint}:`, ex);
			throw new Error(`Unable to process request for ${endpoint}`);
		}
	}

	async updateQuizRoom(
		data: EndPointRequestBody<PostEndPointsMap["/test/updateQuizRoom"]>
	) {
		try {
			return await this.post<"/test/updateQuizRoom">(
				"/test/updateQuizRoom",
				{
					data,
				}
			);
		} catch (ex) {
			console.log(ex);
			if (ex instanceof AxiosError) {
				throw "Unable to update room";
			}
			throw ex;
		}
	}

	async addQuizRoom(
		data: EndPointRequestBody<
			PostEndPointsMap["/test/createNewRoom/:testId"]
		>,
		id: string
	) {
		try {
			const res = await this.post<"/test/createNewRoom/:testId">(
				`/test/createNewRoom/${id}`,
				{
					data,
				}
			);
			return res;
		} catch (ex) {
			console.log(ex);
			if (ex instanceof AxiosError) {
				throw "Unable to add room";
			}
			throw ex;
		}
	}

	async deleteQuizRoom(roomId: string) {
		try {
			const response = await this.get(
				`/test/deleteQuizRoom/${roomId}` as "/test/deleteQuizRoom/:roomId"
			);
			return response;
		} catch (ex) {
			console.log(ex);
			throw "Unable to get Rooms";
		}
	}

	async addExtraTime(
		data: EndPointRequestBody<PostEndPointsMap["/test/addExtraTime"]>
	) {
		try {
			return await this.post("/test/addExtraTime", { data });
		} catch (ex) {
			console.log(ex);
			throw "Unable to add extra time";
		}
	}

	async addAllowedEmailsToQiz(quizId: string, allowedEmailIds: string[]) {
		try {
			return await this.post("test/addAllowedEmailIds", {
				data: { quizId, allowedEmailIds },
			});
		} catch (ex) {
			console.log(ex);
			throw "Unable to add candidates";
		}
	}
	async userSectionControl(
		quizId: string,
		data: EndPointRequestBody<
			PostEndPointsMap["test/resetUserSection/:quizId"]
		>
	) {
		try {
			return await this.post<"test/resetUserSection/:quizId">(
				`test/resetUserSection/${quizId}`,
				{ data }
			);
		} catch (ex) {
			console.log(ex);
			throw "Unable to reset section";
		}
	}
	async addQuizInviteesEmails(
		testId: string,
		data: {
			emailIds: string;
			mailContent: string;
			mailSubject: string;
			expireTime?: string;
		}
	) {
		try {
			return await this.post<"test/inviteEmails">(`test/inviteEmails`, {
				data: {
					...data,
					quizId: testId,
				},
			});
		} catch (ex) {
			console.log(ex);
			if (ex instanceof AxiosError) {
				throw "Unable to send invite emails";
			}
			throw ex;
		}
	}

	async getExtraTimeLogs(quizId: string, userId: string) {
		try {
			return await this.get("test/extraTimeLog", { quizId, userId });
		} catch (ex) {
			console.log(ex);
			throw "Unable to get Extra Time Logs";
		}
	}
	async getTestTemplate() {
		try {
			return await this.get("test/templates/list");
		} catch (ex) {
			console.log(ex);
			throw "Unable to get Test Template";
		}
	}
	async bulkUploadMCQ(
		finalData: object,
		quizId?: string,
		sectionIndex?: number
	) {
		try {
			return await this.post("/bulkUploadMCQ", {
				data: {
					segmentIndex: sectionIndex?.toString(),
					quizId,
					finalData,
				},
			});
		} catch (ex) {
			console.log(ex);
			if (ex instanceof AxiosError) {
				throw "Unable to get Test Data";
			}
			throw ex;
		}
	}
	async getTests(
		data: EndPointRequestBody<PostEndPointsMap["/test/pagination"]>
	) {
		try {
			return await this.post("/test/pagination", { data });
		} catch (error) {
			console.log(error);
			if (error instanceof AxiosError) {
				throw "Unable to get tests";
			}
			throw error;
		}
	}
	async getFilterData(from?: string) {
		try {
			return await this.get<"/tags/filterdata">("/tags/filterdata", {
				from: from ?? "",
			});
		} catch (ex) {
			console.log(ex);
			throw "Unable to get filter data";
		}
	}
	async cloneQuiz(
		testId: string,
		data: EndPointRequestBody<PostEndPointsMap["/test/cloneQuiz/:id"]>
	) {
		try {
			const response = await this.post<"/test/cloneQuiz/:id">(
				`/test/cloneQuiz/${testId}`,
				{ data }
			);
			if ("error" in response) {
				throw response.error;
			}
			return response;
		} catch (ex) {
			console.log(ex);
			if (ex instanceof AxiosError) {
				throw "Unable to clone test";
			}
			throw ex;
		}
	}
	async getQuestions(
		data: EndPointRequestBody<PostEndPointsMap["/quest/pagination"]>
	) {
		try {
			return await this.post("/quest/pagination", { data });
		} catch (error) {
			console.log(error);
			if (error instanceof AxiosError) {
				throw "Unable to get questions";
			}
			throw error;
		}
	}

	async sendOtp(id: string) {
		return await this.get<"/quiz-api/otpForForceSubmission/:id">(
			`/quiz-api/otpForForceSubmission/${id}`
		);
	}

	async deleteQuiz(id: string) {
		try {
			const res = await this.get<"/test/delquiz/:id">(
				`/test/delquiz/${id}`
			);
			if ("error" in res) {
				throw res.error;
			}
			return res;
		} catch (ex) {
			console.log(ex);
			if (ex instanceof AxiosError) {
				throw "Unable to delete test";
			}
			throw ex;
		}
	}

	async makeTemplate(
		data: EndPointRequestBody<PostEndPointsMap["/test/makeTemplate"]>
	) {
		try {
			const res = await this.post("/test/makeTemplate", { data });
			if ("error" in res) {
				throw res.error;
			}
			return res;
		} catch (error) {
			console.log(error);
			if (error instanceof AxiosError) {
				throw "Unable to make template";
			}
			throw error;
		}
	}

	async deleteQuestion(quesId: string) {
		try {
			const res = await this.get<"/quest/delques/:id">(
				`/quest/delques/${quesId}`
			);
			if ("error" in res) {
				throw res.error;
			}
			return res;
		} catch (ex) {
			console.log(ex);
			if (ex instanceof AxiosError) {
				throw "Unable to delete question";
			}
			throw ex;
		}
	}

	async cloneQuestion(id: string) {
		try {
			const res = await this.post<"/quest/cloneQuest/:id">(
				`/quest/cloneQuest/${id}`
			);
			if ("error" in res) {
				throw res.error;
			}
			return res;
		} catch (ex) {
			console.log(ex);
			if (ex instanceof AxiosError) {
				throw "Unable to clone question";
			}
			throw ex;
		}
	}

	async lockQuestion(id: string, isLocked: boolean) {
		try {
			const res = await this.get<"/quest/lockQuestion">(
				"/quest/lockQuestion",
				{
					quesId: id,
					isLocked: isLocked ? 1 : 0,
				}
			);
			if ("error" in res) {
				throw res.error;
			}
			return res;
		} catch (ex) {
			console.log(ex);
			if (ex instanceof AxiosError) {
				throw "Unable to lock question";
			}
			throw ex;
		}
	}

	async publishQuestion(id: string, checked: boolean) {
		try {
			const queryParams: { questionId: string; isPublic?: boolean } = {
				questionId: id,
			};
			if (checked) queryParams.isPublic = true;

			const res = await this.get<"/quest/updateQuestion">(
				"/quest/updateQuestion",
				queryParams
			);
			if ("error" in res) {
				throw res.error;
			}
			return res;
		} catch (ex) {
			console.log(ex);
			if (ex instanceof AxiosError) {
				throw "Unable to publish question";
			}
			throw ex;
		}
	}

	async getTokenForVideoDownload(quizId: string, userId: string) {
		try {
			const res =
				await this.get<"/test/getJWTForDownload/:quizId/:userId">(
					`/test/getJWTForDownload/${quizId}/${userId}`
				);
			return res;
		} catch (ex) {
			console.log(ex);
			throw "Unable to get video download token";
		}
	}

	async freezeQuiz(id: string) {
		try {
			const res = await this.get<"/test/freezeQuiz/:id">(
				`/test/freezeQuiz/${id}`
			);
			return res;
		} catch (ex) {
			console.log(ex);
			throw "Unable to freeze quiz";
		}
	}

	async unFreezeQuiz(id: string) {
		try {
			const res = await this.get<"/test/unfreezeQuiz/:id">(
				`/test/unfreezeQuiz/${id}`
			);
			return res;
		} catch (ex) {
			console.log(ex);
			throw "Unable to unfreeze quiz";
		}
	}
	async addTestCase(
		data: EndPointRequestBody<PostEndPointsMap["/quest/addTestCase"]>
	) {
		try {
			return await this.post("/quest/addTestCase", { data });
		} catch (error) {
			console.log(error);
			throw "Unable to get questions";
		}
	}
	async deleteTestCase(
		data: EndPointRequestBody<PostEndPointsMap["/quest/deleteTestCase"]>
	) {
		try {
			return await this.post("/quest/deleteTestCase", { data });
		} catch (error) {
			console.log(error);
			throw "Unable to get questions";
		}
	}
	async getInitialQuest(quesId?: string) {
		try {
			const url = quesId ? `/quest/add/${quesId}` : "/quest/add";
			const res = await this.get(url as keyof GetEndPointsMap);

			return res as QuestionAddRoot | QuestionEditRoot;
		} catch (ex) {
			console.log(ex);
			if (ex instanceof AxiosError) {
				throw "This question is locked and cannot be edited";
			}
			throw ex;
		}
	}
	async questAdd(
		data: QuestionAddReqBodyDataType,
		quesId?: string,
		quizId?: string,
		segmentIndex?: string
	) {
		try {
			const formData = new FormData();
			for (const [fieldName, fieldValue] of Object.entries(data)) {
				if (fieldValue === null || fieldValue === undefined) {
					formData.set(fieldName, "");
					continue;
				}
				if (fieldName === "upldFile" && Array.isArray(fieldValue)) {
					fieldValue.forEach(file => {
						if (file.originFileObj) {
							formData.append(fieldName, file.originFileObj);
						}
					});
				} else {
					formData.append(fieldName, fieldValue);
				}
			}

			return await this.postFormData<"/quest/add/:id">(
				`/quest/add/${quesId ?? ""}`,
				{
					data: formData,
					params: { quizId, segmentIndex },
				}
			);
		} catch (ex) {
			console.log(ex);
			if (ex instanceof AxiosError) {
				throw `Unable to ${quesId ? "update" : "add"} question.`;
			}
			throw ex;
		}
	}
	async questRemoveFile(fileName: string, quesId: string) {
		try {
			return await this.post<"/quest/removeFile">(`/quest/removeFile`, {
				data: { fileName, quesId },
			});
		} catch (error) {
			console.log(error);
			throw "Unable to get questions";
		}
	}

	async exportQuestion(quesId: string) {
		try {
			return await this.get<"/quest/exportQuestion/:quesId">(
				`/quest/exportQuestion/${quesId}`
			);
		} catch (error) {
			console.log(error);
			throw "Unable to add/update questions";
		}
	}
	async getTestLink(id: string) {
		try {
			const res = await this.get<"/test/viewLink/:id">(
				`/test/viewLink/${id}`
			);
			return res;
		} catch (ex) {
			console.log(ex);
			throw "Unable to get test link";
		}
	}

	async deleteQuestions(
		data: EndPointRequestBody<PostEndPointsMap["/quest/delques"]>
	) {
		try {
			const res = await this.post("/quest/delques", { data });
			return res;
		} catch (ex) {
			console.log(ex);
			throw "Unable to delete questions";
		}
	}
	async saveSolution(questionId: string, data: Record<number, string>) {
		try {
			return await this.post<"/quest/updateSolution">(
				"/quest/updateSolution",
				{
					data: {
						quesId: questionId,
						multiLanguage: data,
					},
				}
			);
		} catch (error) {
			console.log(error);
			throw "Unable to save solution";
		}
	}

	async getRoomData(id: string) {
		try {
			const res = await this.get<"/meeting/rooms/:id">(
				`/meeting/rooms/${id}`
			);
			if ("error" in res) {
				throw res.error;
			}
			return res;
		} catch (ex) {
			console.log(ex);
			if (ex instanceof AxiosError) {
				throw "Unable to get room data";
			}
			throw ex;
		}
	}
	async uploadBulkRoom(id: string, data: BulkRoomReqDataType[]) {
		try {
			const body = { finalData: data };
			return await this.post<"/test/bulkUploadRoom/:id">(
				`/test/bulkUploadRoom/${id}`,
				{ data: body }
			);
		} catch (error) {
			console.log(error);
			throw "Unable to upload bulk data";
		}
	}

	async updateQuizTemplateAccess(id: string, data: string) {
		try {
			const payload = { isPublic: data, quizId: id };
			return await this.post<"/test/quizTemplateAccess">(
				"/test/quizTemplateAccess",
				{ data: payload }
			);
		} catch (ex) {
			console.log(ex);
			throw "Unable to update access";
		}
	}
	async updateUserQuestionScore(props: {
		additionalScore: number;
		quesId: string;
		quizId: string;
		userId: string;
	}) {
		const { additionalScore, quesId, quizId, userId } = props;
		const formData = new FormData();
		formData.append("additionalScore", additionalScore.toString());
		try {
			return await this.post<"test/addmarks/:quizId/:quesId/:userId">(
				`test/addmarks/${quizId}/${quesId}/${userId}`,
				{
					data: formData,
				}
			);
		} catch (ex) {
			console.log(ex);
			throw "Unable to update user question score";
		}
	}
	async getUpdatedScoreActivity(props: {
		questionId: string;
		quizId: string;
		userId: string;
	}) {
		try {
			const res = await this.post<"api/getScoreUpdationActivity">(
				`api/getScoreUpdationActivity`,
				{
					data: props,
				}
			);
			return res;
		} catch (ex) {
			console.log(ex);
			if (ex instanceof AxiosError) {
				throw "Unable to get room data";
			}
			throw ex;
		}
	}

	async toggleIsValid(
		data: EndPointRequestBody<
			PostEndPointsMap["/test/quizEmail/toggleIsValid"]
		>
	) {
		try {
			return await this.post<"/test/quizEmail/toggleIsValid">(
				"/test/quizEmail/toggleIsValid",
				{ data }
			);
		} catch (ex) {
			console.log(ex);
			throw data.isValid
				? "Unable to enable test link"
				: "Unable to disable test link";
		}
	}

	async removeUser(
		data: EndPointRequestBody<
			PostEndPointsMap["test/quizEmail/deleteEmail"]
		>
	) {
		try {
			return await this.post("test/quizEmail/deleteEmail", { data });
		} catch (error) {
			console.log(error);
			throw "Unable to remove user";
		}
	}

	async getLearnerDashBoard() {
		try {
			const res = await this.get<"/user-dashboard/user-quiz?fromRedis=1">(
				`/user-dashboard/user-quiz?fromRedis=1`
			);
			if ("error" in res) {
				throw res.error;
			}
			return res;
		} catch (ex) {
			console.log(ex);
			throw ex;
		}
	}
	async updateExpireTime(
		emailId: string,
		quizId: string,
		updatedTime: string
	) {
		try {
			const res = await this.post<"/test/quizEmail/updateExpireTime">(
				`/test/quizEmail/updateExpireTime`,
				{
					data: { emailId, quizId, updatedTime },
				}
			);
			return res;
		} catch (ex) {
			console.log(ex);
			if (ex instanceof AxiosError) {
				throw "Unable to get room data";
			}
			throw ex;
		}
	}
	async getReportAproveLink(id: string, getJWTOnly: boolean = false) {
		try {
			const res =
				await this.get<"/test/getJWTForMarkingQuizAsVerified/:id">(
					`/test/getJWTForMarkingQuizAsVerified/${id}`
				);
			if ("error" in res) {
				throw res.error;
			}
			const jwt = res.token;
			if (jwt) {
				if (!getJWTOnly) {
					const inbetweenURL = new URL(window.location.href);
					inbetweenURL.searchParams.forEach((_value, key) => {
						inbetweenURL.searchParams.delete(key);
					});
					return `${inbetweenURL.toString()}/${jwt}`;
				}
				return jwt;
			}
		} catch (ex) {
			console.log(ex);
			if (ex instanceof AxiosError) {
				throw "Unable to get JWT";
			}
			throw ex;
		}
	}
	async markQuizAsChecked(quizId: string, formData: FormData) {
		try {
			const jwt = await this.getReportAproveLink(quizId, true);
			return await this.postFormData<"/test/markQuizAsChecked/:jwt">(
				`/test/markQuizAsChecked/${jwt}`,
				{ data: formData }
			);
		} catch (ex) {
			console.log(ex);
			if (ex instanceof AxiosError) {
				throw "Unable to mark quiz as checked";
			}
			throw ex;
		}
	}
	async markReportAsUnresolved(id: string) {
		try {
			const res = await this.get<"/test/markReportAsUnresolved/:id">(
				`/test/markReportAsUnresolved/${id}`
			);
			if ("error" in res) {
				throw res.error;
			}
			return res;
		} catch (ex) {
			console.log(ex);
			if (ex instanceof AxiosError) {
				throw "Unable to get JWT";
			}
			throw ex;
		}
	}
	async getOtpWaitTime(id: string) {
		try {
			const res = await this.get<"/quiz-api/otpwaittime/:id">(
				`/quiz-api/otpwaittime/${id}`
			);
			if ("error" in res) {
				throw res.error;
			}
			return res;
		} catch (ex) {
			console.log(ex);
			if (ex instanceof AxiosError) {
				throw "Unable to get OTP wait time";
			}
			throw ex;
		}
	}
	async shareTest(emails: string[], quizId: string) {
		try {
			const res = await this.post<"/api/sendQuizClone">(
				`/api/sendQuizClone`,
				{
					data: { emails, quizId },
				}
			);
			return res;
		} catch (ex) {
			console.log(ex);
			if (ex instanceof AxiosError) {
				throw "Unable to Share Test";
			}
			throw ex;
		}
	}
	async getCloneQuizByToken(token: string): Promise<TestData> {
		try {
			const { quiz, alreadyClonedThroughThisLink } =
				await this.get<"/test/cloneQuizByToken/:token">(
					`/test/cloneQuizByToken/${token}`
				);
			console.log({ quiz });
			let link;
			try {
				const response = await this.get<"/test/viewLink/:id">(
					`/test/viewLink/${quiz._id}`
				);
				link = response.link;
			} catch (ex) {
				console.log(ex);
			}
			console.log(link, AppConfig.testAttemptUrlBase);
			return {
				_id: quiz._id,
				progressStep: quiz.progressStep ?? TestSaveProgressStep.GENERAL,
				isFrozen: quiz.isFrozen ?? false,
				isTemplate: quiz.isTemplate ?? false,
				alreadyClonedThroughThisLink:
					alreadyClonedThroughThisLink ?? false,
				tabs: {
					general: {
						title: quiz.title,
						quizTime: +quiz.quizTime,
						instructions: quiz.instructions,
					},
					access: {
						isNotPrivate: !quiz.isPrivate,
						link: (link ?? Date.now().toString()).replace(
							AppConfig.testAttemptURL + "/",
							""
						),
						quizCode: quiz.quizCode,
						startTime: dayjs(quiz.startTime),
						...(quiz.endTime
							? { endTime: dayjs(quiz.endTime) }
							: {}),
						...(quiz.entryStopTime
							? { entryStopTime: dayjs(quiz.entryStopTime) }
							: {}),
						allowedIP:
							quiz.allowedIP
								?.split(",")
								.filter(text => text !== "") ?? [],
						showResults: quiz.showResults,
					},
					content: {
						cutOffMarks: quiz.cutOffMarks,
						languagesAllowed: quiz.languagesAllowed,
						keywords:
							quiz.keywords
								?.split(",")
								.filter(text => text !== "") ?? [],
						randomQuestions: quiz.poolQuestion,
						shuffleQuestions: quiz.randomizeQuestion,
						revisitSections: quiz.revisitAllowed,
						ShuffleMCQOptions: quiz.toShuffleMCQOptions ?? false,
						customFields: (quiz.quizUserDetails ?? []).map(
							field => ({
								key: field.fieldName,
								label: field.fieldLabel,
								type: field.fieldType,
								active: field.fieldIsSelected,
								values: field.fieldOptions as string[],
								editable: !defaultFieldKeys.has(
									field.fieldName
								),
							})
						),
					},
					proctoring: {
						copyPasteAllowed: quiz.copyPasteAllowed,
						isFullScreen: quiz.isFullScreen,
						preventTabSwitch: !quiz.tabSwitchAllowed as false,
						tabSwitchAlertLimit: quiz.tabSwitchAlertLimit,
						submitTestOnTabSwitchLimitBreach:
							quiz.submitTestOnTabSwitchLimitBreach,
						isWebCamAllowed: quiz.isWebCamAllowed as true,
						isLiveStreamEnabled: quiz.isLiveStreamEnabled,
						isAppOnly: quiz.isAppOnly as true,
						allowClose: quiz.allowClose,
						isVmDetectionEnabled: quiz.isVmDetectionEnabled,
						isTerminateStudentEnable:
							quiz.toTerminateTestOnVmDetection,
						isRecordingEnabled: quiz.isRecordingEnabled,
						isRecordingUploadBlockingEnabled:
							quiz.isRecordingUploadBlockingEnabled,
						onlySuspiciousRoom: quiz.onlySuspiciousRoom,
						isSignUpAllowed: quiz.isSignUpAllowed ?? false,
					},

					notifications: {
						"candidate.sendEmail":
							(quiz.quizEmailSetting?.sendMail ?? false) &&
							Boolean(
								quiz.quizEmailSetting?.testCompletionStudentMail
							),
						"candidate.message":
							quiz.quizEmailSetting?.mailTemplate.msg?.join("") ??
							"",
						"candidate.subject":
							quiz.quizEmailSetting?.mailTemplate.subject ?? "",
						"organisation.sendEmail":
							(quiz.quizEmailSetting?.sendMail ?? false) &&
							Boolean(
								quiz.quizEmailSetting
									?.quizCompletionIntimationSetting
							),
						"organisation.message":
							quiz.quizEmailSetting?.intimationMailTemplate.msg?.join(
								""
							) ?? "",
						"organisation.subject":
							quiz.quizEmailSetting?.intimationMailTemplate
								.subject ?? "",
					},
				},
			};
		} catch (ex) {
			console.log(ex);
			if (ex instanceof AxiosError) {
				throw "Unable to get Test Data";
			}
			throw ex;
		}
	}
	async getContentWithToken(token: string): Promise<TestContent> {
		try {
			const { quiz, isFrozen, previewOnly, contentModify } =
				await this.get<"/test/contentWithToken">(
					"/test/contentWithToken",
					{ token }
				);
			const questionsArray = quiz.quizContentArray ?? [];
			const sectionsArray = quiz.quizObj.quizSegments ?? [];
			const testScore = questionsArray.reduce(
				(result, question) => result + question.score,
				0
			);
			const sections: TestContent["sections"] = [];
			let questionsIterationCounter = 0;
			console.log(questionsArray);
			for (const [, section] of sectionsArray.entries()) {
				const startIndex = questionsIterationCounter;
				const endIndex = questionsIterationCounter + section.count;
				const typesCount: {
					[K in
						| Exclude<TestContentResponse["type"], "question">
						| TestContentResponse["qtype"]]: number;
				} = {
					tutorial: 0,
					[QuestionType.MCQ]: 0,
					[QuestionType.SUBJECTIVE]: 0,
					[QuestionType.MULTIPLE]: 0,
					[QuestionType.CODING]: 0,
					[QuestionType.WEB]: 0,
				};
				// let segmentContentLength = 0;
				// let sectionScore = 0;
				const questions: TestContentQuestionType[] = [];
				for (let q = startIndex; q < endIndex; q++) {
					if (q >= questionsArray.length) {
						break;
					}
					// segmentContentLength++;
					const question = questionsArray[q];
					switch (question.type) {
						case "tutorial":
							typesCount.tutorial++;
							break;
						case "question":
							// sectionScore += question.score;
							typesCount[question.qtype]++;
					}
					questions.push({
						_id: question._id,
						score: question.score,
						title: question.title,
						createdBy: question.createdBy,
						orgId: question.orgId,
						...(question.qtype === QuestionType.CODING
							? {
									type: question.qtype,
									isOnCloud: Boolean(question.isOnCloud),
									settings: {
										result: question.executionType,
										contentHead: question.showTail,
										contentTail: question.showTail,
										customInput: question.showCustomInput,
									},
								}
							: { type: question.qtype }),
					});
				}
				questionsIterationCounter += section.count;
				sections.push({
					key: section._id,
					title: section.title,
					questions,
					description: section.desc,
					poolCount: section.pollNumber ?? 0,
					isPoolQuestion: quiz.quizObj.poolQuestion,
				});
			}

			return {
				sections,
				testScore,
				isFrozen,
				isPreviewOnly: previewOnly,
				canModifyContent: contentModify,
			};
		} catch (ex) {
			console.log(ex);
			if (ex instanceof AxiosError) {
				throw "Unable to get Test Data";
			}
			throw ex;
		}
	}
	async cloneQuizByToken(
		data: EndPointRequestBody<PostEndPointsMap["/test/cloneQuizByToken"]>
	) {
		try {
			const res = await this.post<"/test/cloneQuizByToken">(
				`/test/cloneQuizByToken`,
				{
					data,
				}
			);
			return res;
		} catch (ex) {
			console.log(ex);
			if (ex instanceof AxiosError) {
				throw "Unable to Create Clone";
			}
			throw ex;
		}
	}
	async getQuesPreviewData(id: string) {
		try {
			const res = await this.get<"/quest/preview/:id">(
				`/quest/preview/${id}`
			);
			return res as QuestionEditRoot;
		} catch (er) {
			console.log(er);
			throw "Something went wrong";
		}
	}
	async downloadTestReport(id: string) {
		try {
			const response = await this.get<"/test/download/:id">(
				`/test/download/${id}`
			);
			console.log(response);
		} catch (er) {
			console.log(er);
			throw er;
		}
	}
}

const convertToArray = (string: string) => {
	if (!string) {
		return "";
	}
	const newString = string.split(/<[A-z]*.>/g);
	const newString2 = string.match(/<.*?>/g);
	const finalArr = [];
	for (let i = 0; i < newString.length; i++) {
		if (newString && newString[i]) finalArr.push(newString[i]);
		if (newString2 && newString2[i]) {
			newString2[i] = newString2[i].replace(/&gt;/g, ">");
			newString2[i] = newString2[i].replace(/&lt;/g, "<");
			finalArr.push(newString2[i]);
		}
	}

	return finalArr;
};
