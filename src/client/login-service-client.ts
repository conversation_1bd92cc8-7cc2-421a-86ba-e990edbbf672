import axios, {
	AxiosError,
	AxiosInstance,
	AxiosRequestConfig,
	AxiosResponse,
} from "axios";

export class LoginServiceClient {
	private __mainApi: AxiosInstance;
	#origin;
	constructor(url: string) {
		this.#origin = url;
		this.__mainApi = axios.create({
			withCredentials: true,
			headers: {
				"Content-Type": "application/json",
				"X-Requested-From": "app-0.0.1",
			},
			baseURL: url,
		});
	}

	get origin() {
		return this.#origin;
	}

	protected async fetch<T, D = unknown>(
		config: AxiosRequestConfig<D> & { data?: D }
	) {
		try {
			const response: AxiosResponse<T> = await this.__mainApi(config);
			return response;
		} catch (error) {
			if (error instanceof AxiosError === false) {
				throw error;
			}
			if (error.status === 401) {
				// window.location.reload();
			}
			throw error;
		}
	}

	async getActiveEmails() {
		try {
			const response = await this.fetch<
				Array<{
					_id: string;
					count: number;
				}>
			>({
				method: "GET",
				url: "api/email/graph?limit=7",
			});
			return response.data;
		} catch (err) {
			console.error(err);
			throw "Failed to get Email Activity";
		}
	}

	async getEmailSentCount() {
		try {
			const response = await this.fetch<{
				currentCount?: number;
			}>({
				method: "GET",
				url: "api/email/currentCount",
			});
			return response.data?.currentCount;
		} catch (err) {
			console.error(err);
			throw "Failed to get Email Activity";
		}
	}
}
