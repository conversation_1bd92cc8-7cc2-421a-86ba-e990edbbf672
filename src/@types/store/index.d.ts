import { InviteCandidateTestResponse } from "@/client/test-add";
import { AllowedCandidates } from "@/testReport/data/data";
import { InvitedUsers } from "@/testReport/testReport";

interface TimeStampData<T> {
	timeStampsArr: T[];
}

type TestStoreState = {
	quizData: { _id: string }[];
	getQuiz(id: string): Promise<Quiz>;
	checkLinkAvailability(name: string): Promise<boolean>;
	allowCandidates: (
		quizId: string,
		allowedEmailIds: string[]
	) => Promise<void>;
	allowedCandidates: Record<string, AllowedCandidates[]>;
	getTestAllowedCandidate(testId: string): AllowedCandidates[];
	inviteCandidates(
		testId: string,
		data: {
			emailIds: string;
			mailContent: string;
			mailSubject: string;
			expireTime?: string;
		}
	): Promise<InviteCandidateTestResponse | void>;
	invitedCandidates: Record<string, InvitedUsers[]>;
	getTestInvitedCandidate(testId: string): InvitedUsers[] | void;
};

type TestFilterState = {
	searchValue: string;
};

type ReportFilterState = {
	searchValue: string;
	dropDownValue: number;
	reportFilters: {
		questionWiseMarks: boolean;
		activeField: Array<string>;
		marksRange:
			| {
					min: number | null;
					max: number | null;
			  }
			| null
			| undefined;
		startTimeRange: {
			start: string | null;
			end: string | null;
		};
		multipleEmailSearch: string[];
		currentPage: number;
		pageSize: number;
	};
	attemptFilter: {
		attemptType: number;
		multipleEmailSearch: string[];
		currentPage: number;
		pageSize: number;
	};
	allowedCandidateFilter: {
		attemptType: number;
		multipleEmailSearch: string[];
		currentPage: number;
		pageSize: number;
	};
};
