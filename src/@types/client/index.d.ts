import type { Socket } from "socket.io-client";

interface TestCase {
	codeproginputparams: string;
	codeprogexpectedoutput: string;
	attemptInMultiLine: boolean;
	sampleTest: boolean;
	scoreip: number;
	_id: string;
	onCloud?: boolean;
}

type ListeningEvent = {
	connect: () => void;
	disconnect: () => void;
	auth_succeed: () => void;
	compile: (data: {
		output: string;
		langId: string;
		errors: string;
		time: string;
		txtCodeAt: string;
		codeId: string;
		progLang: string;
		outputArray: Array<{
			userOutput: string;
			testCasePassed: boolean;
			score: number;
		}>;
		testCase: Array<TestCase>;
	}) => void;
	completed: (data: { error: string }) => unknown;
};

type EmitEvents = {
	submit_all_test: (
		quizId: string,
		otp: string,
		userId?: Array<string>
	) => void;
	compile: (data: {
		language: string;
		code: string;
		questionId: string;
		stdin: string;
		isStepwise?: string;
		stepwiseUserInput?: sring;
		isInvalidAttempt?: boolean;
		errors?: string;
		html?: string;
		css?: string;
		js?: string;
		isCustomInput?: boolean;
	}) => void;
};

type CQSocket = Socket<ListeningEvent, EmitEvents>;
