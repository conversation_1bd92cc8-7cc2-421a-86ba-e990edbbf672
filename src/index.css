* {
	margin: 0px;
	padding: 0px;
	box-sizing: border-box;
}

html,
body,
#root {
	width: 100%;
	height: 100%;
}

body {
	background-color: #fbfbfb;
}

.quill {
	border: 1px solid #e0e0e0;
	border-radius: 0.5rem;
	background-color: #fbfbfb;
	transition: all 0.2s;
}
.quill:hover {
	border-color: #de6834;
}
.quill:focus-within {
	border-color: #de6834;
	box-shadow: 0 0 0 2px rgba(255, 247, 239, 0.89);
}

.ql-toolbar.ql-snow,
.ql-container.ql-snow {
	border-color: #e0e0e0 !important;
	border-width: 0px !important;
	border-bottom-width: 1px !important;
}
.ant-tabs {
	height: 100%;
}

.ant-tabs-content-holder {
	display: flex;
}
.ant-table-placeholder {
	z-index: 0 !important;
}

.solution .ace_gutter {
	width: 63px !important;
}
.solution .ace_gutter-layer {
	width: 63px !important;
}

.solution .ace_gutter {
	width: 63px !important;
}
.solution .ace_scroller {
	left: 63px !important;
}
.solution .ace_gutter-cell {
	padding-left: 0px;
}
.read-only-ace .ace_cursor {
	opacity: 0;
}
.read-only-ace .ace_content {
	background-color: #f7f7f7;
}
.read-only-ace .ace_scroller {
	background-color: #f7f7f7;
}

.expanded-row-no-z-index {
	z-index: unset !important;
}

div.fancy-divider {
	display: flex;
}

div.fancy-divider > div:nth-child(1) {
	flex: 1 1 20%;
	background-color: var(--primary-lighter);
}

div.fancy-divider > div:nth-child(2) {
	flex: 1 1 30%;
	background-color: var(--primary-medium);
}

div.fancy-divider > div:nth-child(3) {
	flex: 1 1 50%;
	background-color: var(--primary-color);
}

div.fancy-divider {
	display: flex;
}

div.fancy-divider > div:nth-child(1) {
	flex: 1 1 20%;
	background-color: var(--primary-lighter);
}

div.fancy-divider > div:nth-child(2) {
	flex: 1 1 30%;
	background-color: var(--primary-medium);
}

div.fancy-divider > div:nth-child(3) {
	flex: 1 1 50%;
	background-color: var(--primary-color);
}
