import React, { useCallback, useEffect, useState } from "react";
import { AxiosError } from "axios";
import { useTestStore } from "@store/index";

type QuizParams = {
	id: string;
};

export const Quiz: React.FC<QuizParams> = ({ id }) => {
	const [loading, setLoading] = useState<boolean>(false);
	const [error, setError] = useState<string>("");
	const [quiz, setQuizDetails] = useState<{ [key: string]: string } | null>(
		null
	);

	const quizGetter = useTestStore(state => state.getQuiz);

	const fetchQuizContent = useCallback(
		async (id: string) => {
			setLoading(true);
			setError("");
			try {
				const quiz = await quizGetter(id);
				setQuizDetails(quiz);
			} catch (error) {
				console.error(error);
				if (error instanceof AxiosError) {
					setError(error.message);
				} else if (error instanceof Error) {
					setError(error.message);
				} else {
					setError("Something went wrong");
				}
			} finally {
				setLoading(false);
			}
		},
		[quizGetter]
	);

	useEffect(() => {
		fetchQuizContent(id);
	}, [id, fetchQuizContent]);

	useEffect(() => {
		console.log(quiz);
	}, [quiz]);

	if (loading) {
		return <div>Loading</div>;
	}

	if (error) {
		return <div>{error}</div>;
	}

	return <div></div>;
};
