import { Divider, Layout } from "antd";
import Header from "@/components/header/Header";
import { Outlet } from "react-router";
import PageHelmet from "../../../components/page-helmet/PageHelmet";
import { AppConfig } from "@/config";
import { PlatformOrganisation, platformTitle } from "@/constants";

export default function MainScreen() {
	const { platformOrganisation } = AppConfig;
	return (
		<>
			<PageHelmet
				title={
					platformOrganisation === PlatformOrganisation.CHITKARA
						? platformTitle[1]
						: platformTitle[0]
				}
				suffix=""
			/>
			<Layout style={{ height: "100%" }}>
				<Layout.Header
					style={{ backgroundColor: "#f8f8f8", padding: 0 }}
				>
					<Header />
				</Layout.Header>
				<Divider style={{ margin: "0px" }} />
				<Layout.Content style={{ height: "100%", overflow: "auto" }}>
					<Outlet />
				</Layout.Content>
			</Layout>
		</>
	);
}
