import { ValidatorRule } from "rc-field-form/lib/interface";

export const testTitleRegex = /^[a-zA-Z0-9-@,&/.()[\]\s+]{1,125}$/;
export const testEmailRegex =
	/^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;

export const testTitleValidator: ValidatorRule["validator"] = (
	_rule,
	value: string
) => {
	if (!value) {
		return Promise.reject("Test title is required");
	}
	if (!testTitleRegex.test(value)) {
		return Promise.reject("Please enter valid Test Name");
	}
	return Promise.resolve();
};

export const validateEmails: ValidatorRule["validator"] = (
	_rule: unknown,
	value: string[]
) => {
	if (!value?.length) {
		return Promise.reject(
			new Error("Please enter at least one e-mail address!")
		);
	}

	for (const email of value) {
		if (!testEmailRegex.test(email)) {
			return Promise.reject(
				new Error(`"${email}" is not a valid e-mail address!`)
			);
		}
	}

	return Promise.resolve();
};
