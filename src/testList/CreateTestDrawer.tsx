import { <PERSON><PERSON>, Card, Drawer, Form, Input, Select, message } from "antd";
import { useCallback, useEffect, useState } from "react";
import { useNavigate } from "react-router";
import { testTitleValidator } from "./common/common";
import { quizClient, useAppStore } from "../store";
import { RoleAction, RoleResource } from "@/constants/roles";

interface TestTemplate {
	_id: string;
	title: string;
}

interface CreateTestDrawerProps {
	open: boolean;
	onClose: () => void;
	onFinish: () => void;
}

function CreateTestDrawer({ open, onClose, onFinish }: CreateTestDrawerProps) {
	const navigate = useNavigate();
	const [submitLoading, setSubmitLoading] = useState<boolean>(false);
	const [loading, setLoading] = useState<boolean>(false);
	const [messageInstance, contextHolder] = message.useMessage({
		maxCount: 1,
	});
	const [testTemplatesData, setTestTemplateData] = useState<TestTemplate[]>(
		[]
	);
	const fetchTemplates = useCallback(async () => {
		try {
			setLoading(true);
			const res = await quizClient.getTestTemplate();
			if (!res) {
				messageInstance.error("Failed to fetch test template data.");
				return;
			}
			if ("error" in res) {
				messageInstance.error(res.error);
			} else {
				setTestTemplateData(res.templateQuizArr);
			}
			return;
		} catch (err) {
			messageInstance.error(err as string);
		} finally {
			setLoading(false);
		}
	}, [messageInstance]);

	const hasResourcePermission = useAppStore(
		state => state.hasResourcePermission
	);

	async function handleFinishForTestTemplate(
		title: string,
		template?: string
	) {
		try {
			const testId = template;
			const payload = {
				title,
			};
			if (testId) {
				const res = await quizClient.cloneQuiz(testId, payload);
				return res._id;
			} else {
				messageInstance.error("Test ID is undefined.");
				return;
			}
		} catch (err) {
			messageInstance.error(err as string);
		}
	}

	useEffect(() => {
		setSubmitLoading(true);
		if (hasResourcePermission(RoleResource.QUIZ, RoleAction.ADD)) {
			fetchTemplates();
		}
		setSubmitLoading(false);
	}, [fetchTemplates, hasResourcePermission]);

	return (
		<>
			{contextHolder}
			<Drawer
				closable={!submitLoading}
				destroyOnClose
				loading={loading}
				title={<p>Create Test</p>}
				open={open}
				maskClosable={!submitLoading}
				onClose={onClose}
			>
				<Card
					bordered={false}
					style={{
						color: "hsl(18.4, 72%, 53.7%)",
						marginBottom: "1rem",
					}}
				>
					<svg
						viewBox="0 0 247.927 158.856"
						width="200"
						style={{ opacity: 0.15 }}
					>
						<use href="/Group2-1.svg#bg"></use>
					</svg>
					<svg viewBox="0 0 247.927 158.856" width="110">
						<use href="/Group2.svg#test"></use>
					</svg>
				</Card>
				<Form<{ title: string; template?: string }>
					layout="vertical"
					variant="outlined"
					style={{ paddingBlock: 32 }}
					onFinish={async data => {
						setSubmitLoading(true);
						try {
							if (data.title && data.template) {
								const id = await handleFinishForTestTemplate(
									data.title,
									data.template
								);
								if (!id) {
									messageInstance.error(
										"Something went wrong"
									);
									return;
								}
								messageInstance.success("Test Created");
								onFinish();
								navigate(`/tests/${id}`);
							} else {
								onFinish();
								navigate(
									`/tests/add/general?title=${data.title}`
								);
							}
						} catch (error) {
							messageInstance.error(error as string);
						} finally {
							setSubmitLoading(false);
						}
					}}
				>
					<Form.Item
						label="Test Title"
						name="title"
						rules={[
							{
								validator: testTitleValidator,
							},
						]}
					>
						<Input
							placeholder="eg. TechRecruitmentJuly2021"
							maxLength={125}
						/>
					</Form.Item>

					<br />
					<Form.Item
						label="Select Test template"
						name="template"
						rules={[{ required: false }]}
					>
						<Select
							showSearch
							allowClear
							placeholder="Select Template"
							optionFilterProp="label"
							disabled={testTemplatesData.length === 0}
							options={
								testTemplatesData.length > 0
									? testTemplatesData.map(temp => ({
											value: temp._id,
											label: temp.title,
										}))
									: [
											{
												value: "",
												label: "No Data",
											},
										]
							}
							filterOption={(input, option) =>
								(option?.label ?? "")
									.toLowerCase()
									.includes(input.trim().toLowerCase())
							}
						/>
					</Form.Item>

					<br />
					<Form.Item>
						<Button
							type="primary"
							htmlType="submit"
							loading={submitLoading}
						>
							Submit
						</Button>
					</Form.Item>
				</Form>
			</Drawer>
		</>
	);
}

export default CreateTestDrawer;
