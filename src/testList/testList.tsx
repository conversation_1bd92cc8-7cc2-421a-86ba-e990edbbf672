import type { QuizList } from "./data/data";
import {
	Space,
	TableColumnsType,
	Dropdown,
	MenuProps,
	Button,
	TablePaginationConfig,
	Flex,
	Layout,
	theme,
	Badge,
	Row,
	Typography,
	Divider,
	message,
	Empty,
	Tag,
	Tooltip,
} from "antd";
import { <PERSON><PERSON>and<PERSON>tick, Copy, FilterIcon } from "lucide-react";
import { useCallback, useEffect, useMemo, useState } from "react";
import Icon, {
	EditOutlined,
	DeleteOutlined,
	CopyOutlined,
	DownloadOutlined,
	FileOutlined,
	LineChartOutlined,
	FieldTimeOutlined,
	CalendarOutlined,
	UserOutlined,
	MoreOutlined,
	LockOutlined,
	UnlockOutlined,
	FileDoneOutlined,
	GlobalOutlined,
	CheckCircleFilled,
	SortAscendingOutlined,
	SortDescendingOutlined,
	CloseCircleOutlined,
	CaretDownFilled,
} from "@ant-design/icons";
import { Link } from "react-router";
import { DataTable } from "@/components/dataTable/dataTable";
import Search from "antd/es/input/Search";
import Sider from "antd/es/layout/Sider";
import { Content, Header } from "antd/es/layout/layout";
import FilterMenu, { FilterMenuItem } from "@/components/filterMenu/filterMenu";
import { quizClient, useAppStore } from "../store";
import timeDate from "@/utils/timeDate";
import CloneQuiz from "@/components/Menu-Components/CloneQuiz";
import useModal from "antd/es/modal/useModal";
import useLocalStorageState from "@/localStorageState";
import { RoleAction, RoleResource, UserRole } from "@/constants/roles";
import { emptyMessages } from "@/constants/messages";
import { SortOrder } from "antd/es/table/interface";
import { AppConfig } from "@/config";
import { PlatformOrganisation } from "@/constants";
import CreateTestDrawer from "./CreateTestDrawer";
import { TestSaveProgressStep } from "@/client/test-add";
import PageHelmet from "@/components/page-helmet/PageHelmet";
import { hasEditPermission } from "@/utils/editPermission";
const { Text } = Typography;

const KeySvg = () => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		viewBox="0 -960 960 960"
		fill="currentColor"
		style={{
			height: "1em",
			marginRight: "0.75em",
			transform: "scale(1.5)",
		}}
		scale={1.5}
	>
		<path d="M280-280q-83.33 0-141.67-58.28Q80-396.56 80-479.82q0-83.26 58.33-141.72Q196.67-680 280-680q58.31 0 109.46 33 51.16 33 73.92 87H880v160h-80v120H640v-120H463.38q-22.76 54-73.92 87-51.15 33-109.46 33Zm0-40q66 0 106-40.5t48-79.5h246v120h80v-120h80v-80H434q-8-39-48-79.5T280-640q-66 0-113 47t-47 113q0 66 47 113t113 47Zm0-110.77q20.31 0 34.77-14.46 14.46-14.46 14.46-34.77 0-20.31-14.46-34.77-14.46-14.46-34.77-14.46-20.31 0-34.77 14.46-14.46 14.46-14.46 34.77 0 20.31 14.46 34.77 14.46 14.46 34.77 14.46Zm0-49.23Z" />
	</svg>
);

const emptyTableMessage = (
	type: string
): [description: string, image?: React.ReactNode] => {
	return [
		emptyMessages.test[type] ??
			emptyMessages.test.default ??
			emptyMessages.default[type],
	];
};

const textStyle = { fontSize: "small" };

const options = [
	{ value: 1, label: "My Tests", key: 1 },
	{ value: 2, label: "My Organization", key: 2 },
	{ value: 3, label: "Other Tests", key: 3 },
	{ value: 4, label: "My Templates", key: 4 },
	{ value: 5, label: "My Organization Templates", key: 5 },
	{ value: 6, label: "Other Templates", key: 6 },
	{ value: 7, label: "CodeQuotient Templates", key: 7 },
];

type Filters = {
	keywords?: string[];
	CreatedBy?: string[];
	dropdown?: string;
	dateRange?: [string, string];
};

const mapQuizType = (dropDownValue: number): number => {
	const mapping: { [key: number]: number } = {
		1: 0,
		2: 1,
		3: 4,
		4: 3,
		5: 6,
		6: 5,
		7: 7,
	};
	return mapping[dropDownValue] ?? 0;
};

const mapFieldName = (dropDownValue: number): string => {
	const mapping: { [key: number]: string } = {
		1: "myList",
		2: "myOrgList",
		3: "otherList",
		4: "myList",
		5: "myOrgList",
		6: "otherList",
		7: "cqList",
	};
	return mapping[dropDownValue] ?? "myList";
};

const firstColumnNameMap: { [key: string]: string } = {
	title: "Title",
	startTime: "Start Time",
	endTime: "End Time",
};

const firstColumnNames = Object.keys(firstColumnNameMap);

export default function TestList() {
	const [modal, modalContext] = useModal();
	const { user, hasResourcePermission, session } = useAppStore();

	const [tableState, setTableState] = useLocalStorageState<{
		dropDownValue: number;
		pageSize: number;
		currentPage: number;
		searchValue: string;
		sorting: { order?: string; field?: string };
		keywords: string[];
		createdBy: string[];
		dateRange: [string, string];
	}>("testListState", {
		dropDownValue: 1,
		pageSize: 10,
		currentPage: 1,
		searchValue: "",
		sorting: {},
		keywords: [],
		createdBy: [],
		dateRange: ["", ""],
	});

	const {
		token: {
			colorBgContainer,
			colorBorder,
			colorPrimary,
			colorTextDisabled,
		},
	} = theme.useToken();

	const newOptions = useMemo(() => {
		let finalOptions = options;
		if (
			user?.role.id === UserRole.SUB_ADMIN ||
			user?.role.id === UserRole.SUPER_ORG
		) {
			finalOptions = finalOptions.filter(
				item => item.value !== 3 && item.value !== 6
			);
		} else if (user?.role.id === UserRole.FACULTY) {
			finalOptions = finalOptions.filter(
				item => item.value !== 2 && item.value !== 3 && item.value !== 6
			);
		}

		if (user?.role.id === UserRole.ADMIN) {
			finalOptions = finalOptions.filter(item => item.value !== 7);
		}

		if (AppConfig.platformOrganisation === PlatformOrganisation.CHITKARA) {
			finalOptions = finalOptions.map(value => {
				if (value.key === 7) {
					value.label = "Chitkara Templates";
				}
				return value;
			});
		}

		const havePermission = finalOptions.find(
			item => item.value === tableState.dropDownValue
		);
		if (!havePermission) {
			setTableState({
				...tableState,
				dropDownValue: 1,
			});
		}
		return finalOptions;
	}, [setTableState, tableState, user?.role.id]);

	const sortMenu = () => {
		return {
			onClick: ({ key }: { key: string }) => {
				if (key === "clear") {
					setTableState(prev => ({
						...prev,
						sorting: {},
					}));
					return;
				}

				const isSameField = tableState.sorting?.field === key;
				const nextOrder =
					isSameField && tableState.sorting.order === "ascend"
						? "descend"
						: "ascend";

				setTableState(prev => ({
					...prev,
					sorting: {
						field: key,
						order: nextOrder,
					},
				}));
			},
			items: [
				{
					key: "title",
					label: (
						<Tooltip
							title={
								tableState.sorting?.field === "title"
									? tableState.sorting.order === "ascend"
										? "Toggle Descending"
										: "Toggle Ascending"
									: undefined
							}
						>
							Sort by Title
						</Tooltip>
					),
					icon:
						tableState.sorting?.field === "title" ? (
							tableState.sorting.order === "ascend" ? (
								<SortAscendingOutlined
									style={{ color: colorPrimary }}
								/>
							) : (
								<SortDescendingOutlined
									style={{ color: colorPrimary }}
								/>
							)
						) : (
							<SortAscendingOutlined />
						),
				},
				{
					key: "startTime",
					label: (
						<Tooltip
							title={
								tableState.sorting?.field === "startTime"
									? tableState.sorting.order === "ascend"
										? "Toggle Descending"
										: "Toggle Ascending"
									: undefined
							}
						>
							Sort by Start Time
						</Tooltip>
					),
					icon:
						tableState.sorting?.field === "startTime" ? (
							tableState.sorting.order === "ascend" ? (
								<SortAscendingOutlined
									style={{ color: colorPrimary }}
								/>
							) : (
								<SortDescendingOutlined
									style={{ color: colorPrimary }}
								/>
							)
						) : (
							<SortAscendingOutlined />
						),
				},
				{
					key: "endTime",
					label: (
						<Tooltip
							title={
								tableState.sorting?.field === "endTime"
									? tableState.sorting.order === "ascend"
										? "Toggle Descending"
										: "Toggle Ascending"
									: undefined
							}
						>
							Sort by EndTime
						</Tooltip>
					),
					icon:
						tableState.sorting?.field === "endTime" ? (
							tableState.sorting.order === "ascend" ? (
								<SortAscendingOutlined
									style={{ color: colorPrimary }}
								/>
							) : (
								<SortDescendingOutlined
									style={{ color: colorPrimary }}
								/>
							)
						) : (
							<SortAscendingOutlined />
						),
				},
				{
					key: "clear",
					label: "Clear Sort",
					icon: <CloseCircleOutlined />,
				},
			],
		};
	};

	const handleCopyLink = async (id: string) => {
		try {
			const link = await quizClient.getTestLink(id);
			await navigator.clipboard.writeText(link.link);
			messageInstance.success("Link copied successfully");
		} catch (error) {
			messageInstance.error(error as string);
		}
	};

	const handleQuizTemplateAccess = async (id: string, isPublic: boolean) => {
		const data = isPublic ? "0" : "1";
		try {
			await quizClient.updateQuizTemplateAccess(id, data);
			messageInstance.success("Test Access updated successfully");
			fetchData();
		} catch (error) {
			messageInstance.error(error as string);
		}
	};

	const columns: TableColumnsType<QuizList> = [
		{
			key: "Serial Number",
			render: (_val, _record, index) => {
				const { currentPage, pageSize } = tableState;
				return (currentPage - 1) * pageSize + index + 1;
			},
			width: 60,
			onCell: () => ({ style: { verticalAlign: "top" } }),
		},
		{
			title: (
				<Flex
					justify="space-between"
					style={{
						width: "100%",
					}}
				>
					<div>Test Details</div>
					<Dropdown menu={{ ...sortMenu() }} trigger={["hover"]}>
						<Space>
							{firstColumnNames.includes(
								tableState.sorting.field ?? ""
							) ? (
								<>
									<Text
										type="secondary"
										style={{ fontSize: "0.85em" }}
									>
										{
											firstColumnNameMap[
												tableState.sorting.field ?? ""
											]
										}
									</Text>
									{tableState.sorting.order === "ascend" ? (
										<SortAscendingOutlined />
									) : (
										<SortDescendingOutlined />
									)}
								</>
							) : (
								<CaretDownFilled
									style={{ color: colorTextDisabled }}
								/>
							)}
						</Space>
					</Dropdown>
				</Flex>
			),
			dataIndex: "title",
			key: "title",
			sorter: false,
			showSorterTooltip: false,
			sortOrder:
				tableState.sorting?.field === "title"
					? (tableState.sorting.order as SortOrder)
					: undefined,
			width: 500,
			ellipsis: true,
			onCell: () => ({ style: { verticalAlign: "top" } }),
			render: (val, record) => {
				return (
					<>
						<Flex vertical gap={10}>
							<Flex vertical>
								{hasEditPermission(
									{
										createdById: record.createdBy._id,
										orgId: record.orgId,
									},
									session,
									user.role?.id,
									hasResourcePermission
								) ? (
									<Flex
										gap={8}
										style={{ padding: 0 }}
										align="center"
									>
										<Space
											style={{
												width: "90%",
												overflow: "hidden",
												textWrap: "wrap",
											}}
										>
											<Link to={`${record._id}/content`}>
												<Space>
													<span
														style={{
															fontWeight: 500,
														}}
													>
														{val}
													</span>
													{record.isMarkedAsCheck && (
														<CheckCircleFilled
															style={{
																color: "#52c41a",
															}}
														/>
													)}
												</Space>
											</Link>
										</Space>
										{(record.progressStep ?? 0) <
											TestSaveProgressStep.PROCTORING && (
											<Tag
												style={{
													fontSize: "0.8em",
												}}
											>
												Draft
											</Tag>
										)}
									</Flex>
								) : (
									<Space>
										<span
											style={{
												fontWeight: 500,
											}}
										>
											{val}
										</span>
									</Space>
								)}
							</Flex>

							<Flex gap={8}>
								<Text
									style={textStyle}
									type="secondary"
									title={record.createdBy.email}
								>
									<Space>
										<UserOutlined />
										{tableState.dropDownValue === 7
											? "Chitkara"
											: record.createdBy.displayname}
									</Space>
								</Text>
								<Divider
									type="vertical"
									style={{ marginTop: "0.5em" }}
								></Divider>
								<Text type="secondary">
									<Space title="Test Duration">
										<FieldTimeOutlined />
										{record.quizTime}
									</Space>
								</Text>
								<Divider
									type="vertical"
									style={{ marginTop: "0.5em" }}
								></Divider>
								<Space title="Test Code">
									<Text type="secondary">
										<Icon component={KeySvg} />
										<span>{record.quizCode}</span>
									</Text>
								</Space>
							</Flex>

							<Flex gap={5}>
								<Text type="secondary">
									{(() => {
										const startDateTime = new Date(
											record.startTime
										);
										const formattedStartDate =
											startDateTime.toLocaleDateString();
										const formattedStartTime =
											startDateTime.toLocaleTimeString(
												[],
												{
													hour: "2-digit",
													minute: "2-digit",
													hour12: false,
												}
											);

										if (record.endTime) {
											const endDateTime = new Date(
												record.endTime
											);
											const formattedEndDate =
												endDateTime.toLocaleDateString();
											const formattedEndTime =
												endDateTime.toLocaleTimeString(
													[],
													{
														hour: "2-digit",
														minute: "2-digit",
														hour12: false,
													}
												);

											return (
												<Space>
													<CalendarOutlined />
													{`${formattedStartDate} ${formattedStartTime} - ${formattedEndDate} ${formattedEndTime}`}
												</Space>
											);
										}

										return `${formattedStartDate} ${formattedStartTime} - NA`;
									})()}
								</Text>
							</Flex>
						</Flex>
					</>
				);
			},
		},
		{
			title: "Updated At",
			dataIndex: "updatedAt",
			key: "updatedAt",
			sorter: true,
			showSorterTooltip: false,
			align: "center",
			onCell: () => ({ style: { verticalAlign: "top" } }),
			sortOrder:
				tableState.sorting?.field === "updatedAt"
					? (tableState.sorting.order as SortOrder)
					: undefined,
			render: value => {
				const dateAndTime = timeDate(value);
				const [rawDate, time] = dateAndTime.split(" ");
				const date = rawDate.replace(",", "");
				return (
					<Space direction="vertical" align="center">
						<span>{date}</span>
						<span>{time}</span>
					</Space>
				);
			},
		},
		{
			title: "Total Attempts",
			dataIndex: "attemptedUsers",
			key: "attemptedUsers",
			align: "center",
			width: 150,
			onCell: () => ({ style: { verticalAlign: "top" } }),
			render: value => (!value ? 0 : value),
		},
		{
			title: "Questions Count",
			dataIndex: "totalQuestions",
			key: "totalQuestions",
			sorter: true,
			showSorterTooltip: false,
			sortOrder:
				tableState.sorting?.field === "totalQuestions"
					? (tableState.sorting.order as SortOrder)
					: undefined,
			align: "center",
			onCell: () => ({ style: { verticalAlign: "top" } }),
			render: value => (!value ? 0 : value),
		},
		{
			title: "Actions",
			key: "actions",
			width: 100,
			onCell: () => ({ style: { verticalAlign: "top" } }),
			render: (_value, data) => {
				const menuItems: MenuProps["items"] = [
					hasResourcePermission(RoleResource.QUIZ, RoleAction.REPORT)
						? {
								key: "Reports",
								label: (
									<Link to={`${data._id}/report`}>
										Reports
									</Link>
								),
								icon: <ChartCandlestick size={15} />,
							}
						: null,
					{
						type: "divider",
					},
					data.isPrivate
						? null
						: {
								key: "Copy",
								label: "Copy Link",
								icon: <Copy size={15} />,
								onClick: () => {
									handleCopyLink(data._id);
								},
							},

					data.isPrivate
						? null
						: {
								type: "divider",
							},
					{
						key: "Try Test",
						label: "Try Test",
						icon: <FileDoneOutlined style={{ fontSize: "14px" }} />,
						onClick: () => {
							window.open(
								`${AppConfig.quizServerURL}/test/try/${data._id}?tryTest=true`,
								"_blank",
								"noopener,noreferrer"
							);
						},
					},
					hasResourcePermission(RoleResource.QUIZ, RoleAction.EDIT)
						? {
								type: "divider",
							}
						: null,
					hasResourcePermission(RoleResource.QUIZ, RoleAction.EDIT)
						? {
								key: "Settings",
								label: (
									<Link to={`${data._id}/update/general`}>
										<Space size="middle">
											<span>
												{data.isFrozen
													? "View"
													: "Settings"}
											</span>
										</Space>
									</Link>
								),
								icon: (
									<EditOutlined
										style={{ fontSize: "14px" }}
									/>
								),
							}
						: null,
					hasResourcePermission(RoleResource.QUIZ, RoleAction.CLONE)
						? {
								type: "divider",
							}
						: null,
					hasResourcePermission(RoleResource.QUIZ, RoleAction.CLONE)
						? {
								key: "Clone",
								label: "Clone",
								icon: (
									<CopyOutlined
										style={{ fontSize: "14px" }}
									/>
								),
								onClick: () => {
									setCloneQuizData({
										_id: data._id,
										randomizeQuestion:
											data.randomizeQuestion ?? false,
										poolQuestion:
											data.poolQuestion ?? false,
									});
								},
							}
						: null,
					{
						type: "divider",
					},
					{
						key: "Download",
						label: "Download",
						icon: <DownloadOutlined style={{ fontSize: "14px" }} />,
						children: [
							{
								label: "Including Test Cases",
								key: "01",
								onClick: () => {
									handleDownloadTest(
										data._id,
										false,
										data.title
									);
								},
							},
							{
								type: "divider",
							},
							{
								label: "Excluding Test Cases",
								key: "02",
								onClick: () => {
									handleDownloadTest(
										data._id,
										true,
										data.title
									);
								},
							},
						],
					},
					{
						type: "divider",
					},
					{
						key: "Template",
						label:
							tableState.dropDownValue === 4
								? "Remove Template"
								: "Make Template",
						icon: <FileOutlined style={{ fontSize: "14px" }} />,
						onClick: () => {
							modal.confirm({
								title: "Make Template",
								content:
									tableState.dropDownValue !== 4
										? "Are you sure you want to make this test a template?"
										: "Are you sure you want to remove this test from templates?",
								onOk: () => {
									makeTemplate(
										data._id,
										tableState.dropDownValue === 4
											? false
											: true
									);
								},
							});
						},
					},
					{
						type: "divider",
					},
					{
						key: "metrics",
						label: (
							<Link to={`/tests/${data._id}/metrics`}>
								<Space size="large">
									<span>Metrics</span>
								</Space>
							</Link>
						),
						icon: (
							<LineChartOutlined style={{ fontSize: "14px" }} />
						),
					},
					{
						type: "divider",
					},
					{
						key: "Freeze",
						label: data.isFrozen ? "Unfreeze" : "Freeze",
						icon: data.isFrozen ? (
							<LockOutlined style={{ fontSize: "14px" }} />
						) : (
							<UnlockOutlined style={{ fontSize: "14px" }} />
						),
						onClick: () => {
							handleFreeze(data._id, data.isFrozen ?? false);
						},
					},
					hasResourcePermission(RoleResource.QUIZ, RoleAction.DELETE)
						? {
								type: "divider",
							}
						: null,
					hasResourcePermission(RoleResource.QUIZ, RoleAction.DELETE)
						? {
								key: "Delete",
								label: "Delete",
								icon: (
									<DeleteOutlined
										style={{ fontSize: "14px" }}
									/>
								),
								onClick: () => {
									modal.confirm({
										title: "Delete Test",
										content:
											"Are you sure you want to delete this test?",
										onOk: () => handleDeleteQuiz(data._id),
									});
								},
							}
						: null,
				];
				let filteredMenuItems = menuItems;
				if (data.isFrozen) {
					filteredMenuItems = filteredMenuItems.filter(
						item =>
							(item?.key !== "Freeze" ||
								user?.role.id === UserRole.ADMIN) &&
							item?.key !== "Delete" &&
							item?.key !== "Template"
					);
				}
				if (tableState.dropDownValue !== 1) {
					filteredMenuItems = filteredMenuItems.filter(
						item =>
							item?.key !== "Try Test" &&
							item?.key !== "divider-freeze"
					);
				}
				if (
					tableState.dropDownValue !== 1 &&
					tableState.dropDownValue !== 4
				) {
					filteredMenuItems = filteredMenuItems.filter(
						item => item?.key !== "Template"
					);
				}
				if (
					tableState.dropDownValue === 4 ||
					tableState.dropDownValue === 5 ||
					tableState.dropDownValue === 6
				) {
					filteredMenuItems = filteredMenuItems.filter(
						item =>
							item?.key !== "Try Test" &&
							item?.key !== "metrics" &&
							item?.key !== "Reports" &&
							item?.key !== "Freeze" &&
							item?.key !== "Copy"
					);
				}
				if (tableState.dropDownValue === 7) {
					filteredMenuItems = filteredMenuItems.filter(
						item =>
							item?.key !== "Settings" &&
							item?.key !== "Delete" &&
							item?.key !== "metrics" &&
							item?.key !== "Freeze" &&
							item?.key !== "Reports" &&
							item?.key !== "Copy"
					);
				}
				if (tableState.dropDownValue === 4) {
					filteredMenuItems.unshift(
						{
							key: "makePrivate",
							label: data.isPublic
								? "Make Private"
								: "Make Public",
							icon: data.isPublic ? (
								<LockOutlined size={16} />
							) : (
								<GlobalOutlined size={16} />
							),
							onClick: () =>
								handleQuizTemplateAccess(
									data._id,
									data.isPublic ?? false
								),
						},
						{
							type: "divider",
						}
					);
				}
				if (
					tableState.dropDownValue !== 1 &&
					tableState.dropDownValue !== 4 &&
					user?.role.id === UserRole.FACULTY
				) {
					filteredMenuItems = filteredMenuItems.filter(
						item =>
							item?.key !== "Settings" && item?.key !== "Delete"
					);
				}
				if (
					(data.progressStep ?? 0) < TestSaveProgressStep.PROCTORING
				) {
					filteredMenuItems = filteredMenuItems.filter(
						item =>
							item?.key === "Settings" || item?.key === "Delete"
					);
				}
				filteredMenuItems = filteredMenuItems.filter(
					(item, index, arr) => {
						return !(
							item?.type === "divider" &&
							(index === 0 ||
								index === arr.length - 1 ||
								arr[index - 1]?.type === "divider")
						);
					}
				);
				return (
					<Dropdown
						menu={{ items: filteredMenuItems }}
						trigger={["click"]}
						placement="bottomRight"
						getPopupContainer={triggerNode =>
							triggerNode.parentElement || document.body
						}
						popupRender={menu => (
							<div style={{ width: 180 }}>{menu}</div>
						)}
					>
						<Button variant="text" color="default">
							<MoreOutlined style={{ fontSize: "16px" }} />
						</Button>
					</Dropdown>
				);
			},
		},
	];
	const [loading, setLoading] = useState<boolean>(true);
	const [tableData, setTableData] = useState<{
		data: QuizList[];
		totalRecords: number;
	}>({
		data: [],
		totalRecords: 0,
	});
	const [messageInstance, contextHolder] = message.useMessage({
		maxCount: 1,
	});
	const [showDrawer, setShowDrawer] = useState<boolean>(false);
	const [collapsed, setCollapsed] = useState<boolean>(true);
	const [filterData, setFilterData] = useState<{
		keywords: {
			_id: string;
			tag: string;
		}[];
		createdBy: {
			_id: string;
			displayname: string;
		}[];
	}>({
		keywords: [],
		createdBy: [],
	});
	const [cloneQuizData, setCloneQuizData] = useState<
		| {
				_id: string;
				randomizeQuestion: boolean;
				poolQuestion: boolean;
		  }
		| undefined
	>();
	const [searchTerm, setSearchTerm] = useState<string>(
		tableState.searchValue
	);
	const [debouncedSearchTerm, setDebouncedSearchTerm] = useState<string>(
		tableState.searchValue
	);
	const [emptyMessageType, setEmptyMessageType] = useState<string>("");

	useEffect(() => {
		const handler = setTimeout(() => {
			setDebouncedSearchTerm(searchTerm);
		}, 300);

		return () => {
			clearTimeout(handler);
		};
	}, [searchTerm]);

	useEffect(() => {
		setTableState(prev => ({
			...prev,
			searchValue: debouncedSearchTerm,
			currentPage: 1,
		}));
	}, [debouncedSearchTerm]);

	useEffect(() => {
		fetchFiltersData();
	}, []);

	useEffect(() => {
		setDataFilter([
			{
				type: "select",
				key: "dropdown",
				title: "dropdown",
				items: newOptions,
				value: tableState.dropDownValue,
				onChange: handleDropdownChange,
			},
			{
				type: "collapse",
				key: "keywords",
				title: "keywords",
				options: filterData?.keywords?.map(val => ({
					value: val.tag,
					label: val.tag,
				})),
				defaultValue: tableState.keywords,
				disabled: false,
			},
			{
				type: "collapse",
				key: "CreatedBy",
				title: "Created By",
				options: filterData?.createdBy?.map(val => ({
					value: val._id,
					label: val.displayname,
				})),
				defaultValue: tableState.createdBy,
				disabled:
					tableState.dropDownValue === 1 ||
					tableState.dropDownValue === 4 ||
					tableState.dropDownValue === 7,
			},
			{
				type: "dateRange",
				key: "dateRange",
				title: "Filter By Date",
				value: tableState.dateRange,
			},
		]);
	}, [tableState]);

	const fetchData = useCallback(async () => {
		setLoading(true);
		const { order, field } = tableState.sorting || {};
		const payload = {
			quizType: mapQuizType(tableState.dropDownValue),
			[mapFieldName(tableState.dropDownValue)]: 1,
			length: tableState.pageSize,
			start: (tableState.currentPage - 1) * tableState.pageSize,
			search: { value: tableState.searchValue.trim() },
			sorting: {
				order:
					order === "ascend"
						? "asc"
						: order === "descend"
							? "dsc"
							: undefined,
				column: field,
			},
			keywords: tableState.keywords,
			fltrCreatedBy: tableState.createdBy,
			dateRange: [
				tableState.dateRange[0]?.toString(),
				tableState.dateRange[1]?.toString(),
			],
		};
		if (tableState.dropDownValue > 3) {
			payload["isTemplate"] = 1;
		}
		try {
			const res = await quizClient.getTests(payload);
			if ("error" in res) {
				messageInstance.error("Something went wrong");
			} else {
				setLoading(false);
				setTableData(prev => {
					return {
						...prev,
						data: res.data,
						totalRecords: res.recordsFiltered,
					};
				});
			}
		} catch (err) {
			messageInstance.error(err as string);
		} finally {
			setLoading(false);
		}
	}, [tableState, messageInstance]);

	async function fetchFiltersData() {
		try {
			const res = await quizClient.getFilterData();
			if (!res) {
				messageInstance.error("Failed to fetch filter data.");
				return;
			}
			if ("error" in res) {
				messageInstance.error(res.error);
			} else {
				setFilterData({
					keywords: res.keywords,
					createdBy: res.users,
				});

				setDataFilter(prevFilters =>
					prevFilters.map(filter => {
						if (filter.key === "keywords") {
							return {
								...filter,
								options: res.keywords?.map(val => ({
									value: val.tag,
									label: val.tag,
								})),
								defaultValue: tableState.keywords,
							};
						}
						if (filter.key === "CreatedBy") {
							return {
								...filter,
								options: res.users?.map(val => ({
									value: val._id,
									label: val.displayname,
								})),
								defaultValue: tableState.createdBy,
								disabled:
									tableState.dropDownValue === 1 ||
									tableState.dropDownValue === 4 ||
									tableState.dropDownValue === 7,
							};
						}
						return filter;
					})
				);
			}
		} catch (err) {
			console.log(err);
			messageInstance.error(err as string);
		}
	}

	useEffect(() => {
		fetchData();
		if (tableState.searchValue !== "") {
			setEmptyMessageType("search");
		} else if (
			tableState.dateRange.some(date => date) ||
			tableState.createdBy.length > 0 ||
			tableState.keywords.length > 0
		) {
			setEmptyMessageType("filter");
		} else {
			setEmptyMessageType("default");
		}
	}, [
		tableState.dropDownValue,
		tableState.searchValue,
		tableState.pageSize,
		tableState.sorting,
		tableState.keywords,
		tableState.dateRange,
		tableState.createdBy,
		fetchData,
	]);

	const handleTableChange = (
		pagination: TablePaginationConfig,
		_filter: unknown,
		sorting: unknown
	) => {
		let sort = {};

		if (sorting && typeof sorting === "object" && "field" in sorting) {
			sort =
				sorting.field === undefined &&
				firstColumnNames.includes(tableState.sorting.field ?? "")
					? tableState.sorting
					: (sorting as { order?: string; field?: string });
		}
		setTableState(prev => {
			return {
				...prev,
				pageSize: pagination.pageSize || prev.pageSize,
				currentPage: pagination.current || prev.currentPage,
				sorting: sort || {},
			};
		});
	};

	const handlePlusButton = async () => {
		setShowDrawer(prev => !prev);
	};

	const handleFiltersChange = (items: Filters) => {
		setTableState(prev => {
			return {
				...prev,
				currentPage: 1,
				dropDownValue:
					Number(items.dropdown) || tableState.dropDownValue,
				keywords: items.keywords || tableState.keywords,
				createdBy: items.CreatedBy || tableState.createdBy,
				dateRange:
					items.dateRange === undefined
						? tableState.dateRange
						: (items.dateRange ?? ["", ""]),
			};
		});

		if (items.dropdown !== undefined) {
			const dropdownValue = Number(items.dropdown);
			handleDropdownChange(dropdownValue);
		}
	};

	const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
		setSearchTerm(e.target.value);
	};

	const handleSearchClear = () => {
		setSearchTerm("");
	};

	const handleDropdownChange = (value: number) => {
		setDataFilter(prevFilters =>
			prevFilters.map(filter => {
				if (filter.key === "dropdown" && filter.type === "select") {
					return { ...filter, value };
				}
				return filter;
			})
		);
		if (value === 1 || value === 4 || value === 7) {
			setDataFilter(prevFilters =>
				prevFilters.map(filter =>
					filter.key === "CreatedBy"
						? { ...filter, disabled: true, defaultValue: [] }
						: filter
				)
			);
		} else {
			setDataFilter(prevFilters =>
				prevFilters.map(filter =>
					filter.key === "CreatedBy"
						? {
								...filter,
								disabled: false,
								defaultValue: tableState.createdBy,
							}
						: filter
				)
			);
		}
	};

	const handleFilterClose = () => {
		setCollapsed(true);
	};

	const handleFilterReset = () => {
		setTableState(prev => ({
			...prev,
			keywords: [],
			createdBy: [],
			dateRange: ["", ""],
			currentPage: 1,
			searchValue: "",
			dropDownValue: 1,
		}));
		setSearchTerm("");
	};

	const [dataFilter, setDataFilter] = useState<FilterMenuItem[]>([
		{
			type: "select",
			key: "dropdown",
			title: "dropdown",
			items: newOptions,
			value: tableState.dropDownValue,
			onChange: handleDropdownChange,
		},
		{
			type: "collapse",
			key: "keywords",
			title: "keywords",
			options: filterData?.keywords?.map(val => ({
				value: val.tag,
				label: val.tag,
			})),
			defaultValue: tableState.keywords,
			disabled: false,
		},
		{
			type: "collapse",
			key: "CreatedBy",
			title: "Created By",
			options: filterData?.createdBy?.map(val => ({
				value: val._id,
				label: val.displayname,
			})),
			defaultValue: tableState.createdBy,
			disabled:
				tableState.dropDownValue === 1 ||
				tableState.dropDownValue === 4 ||
				tableState.dropDownValue === 7,
		},
		{
			type: "dateRange",
			key: "dateRange",
			title: "Filter By Date",
			value: tableState.dateRange,
		},
	]);

	const handleCloneClose = () => {
		setCloneQuizData(undefined);
	};

	const handleCloneSubmit = (message: string, type: "success" | "error") => {
		if (type === "success") {
			messageInstance.success(message);
			setCloneQuizData(undefined);
		} else {
			messageInstance.error(message);
		}
	};

	const handleDeleteQuiz = async (quizId: string) => {
		try {
			const res = await quizClient.deleteQuiz(quizId);
			messageInstance.success(res.msg);
			fetchData();
		} catch (err) {
			messageInstance.error(err as string);
		}
	};

	const makeTemplate = async (quizId: string, flag: boolean) => {
		try {
			const payload = {
				isMakeTemplate: flag,
				quizId: quizId,
			};
			await quizClient.makeTemplate(payload);
			messageInstance.success(
				flag
					? "Test has been moved to templates successfully"
					: "Test removed from templates successfully"
			);
			fetchData();
		} catch (err) {
			messageInstance.error(err as string);
		}
	};

	const handleDownloadTest = async (
		quizId: string,
		withoutTestCases: boolean,
		title: string
	) => {
		const url = `/getTestContentFile/${quizId}${withoutTestCases ? "?withoutTestCases=1&" : "?"}title=${title}`;
		window.open(url, "_blank", "noopener,noreferrer");
	};

	const handleFreeze = async (id: string, isFrozen: boolean) => {
		try {
			if (isFrozen) {
				await quizClient.unFreezeQuiz(id);
				messageInstance.success("Quiz unfreezed successfully");
			} else {
				await quizClient.freezeQuiz(id);
				messageInstance.success("Quiz Freezed successfully");
			}
			fetchData();
		} catch (err) {
			console.log(err);
			messageInstance.error(err as string);
		}
	};

	return (
		<>
			<PageHelmet title="Tests" />
			<Layout style={{ width: "100%", height: "100%" }}>
				{contextHolder}
				{modalContext}
				<Sider
					width="20%"
					collapsed={collapsed}
					collapsedWidth={0}
					theme="light"
					style={{
						borderRight: `1px solid ${colorBorder}`,
						overflow: "hidden auto",
					}}
				>
					<FilterMenu
						onChange={handleFiltersChange}
						componentsData={dataFilter}
						onClose={handleFilterClose}
						onReset={handleFilterReset}
					></FilterMenu>
				</Sider>
				<Layout>
					<Header
						style={{
							backgroundColor: colorBgContainer,
							padding: 0,
						}}
					>
						<Flex
							style={{ height: "100%", padding: "1em" }}
							justify="space-between"
						>
							<Space align="center" size={"large"}>
								<Badge
									color="#de6834"
									size="small"
									offset={[2, 1]}
									count={
										(tableState.keywords.length > 0
											? 1
											: 0) +
										(tableState.createdBy.length > 0
											? 1
											: 0) +
										(tableState.dateRange[0] ? 1 : 0) +
										(tableState.dropDownValue !== 1 ? 1 : 0)
									}
								>
									<Button
										type={collapsed ? "default" : "primary"}
										onClick={() =>
											setCollapsed(prev => !prev)
										}
										icon={
											<FilterIcon
												size={18}
												style={{
													paddingTop: "0.15rem",
												}}
											/>
										}
									></Button>
								</Badge>
								<Row>
									<Search
										size="middle"
										placeholder="Search tests..."
										allowClear
										value={searchTerm}
										loading={loading}
										onInput={handleSearch}
										onClear={handleSearchClear}
									/>
								</Row>
							</Space>

							{hasResourcePermission(
								RoleResource.QUIZ,
								RoleAction.ADD
							) && (
								<Button
									type="primary"
									onClick={() => handlePlusButton()}
								>
									create test
								</Button>
							)}
						</Flex>
					</Header>
					<Content>
						<div style={{ height: "100%", overflow: "auto" }}>
							<DataTable<QuizList>
								data={tableData.data}
								columns={columns}
								loading={loading}
								totalRecords={tableData.totalRecords}
								pageSize={tableState.pageSize}
								onTableChange={handleTableChange}
								currentPage={tableState.currentPage}
								isServerSide={true}
								locale={{
									emptyText: (
										<Empty
											description={emptyTableMessage(
												emptyMessageType
											)}
											image={Empty.PRESENTED_IMAGE_SIMPLE}
											styles={{ image: { height: 41 } }}
										/>
									),
								}}
							/>
						</div>
					</Content>
				</Layout>
				{cloneQuizData && (
					<CloneQuiz
						quizId={cloneQuizData._id}
						randomizeQuestion={cloneQuizData.randomizeQuestion}
						poolQuestion={cloneQuizData.poolQuestion}
						onClose={handleCloneClose}
						onSubmit={handleCloneSubmit}
					/>
				)}
			</Layout>
			<CreateTestDrawer
				open={showDrawer}
				onClose={() => setShowDrawer(false)}
				onFinish={() => setShowDrawer(false)}
			/>
		</>
	);
}
