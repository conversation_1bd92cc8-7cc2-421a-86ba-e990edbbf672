export interface User {
	_id: string;
	email: string;
	displayname: string;
}

export interface QuizList {
	_id: string;
	updatedAt: string;
	title: string;
	keywords: string;
	quizTime: number;
	quizCode: number;
	createdBy: User;
	isPublic?: boolean;
	startTime: string;
	endTime?: string;
	parentIdOfCreator?: string;
	randomizeQuestion?: boolean;
	poolQuestion?: boolean;
	isFrozen?: boolean;
	attemptsCount?: number;
	score?: number;
	questionsCount?: number;
	isPrivate?: boolean;
	isMarkedAsCheck?: boolean;
	progressStep?: number;
	orgId?: string;
}

export const Data: QuizList[] = [
	{
		_id: "6789ddad02568e45bef3c012",
		updatedAt: "2025-01-17T04:33:49.183Z",
		title: "asdxdfsdsssssssssssssssssssfsdfsdfsdfsdfsdf sfdgdfgdf dsfdgds sfefdsfsdfsdfdf asdxdfsdsssssssssssssssssssfsdfsdfsdfsdfsdf sfdgdfgdf dsfdgds sfefdsfsdfsdfdf",
		keywords: "demotest",
		quizTime: 90,
		quizCode: 111,
		randomizeQuestion: true,
		endTime: "2025-01-17T04:43:49.176Z",
		createdBy: {
			_id: "59f9c87bbace049edfca78cf",
			email: "<EMAIL>",
			displayname: "Admin",
		},
		parentIdOfCreator: "59f9c87bbace049edfca78cf",
		isFrozen: false,
		isPublic: true,
		startTime: "2025-01-17T04:33:49.176Z",
	},
	{
		_id: "6789dd9902568e45bef3c010",
		updatedAt: "2025-01-17T04:33:29.114Z",
		title: "asdf",
		keywords: "firsttest",
		quizTime: 90,
		quizCode: 111,
		poolQuestion: true,
		endTime: "2025-01-17T04:43:29.089Z",
		createdBy: {
			_id: "59f9c87bbace049edfca78cf",
			email: "<EMAIL>",
			displayname: "Pankaj Organization",
		},
		parentIdOfCreator: "59f9c87bbace049edfca78cf",
		isFrozen: false,
		isPublic: true,
		startTime: "2025-01-17T04:33:29.089Z",
	},
	{
		_id: "5cb56138eea27b4e719fbbe2",
		updatedAt: "2025-01-13T05:12:30.593Z",
		title: "BCA-LP3-DS",
		keywords: "",
		quizTime: 60,
		startTime: "2019-04-15T04:40:00.000Z",
		endTime: "2019-04-17T11:05:00.000Z",
		quizCode: 160435,
		createdBy: {
			_id: "59f9c87bbace049edfca78cf",
			email: "<EMAIL>",
			displayname: "Mentor",
		},
		parentIdOfCreator: "59f9c87bbace049edfca78cf",
	},
	{
		_id: "6786150bac6111657f01297e",
		updatedAt: "2025-01-14T07:40:59.902Z",
		title: "BCA-LP3-DS-Clone",
		keywords: "",
		quizTime: 60,
		endTime: "2019-04-17T11:05:00.000Z",
		quizCode: 160435,
		createdBy: {
			_id: "59f9c87bbace049edfca78cf",
			email: "<EMAIL>",
			displayname: "Admin",
		},
		parentIdOfCreator: "59f9c87bbace049edfca78cf",
		randomizeQuestion: false,
		poolQuestion: false,
		isPublic: true,
		startTime: "2019-04-15T04:40:00.000Z",
	},
	{
		_id: "6786167aac6111657f012984",
		updatedAt: "2025-01-14T07:47:06.757Z",
		title: "BCA-LP3-DS-Clone-Clone",
		keywords: "",
		quizTime: 60,
		endTime: "2019-04-17T11:05:00.000Z",
		quizCode: 160435,
		createdBy: {
			_id: "59f9c87bbace049edfca78cf",
			email: "<EMAIL>",
			displayname: "Thakur Organisation",
		},
		parentIdOfCreator: "59f9c87bbace049edfca78cf",
		randomizeQuestion: false,
		poolQuestion: false,
		isPublic: true,
		startTime: "2019-04-15T04:40:00.000Z",
	},
	{
		_id: "67861697ac6111657f012986",
		updatedAt: "2025-01-14T07:47:35.348Z",
		title: "BCA-LP3-DS-Clone-Clone-Clone",
		keywords: "",
		quizTime: 60,
		endTime: "2019-04-17T11:05:00.000Z",
		quizCode: 160435,
		createdBy: {
			_id: "59f9c87bbace049edfca78cf",
			email: "<EMAIL>",
			displayname: "Admin",
		},
		parentIdOfCreator: "59f9c87bbace049edfca78cf",
		randomizeQuestion: false,
		poolQuestion: false,
		isPublic: true,
		startTime: "2019-04-15T04:40:00.000Z",
	},
	{
		_id: "678616daac6111657f012988",
		updatedAt: "2025-01-14T07:48:42.937Z",
		title: "BCA-LP3-DS-Clone-Clone-Clone",
		keywords: "",
		quizTime: 60,
		endTime: "2019-04-17T11:05:00.000Z",
		quizCode: 160435,
		createdBy: {
			_id: "59f9c87bbace049edfca78cf",
			email: "<EMAIL>",
			displayname: "Admin",
		},
		parentIdOfCreator: "59f9c87bbace049edfca78cf",
		randomizeQuestion: false,
		poolQuestion: false,
		isPublic: true,
		startTime: "2019-04-15T04:40:00.000Z",
	},
	{
		_id: "6786171eac6111657f01298a",
		updatedAt: "2025-01-14T07:49:50.402Z",
		title: "BCA-LP3-DS-Clone-Clone-Clone",
		keywords: "",
		quizTime: 60,
		endTime: "2019-04-17T11:05:00.000Z",
		quizCode: 160435,
		createdBy: {
			_id: "59f9c87bbace049edfca78cf",
			email: "<EMAIL>",
			displayname: "Admin",
		},
		parentIdOfCreator: "59f9c87bbace049edfca78cf",
		randomizeQuestion: false,
		poolQuestion: true,
		isPublic: true,
		startTime: "2019-04-15T04:40:00.000Z",
	},
	{
		_id: "5c680434c7222e7b6d1b9189",
		updatedAt: "2024-12-17T09:15:56.665Z",
		title: "SDC-Coding-1",
		keywords: "",
		quizTime: 120,
		startTime: "2019-02-16T02:30:00.000Z",
		endTime: "2019-02-18T11:30:00.000Z",
		quizCode: 12345,
		createdBy: {
			_id: "59f9c87bbace049edfca78cf",
			email: "<EMAIL>",
			displayname: "Admin",
		},
	},
	{
		_id: "67861525ac6111657f012980",
		updatedAt: "2025-01-14T07:41:25.667Z",
		title: "SDC-Coding-1-Clone",
		keywords: "",
		quizTime: 120,
		endTime: "2019-02-18T11:30:00.000Z",
		quizCode: 12345,
		createdBy: {
			_id: "59f9c87bbace049edfca78cf",
			email: "<EMAIL>",
			displayname: "Admin",
		},
		randomizeQuestion: false,
		poolQuestion: false,
		parentIdOfCreator: "59f9c87bbace049edfca78cf",
		isPublic: true,
		startTime: "2019-02-16T02:30:00.000Z",
	},
	{
		_id: "5cf0f8a28283815e8b02c926",
		updatedAt: "2025-01-04T10:16:52.981Z",
		title: "Test-4-UCA-17 Clone",
		keywords: "",
		quizTime: 170,
		startTime: "2019-05-31T10:15:00.000Z",
		quizCode: 98451,
		createdBy: {
			_id: "59f9c87bbace049edfca78cf",
			email: "<EMAIL>",
			displayname: "Admin",
		},
		parentIdOfCreator: "59f9c87bbace049edfca78cf",
	},
	{
		_id: "5db80eb6e17366697c176928",
		updatedAt: "2024-12-18T06:27:50.810Z",
		title: "UCA-Test-8-PPT Clone",
		keywords: "",
		quizTime: 90,
		startTime: "2019-11-03T14:30:00.000Z",
		quizCode: 123,
		createdBy: {
			_id: "59f9c87bbace049edfca78cf",
			email: "<EMAIL>",
			displayname: "Admin",
		},
		parentIdOfCreator: "59f9c87bbace049edfca78cf",
	},
	{
		_id: "5db80ebde17366697c176929",
		updatedAt: "2024-12-17T09:15:58.725Z",
		title: "UCA-Test-5-PPT Clone",
		keywords: "",
		quizTime: 90,
		startTime: "2019-11-03T03:30:00.000Z",
		quizCode: 2502,
		createdBy: {
			_id: "59f9c87bbace049edfca78cf",
			email: "<EMAIL>",
			displayname: "Admin",
		},
		parentIdOfCreator: "59f9c87bbace049edfca78cf",
	},
	{
		_id: "5db80f01e17366697c17692b",
		updatedAt: "2025-01-04T06:11:13.612Z",
		title: "UCA-Test-6-PPT Clone",
		keywords: "",
		quizTime: 90,
		startTime: "2019-11-03T03:30:00.000Z",
		quizCode: 96521,
		createdBy: {
			_id: "59f9c87bbace049edfca78cf",
			email: "<EMAIL>",
			displayname: "Admin",
		},
		parentIdOfCreator: "59f9c87bbace049edfca78cf",
	},
	{
		_id: "5e54ceb489d5e2512ae34906",
		updatedAt: "2024-12-17T09:16:00.766Z",
		title: "Code-Champ-5",
		keywords: "",
		quizTime: 150,
		startTime: "2020-02-25T03:30:00.000Z",
		quizCode: 82014,
		createdBy: {
			_id: "59f9c87bbace049edfca78cf",
			email: "<EMAIL>",
			displayname: "Admin",
		},
		parentIdOfCreator: "59f9c87bbace049edfca78cf",
	},
	{
		_id: "674ed09ba94334a53b23c7ea",
		updatedAt: "2024-12-17T09:16:03.344Z",
		title: "test-quiz",
		keywords: "itc",
		quizTime: 123,
		quizCode: 123,
		createdBy: {
			_id: "59f9c87bbace049edfca78cf",
			email: "<EMAIL>",
			displayname: "Admin",
		},
		isPublic: true,
		startTime: "2024-12-03T09:34:00.000Z",
	},
	{
		_id: "674edc7ca94334a53b23c7ff",
		updatedAt: "2024-12-17T09:16:03.355Z",
		title: "test space in name",
		keywords: "space,test,key,abc",
		quizTime: 123,
		quizCode: 123,
		createdBy: {
			_id: "59f9c87bbace049edfca78cf",
			email: "<EMAIL>",
			displayname: "Admin",
		},
		isPublic: true,
		startTime: "2024-12-03T10:14:00.000Z",
	},
];
