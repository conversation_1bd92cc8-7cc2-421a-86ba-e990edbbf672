import { useEffect, useState, Key, useMemo, useCallback } from "react";
import {
	Badge,
	Button,
	Dropdown,
	Empty,
	Flex,
	Layout,
	MenuProps,
	message,
	Row,
	Space,
	Switch,
	TableColumnsType,
	TablePaginationConfig,
	Tag,
	theme,
	Tooltip,
	Typography,
} from "antd";
import { DataTable } from "@/components/dataTable/dataTable";
import { quizClient, useAppStore } from "../store";
import {
	CopyOutlined,
	DeleteOutlined,
	EditOutlined,
	MoreOutlined,
	PlusOutlined,
	UploadOutlined,
} from "@ant-design/icons";
import FilterMenu, { FilterMenuItem } from "@/components/filterMenu/filterMenu";
import { Content, Header } from "antd/es/layout/layout";
import Sider from "antd/es/layout/Sider";
import { FilterIcon } from "lucide-react";
import { Question } from "@/components/add-question-modal/AddQuestionModal";
import { Link, useNavigate } from "react-router";
import { QuestionType } from "@/client/test-add";
import timeDate from "@/utils/timeDate";
import useModal from "antd/es/modal/useModal";
import useLocalStorageState from "@/localStorageState";
import { RoleAction, RoleResource, UserRole } from "@/constants/roles";
import Search from "antd/es/input/Search";
import { emptyMessages } from "@/constants/messages";
import AddBulkQuestionModal from "@/components/bulk-upload-modal/AddBulkQuestionModal";
import { AppConfig } from "@/config";
import { PlatformOrganisation } from "@/constants";
import { SortOrder } from "antd/es/table/interface";
import PageHelmet from "@/components/page-helmet/PageHelmet";
import QuestionTypeCloudIcon from "@/components/question-type-cloud-icon/question-type-cloud-icon";

type Filters = {
	keywords?: string[];
	CreatedBy?: string[];
	dropdown?: string;
	questionType?: string;
	premiumQuestions?: boolean;
};

const langCodeToLangStringMapping: { [key: string]: string } = {
	"0": "Python 2",
	"1": "Ruby",
	"2": "React-Jest",
	"3": "Php",
	"4": "JavaScript",
	"5": "SQL",
	"7": "C",
	"8": "Java",
	"10": "C#",
	"11": "Bash",
	"17": "C99",
	"18": "Cpp11",
	"19": "Cpp14",
	"20": "Python 3",
	"23": "MySql",
	"24": "Oracle",
	"77": "Cpp",
};

const mapFieldName = (dropDownValue: number): string => {
	const mapping: { [key: number]: string } = {
		1: "myList",
		2: "myOrgList",
		3: "otherList",
		4: "cqList",
	};
	return mapping[dropDownValue] ?? "myList";
};

const options = [
	{ value: 1, label: "My Questions" },
	{ value: 2, label: "My Organization" },
	{ value: 3, label: "Others" },
	{ value: 4, label: "CodeQuotient" },
];

const questionTypeOptions = [
	{ value: 0, label: "All" },
	{ value: 4, label: "Coding" },
	{ value: 1, label: "MCQ" },
	{ value: 5, label: "Multiple Questions" },
	{ value: 9, label: "Web" },
	{ value: 2, label: "Subjective" },
];

const emptyTableMessage = (
	type: string
): [description: string, image?: React.ReactNode] => {
	return [
		emptyMessages.question[type] ??
			emptyMessages.question.default ??
			emptyMessages.question[type],
	];
};

export default function QuestionList() {
	const navigate = useNavigate();
	const { user, session, hasResourcePermission } = useAppStore();

	const [tableState, setTableState] = useLocalStorageState<{
		dropDownValue: number;
		pageSize: number;
		searchValue: string;
		keywords: string[];
		sorting: { order?: string; field?: string };
		currentPage: number;
		questionType: number;
		createdBy: string[];
		isPremium: boolean;
	}>("questionListState", {
		dropDownValue: 1,
		pageSize: 10,
		searchValue: "",
		keywords: [],
		sorting: {},
		currentPage: 1,
		questionType: 0,
		createdBy: [],
		isPremium: false,
	});

	const newOptions = useMemo(() => {
		const havePermission = options.find(
			item => item.value === tableState.dropDownValue
		);
		if (!havePermission) {
			setTableState({
				...tableState,
				dropDownValue: 1,
			});
		}

		if (
			user?.role.id !== UserRole.ADMIN &&
			AppConfig.platformOrganisation === PlatformOrganisation.CHITKARA
		) {
			const newData = options.filter(item => item.value !== 3);
			if (!session?.allowAdminContent) {
				return newData.filter(item => item.value !== 4);
			}
			return newData.map(item =>
				item.value === 4 ? { ...item, label: "Chitkara" } : item
			);
		} else if (
			user?.role.id !== UserRole.ADMIN &&
			AppConfig.platformOrganisation === PlatformOrganisation.CQ
		) {
			const newData = options.filter(item => item.value !== 3);
			if (!session?.allowAdminContent) {
				return newData.filter(item => item.value !== 4);
			}
			return newData;
		} else {
			return options.filter(item => item.value !== 4);
		}
	}, [user?.role]);
	const [totalRecords, setTotalRecords] = useState<number>(0);
	const [tableData, setTableData] = useState<Question[]>([]);
	const [loading, setLoading] = useState<boolean>(true);
	const [rowSelection, setRowSelection] = useLocalStorageState<boolean>(
		"questionRowSelection",
		true
	);
	const [keywordData, setKeywordData] = useState<{
		keywords: {
			_id: string;
			tag: string;
		}[];
		createdBy: {
			_id: string;
			displayname: string;
		}[];
	}>({
		keywords: [],
		createdBy: [],
	});
	const [selectedQuestions, setSelectedQuestions] = useState<Key[]>([]);
	const [collapsed, setCollapsed] = useState<boolean>(true);
	const [isBulkModalOpen, toggleBulkAddModal] = useState(false);

	const handleDropdownChange = (value: number) => {
		setDataFilter(prevFilters =>
			prevFilters.map(filter => {
				if (filter.key === "dropdown" && filter.type === "select") {
					return { ...filter, value };
				}
				return filter;
			})
		);
		if (value === 1) {
			setDataFilter(prevFilters =>
				prevFilters.map(filter =>
					filter.key === "CreatedBy"
						? { ...filter, disabled: true, defaultValue: [] }
						: filter
				)
			);
		} else {
			setDataFilter(prevFilters =>
				prevFilters.map(filter =>
					filter.key === "CreatedBy"
						? {
								...filter,
								disabled: false,
								defaultValue: tableState.createdBy,
							}
						: filter
				)
			);
		}
	};

	const handleQuestionTypeChange = (value: number) => {
		setDataFilter(prevFilters =>
			prevFilters.map(filter => {
				if (filter.key === "questionType" && filter.type === "select") {
					return { ...filter, value };
				}
				return filter;
			})
		);
	};

	const [filterData, setDataFilter] = useState<FilterMenuItem[]>([
		{
			type: "select",
			key: "dropdown",
			title: "dropdown",
			items: newOptions,
			value: tableState.dropDownValue,
			onChange: handleDropdownChange,
		},
		{
			type: "collapse",
			key: "keywords",
			title: "keywords",
			options: keywordData?.keywords?.map(val => ({
				value: val.tag,
				label: val.tag,
			})),
			defaultValue: tableState.keywords,
			disabled: false,
		},
		{
			type: "collapse",
			key: "CreatedBy",
			title: "Created By",
			options: keywordData?.createdBy?.map(val => ({
				value: val._id,
				label: val.displayname,
			})),
			defaultValue: tableState.createdBy,
			disabled: tableState.dropDownValue === 1,
		},
		{
			type: "select",
			key: "questionType",
			title: "Question Type",
			items: questionTypeOptions,
			value: tableState.questionType,
			onChange: handleQuestionTypeChange,
		},
	]);

	const [messageInstance, contextHolder] = message.useMessage({
		maxCount: 1,
	});
	const [modal, modalContext] = useModal();
	const [searchTerm, setSearchTerm] = useState<string>(
		tableState.searchValue
	);
	const [debouncedSearchTerm, setDebouncedSearchTerm] = useState<string>(
		tableState.searchValue
	);
	const [emptyMessageType, setEmptyMessageType] = useState<string>("");

	useEffect(() => {
		const handler = setTimeout(() => {
			setDebouncedSearchTerm(searchTerm);
		}, 300);

		return () => {
			clearTimeout(handler);
		};
	}, [searchTerm]);

	useEffect(() => {
		console.log("debouncedSearchTerm", debouncedSearchTerm);
		setTableState(prev => ({
			...prev,
			searchValue: debouncedSearchTerm,
			currentPage: 1,
		}));
	}, [debouncedSearchTerm]);

	const fetchData = useCallback(async () => {
		setLoading(true);
		try {
			const { order, field } = tableState.sorting || {};
			const payload = {
				[mapFieldName(tableState.dropDownValue)]: 1,
				length: tableState.pageSize,
				start: (tableState.currentPage - 1) * tableState.pageSize,
				search: { value: tableState.searchValue.trim() },
				sorting: {
					order:
						order === "ascend"
							? "asc"
							: order === "descend"
								? "dsc"
								: undefined,
					column: field,
				},
				questionType: tableState.questionType,
				keywords: tableState.keywords,
				fltrCreatedBy: tableState.createdBy,
				...(tableState.isPremium && { isPremium: 1 }),
			};
			const response = await quizClient.getQuestions(payload);
			if ("error" in response) {
				messageInstance.error(response.error);
			} else {
				setTotalRecords(response.recordsFiltered);
				setTableData(response.data);
			}
		} catch (ex) {
			console.log(ex);
			messageInstance.error(ex as string);
		} finally {
			setLoading(false);
		}
	}, [
		messageInstance,
		tableState.createdBy,
		tableState.currentPage,
		tableState.dropDownValue,
		tableState.keywords,
		tableState.pageSize,
		tableState.questionType,
		tableState.searchValue,
		tableState.sorting,
		setTableState,
		tableState.isPremium,
	]);

	useEffect(() => {
		fetchData();
		if (tableState.searchValue !== "") {
			setEmptyMessageType("search");
		} else if (
			tableState.createdBy.length > 0 ||
			tableState.keywords.length > 0 ||
			tableState.questionType !== 0
		) {
			setEmptyMessageType("filter");
		} else {
			setEmptyMessageType("default");
		}
	}, [
		tableState.dropDownValue,
		tableState.searchValue,
		tableState.pageSize,
		tableState.sorting,
		tableState.keywords,
		tableState.createdBy,
		tableState.questionType,
		fetchData,
	]);

	useEffect(() => {
		fetchFiltersData();
	}, []);

	useEffect(() => {
		setDataFilter([
			{
				type: "select",
				key: "dropdown",
				title: "dropdown",
				items: newOptions,
				value: tableState.dropDownValue,
				onChange: handleDropdownChange,
			},
			{
				type: "collapse",
				key: "keywords",
				title: "keywords",
				options: keywordData?.keywords?.map(val => ({
					value: val.tag,
					label: val.tag,
				})),
				defaultValue: tableState.keywords,
				disabled: false,
			},
			{
				type: "collapse",
				key: "CreatedBy",
				title: "Created By",
				options: keywordData?.createdBy?.map(val => ({
					value: val._id,
					label: val.displayname,
				})),
				defaultValue: tableState.createdBy,
				disabled: tableState.dropDownValue === 1,
			},
			{
				type: "select",
				key: "questionType",
				title: "Question Type",
				items: questionTypeOptions,
				value: tableState.questionType,
				onChange: handleQuestionTypeChange,
			},
		]);
		if (user?.role.id === UserRole.ADMIN) {
			setDataFilter(prevFilters => {
				const filteredData = prevFilters.filter(
					item => item.key !== "premiumQuestions"
				);
				return [
					...filteredData,
					{
						type: "checkbox",
						key: "premiumQuestions",
						title: "Premium Questions",
						checked: tableState.isPremium,
					},
				];
			});
		}
	}, [tableState]);

	async function fetchFiltersData() {
		try {
			const res = await quizClient.getFilterData("questionId");
			if ("error" in res) {
				messageInstance.error("Something went wrong");
			} else {
				console.log(res);
				setKeywordData({
					keywords: res.keywords,
					createdBy: res.users,
				});

				setDataFilter(prevFilters =>
					prevFilters.map(filter => {
						if (filter.key === "keywords") {
							return {
								...filter,
								options: res.keywords.map(val => ({
									value: val.tag,
									label: val.tag,
								})),
								defaultValue: tableState.keywords,
							};
						}
						if (filter.key === "CreatedBy") {
							return {
								...filter,
								options: res.users?.map(val => ({
									value: val._id,
									label: val.displayname,
								})),
								defaultValue: tableState.createdBy,
							};
						}
						return filter;
					})
				);
			}
		} catch (err) {
			console.log(err);
			messageInstance.error(err as string);
		}
	}

	const handleTableChange = (
		pagination: TablePaginationConfig,
		_filter: unknown,
		sorting: unknown
	) => {
		setTableState(prev => {
			return {
				...prev,
				pageSize: pagination.pageSize || prev.pageSize,
				currentPage: pagination.current || prev.currentPage,
				sorting: sorting as { order?: string; field?: string },
			};
		});
	};

	const handleRowSelection = (selectedKeys: Key[]) => {
		setSelectedQuestions(selectedKeys);
	};

	const handleFiltersChange = (items: Filters) => {
		console.log(items);
		setTableState(prev => {
			return {
				...prev,
				currentPage: 1,
				dropDownValue:
					Number(items.dropdown) || tableState.dropDownValue,
				keywords: items.keywords || tableState.keywords,
				createdBy: items.CreatedBy || tableState.createdBy,
				questionType:
					items.questionType === undefined
						? tableState.questionType
						: Number(items.questionType),
				isPremium:
					items.premiumQuestions === undefined
						? tableState.isPremium
						: items.premiumQuestions,
			};
		});
		if (items.dropdown === undefined) {
			setRowSelection(prev => prev);
		} else if (
			Number(items.dropdown) !== 1 &&
			user?.role.id !== UserRole.ADMIN
		) {
			setRowSelection(false);
			setSelectedQuestions([]);
		} else {
			setRowSelection(true);
		}
	};

	const {
		token: { colorBgContainer, colorBorder },
	} = theme.useToken();

	const columns: TableColumnsType<Question> = [
		{
			key: "Serial Number",
			render: (_val, _record, index) => {
				const currentPage = tableState.currentPage;
				const pageSize = tableState.pageSize;
				return (currentPage - 1) * pageSize + index + 1;
			},
			width: 50,
		},
		{
			title: "Title",
			dataIndex: "title",
			key: "title",
			sorter: true,
			sortOrder:
				tableState.sorting?.field === "title"
					? (tableState.sorting.order as SortOrder)
					: undefined,
			showSorterTooltip: false,
			width: 250,
			render: (value, record) => {
				return (
					<Flex vertical>
						{hasResourcePermission(
							RoleResource.QUEST,
							RoleAction.PREVIEW
						) ? (
							<Link
								to={`/questions/preview/${record._id}`}
								title={`${value}`}
							>
								{value}
							</Link>
						) : (
							<Typography.Text>{value}</Typography.Text>
						)}
						{tableState.dropDownValue !== 1 && (
							<Typography.Text
								type="secondary"
								style={{ fontSize: "small" }}
							>
								<span>By </span>
								{record.createdBy.displayname}
							</Typography.Text>
						)}
					</Flex>
				);
			},
			ellipsis: true,
		},
		{
			title: "Type",
			dataIndex: "type",
			key: "quizTime",
			render(_value, record) {
				let isOnCloud = false;
				if (
					record.questionTypeCoding &&
					typeof record.questionTypeCoding === "object" &&
					"testCase" in record.questionTypeCoding &&
					Array.isArray(record.questionTypeCoding.testCase) &&
					record.questionTypeCoding.testCase.length
				) {
					record.questionTypeCoding.testCase.forEach(
						(ele: unknown) => {
							if (
								ele &&
								typeof ele === "object" &&
								"onCloud" in ele &&
								ele.onCloud
							) {
								isOnCloud = true;
							}
						}
					);
				}
				return (
					<QuestionTypeCloudIcon
						question={{
							type: record.type,
							isOnCloud: isOnCloud,
						}}
					/>
				);
			},
			width: 120,
		},
		{
			title: "Test Cases",
			key: "quizCode",
			dataIndex: ["questionTypeCoding", "testCase"],
			render: (_, record) => {
				if (
					record.type === QuestionType.CODING ||
					record.type === QuestionType.MULTIPLE
				) {
					return record.questionTypeCoding?.testCase?.length ?? 0;
				} else if (record.type === QuestionType.WEB) {
					return record.questionTypeWeb?.testCase?.length ?? 0;
				} else {
					return "-NA-";
				}
			},
			width: 120,
		},
		{
			title: "Languages",
			key: "language",
			width: 250,
			ellipsis: true,
			render: (_, record) => {
				if (
					record.type !== QuestionType.CODING &&
					record.type !== QuestionType.WEB
				) {
					return "-NA-";
				}
				let languages;
				if (record.type === QuestionType.CODING) {
					languages = record.questionTypeCoding?.codeproglang.map(
						language =>
							langCodeToLangStringMapping[language.language]
					);
				} else if (record.type === QuestionType.WEB) {
					languages = Array.from(
						(function* (questionOptions) {
							if (questionOptions.isHtmlAllowed) yield "HTML";
							if (questionOptions.isCssAllowed) yield "CSS";
							if (questionOptions.isJsAllowed) yield "JS";
							if (questionOptions.isReactQuestion) yield "React";
						})(record.questionTypeWeb)
					);
				}
				if (languages === undefined || languages.length === 0) {
					return "-NA-";
				}
				return (
					<Tooltip title={languages.join(", ")} placement="topLeft">
						{languages.map(name => (
							<Tag key={name}>{name}</Tag>
						))}
					</Tooltip>
				);
			},
		},
		{
			title: "Score",
			dataIndex: "score",
			key: "score",
			sorter: true,
			sortOrder:
				tableState.sorting?.field === "score"
					? (tableState.sorting.order as SortOrder)
					: undefined,
			showSorterTooltip: false,
			width: 120,
		},
		{
			title: "Lock",
			dataIndex: "isLocked",
			key: "isLocked",
			width: 120,
			render: (value, record) => {
				return (
					<Switch
						checked={value}
						onChange={checked => lockQuestion(record._id, checked)}
					/>
				);
			},
		},
		{
			title: "Publish",
			dataIndex: "isPublic",
			key: "isPublic",
			width: 120,
			render: (_, record) => {
				return (
					<Tooltip
						title={
							user.role.id === UserRole.ADMIN
								? !record.isLocked
									? "Lock question before publishing"
									: "Publish"
								: "Publish"
						}
					>
						<Switch
							checked={record.isPublic ? true : false}
							disabled={
								user.role.id === UserRole.ADMIN
									? !record.isLocked
									: false
							}
							onChange={checked =>
								publishQuestion(record._id, checked)
							}
						/>
					</Tooltip>
				);
			},
		},
		{
			title: "Updated At",
			dataIndex: "updatedAt",
			key: "updatedAt",
			sorter: true,
			sortOrder:
				tableState.sorting?.field === "updatedAt"
					? (tableState.sorting.order as SortOrder)
					: undefined,
			showSorterTooltip: false,
			width: 150,
			render: value => {
				const dateAndTime = timeDate(value);
				const [rawDate, time] = dateAndTime.split(" ");
				const date = rawDate.replace(",", "");
				return (
					<Space direction="vertical" align="center">
						<span>{date}</span>
						<span>{time}</span>
					</Space>
				);
			},
		},
		{
			title: "Actions",
			key: "actions",
			width: 80,
			render: (_value, data) => {
				const items: MenuProps["items"] = [
					hasResourcePermission(RoleResource.QUEST, RoleAction.EDIT)
						? {
								label: (
									<Link to={`/questions/add/${data._id}`}>
										Edit
									</Link>
								),
								key: "0",
								icon: (
									<EditOutlined
										style={{ fontSize: "14px" }}
									/>
								),
							}
						: null,
					hasResourcePermission(RoleResource.QUEST, RoleAction.EDIT)
						? {
								type: "divider",
							}
						: null,
					hasResourcePermission(RoleResource.QUEST, RoleAction.CLONE)
						? {
								label: "Clone",
								key: "2",
								icon: (
									<CopyOutlined
										style={{ fontSize: "14px" }}
									/>
								),
								onClick: () => {
									modal.confirm({
										title: "Clone question",
										content:
											"Are you sure you want to clone this question?",
										onOk: () => cloneQuestion(data._id),
									});
								},
							}
						: null,
					hasResourcePermission(RoleResource.QUEST, RoleAction.CLONE)
						? {
								type: "divider",
							}
						: null,
					hasResourcePermission(RoleResource.QUEST, RoleAction.DELETE)
						? {
								label: "Delete",
								key: "1",
								style: { minWidth: "100px" },
								icon: (
									<DeleteOutlined
										style={{ fontSize: "14px" }}
									/>
								),
								onClick: () => {
									modal.confirm({
										title: "Delete question",
										content:
											"Are you sure you want to delete this question?",
										onOk: () => deleteQuestion(data._id),
									});
								},
							}
						: null,
				];
				let filteredItems = items;
				if (tableState.dropDownValue === 4) {
					filteredItems = filteredItems.filter(
						item => item?.key !== "0" && item?.key !== "1"
					);
				}
				if (
					(tableState.dropDownValue === 2 ||
						tableState.dropDownValue === 4) &&
					user?.role.id === UserRole.FACULTY
				) {
					filteredItems = filteredItems.filter(
						item => item?.key !== "0" && item?.key !== "1"
					);
				}
				filteredItems = filteredItems.filter((item, index, array) => {
					if (item?.type !== "divider") return true;

					if (index === 0 || index === array.length - 1) return false;

					const prevItem = array[index - 1];
					const nextItem = array[index + 1];
					if (
						prevItem?.type === "divider" ||
						nextItem?.type === "divider"
					)
						return false;

					return true;
				});
				if (
					!(
						hasResourcePermission(
							RoleResource.QUEST,
							RoleAction.EDIT
						) ||
						hasResourcePermission(
							RoleResource.QUEST,
							RoleAction.CLONE
						) ||
						hasResourcePermission(
							RoleResource.QUEST,
							RoleAction.DELETE
						)
					)
				) {
					return <></>;
				}
				return (
					<Dropdown
						menu={{ items: filteredItems }}
						trigger={["click"]}
						placement="bottomRight"
						getPopupContainer={triggerNode =>
							triggerNode.parentElement || document.body
						}
					>
						<MoreOutlined style={{ fontSize: "16px" }} />
					</Dropdown>
				);
			},
		},
	];

	const filteredColumns = filterColumns(
		columns,
		user?.role.id ?? 0,
		tableState.dropDownValue
	);

	const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
		setSearchTerm(e.target.value);
	};

	const handleFilterClose = () => {
		setCollapsed(true);
	};

	const handleFilterReset = () => {
		setTableState(prev => ({
			...prev,
			keywords: [],
			createdBy: [],
			questionType: 0,
			isPremium: false,
			currentPage: 1,
			searchValue: "",
			dropDownValue: 1,
		}));
		setSearchTerm("");
		setRowSelection(true);

		if (user?.role.id === UserRole.ADMIN) {
			setDataFilter(prevFilters => {
				const filteredData = prevFilters.filter(
					item => item.key !== "premiumQuestions"
				);
				return [
					...filteredData,
					{
						type: "checkbox",
						key: "premiumQuestions",
						title: "Premium Questions",
						checked: false,
					},
				];
			});
		}
	};

	const handleSearchClear = () => {
		setSearchTerm("");
	};

	const deleteQuestion = async (id: string) => {
		try {
			const res = await quizClient.deleteQuestion(id);
			messageInstance.success(res.msg);
			fetchData();
		} catch (err) {
			messageInstance.error(err as string);
		}
	};

	const cloneQuestion = async (id: string) => {
		try {
			const res = await quizClient.cloneQuestion(id);
			messageInstance.success(res.msg);
			fetchData();
		} catch (err) {
			messageInstance.error(err as string);
			setLoading(false);
		}
	};

	const lockQuestion = async (id: string, checked: boolean) => {
		try {
			setLoading(true);
			const res = await quizClient.lockQuestion(id, checked);
			setTableData(prev =>
				prev.map(item =>
					item._id === id
						? {
								...item,
								isPublic: false,
								isLocked: checked,
							}
						: item
				)
			);
			setLoading(false);
			messageInstance.success(
				res.msg === "success" && checked
					? "Question locked successfully"
					: "Question unlocked successfully"
			);
		} catch (err) {
			messageInstance.error(err as string);
		} finally {
			setLoading(false);
			// fetchData();
		}
	};

	const publishQuestion = async (id: string, checked: boolean) => {
		try {
			setLoading(true);
			const res = await quizClient.publishQuestion(id, checked);
			setLoading(false);
			setTableData(prev =>
				prev.map(item =>
					item._id === id ? { ...item, isPublic: checked } : item
				)
			);
			messageInstance.success(
				res.msg === "Success" && checked
					? "Question published successfully"
					: "Question Unpublished successfully"
			);
		} catch (err) {
			messageInstance.error(err as string);
		} finally {
			setLoading(false);
			// fetchData();
		}
	};

	const deleteQuestions = async (ids: string[]) => {
		try {
			setLoading(true);
			await quizClient.deleteQuestions({ ids });
			setSelectedQuestions([]);
			messageInstance.success("Questions deleted successfully");
			fetchData();
		} catch (error) {
			messageInstance.error(error as string);
		} finally {
			setSelectedQuestions([]);
			setLoading(false);
		}
	};

	return (
		<>
			<PageHelmet title="Questions" />
			<Layout style={{ width: "100%", height: "100%" }}>
				{modalContext}
				<Sider
					width="20%"
					collapsed={collapsed}
					collapsedWidth={0}
					theme="light"
					style={{
						borderRight: `1px solid ${colorBorder}`,
						overflow: "hidden auto",
					}}
				>
					<FilterMenu
						onChange={handleFiltersChange}
						componentsData={filterData}
						onClose={handleFilterClose}
						onReset={handleFilterReset}
					></FilterMenu>
				</Sider>
				<Layout>
					<Header
						style={{
							backgroundColor: colorBgContainer,
							padding: "0px 16px",
						}}
					>
						<Flex align="center" justify="space-between">
							<Space align="center" style={{ gap: 16 }}>
								<Badge
									color="#de6834"
									size="small"
									offset={[-1, 1]}
									count={
										(tableState.keywords.length > 0
											? 1
											: 0) +
										(tableState.createdBy.length > 0
											? 1
											: 0) +
										(tableState.questionType != 0 ? 1 : 0) +
										(tableState.dropDownValue !== 1
											? 1
											: 0) +
										(tableState.isPremium ? 1 : 0)
									}
								>
									<Button
										type={collapsed ? "default" : "primary"}
										onClick={() =>
											setCollapsed(prev => !prev)
										}
										icon={
											<FilterIcon
												size={18}
												style={{
													paddingTop: "0.15rem",
												}}
											/>
										}
									></Button>
								</Badge>
								<Row>
									<Search
										size="middle"
										placeholder="Search questions..."
										allowClear
										value={searchTerm}
										loading={loading}
										onInput={handleSearch}
										onClear={handleSearchClear}
									/>
								</Row>
							</Space>
							<Flex gap={8}>
								{selectedQuestions?.length !== 0 &&
									hasResourcePermission(
										RoleResource.QUEST,
										RoleAction.DELETE
									) && (
										<Button
											type="primary"
											icon={<DeleteOutlined />}
											onClick={() =>
												deleteQuestions(
													selectedQuestions.map(
														String
													)
												)
											}
										>
											Delete
										</Button>
									)}
								{hasResourcePermission(
									RoleResource.QUEST,
									RoleAction.ADD
								) && (
									<Button
										type="primary"
										icon={<PlusOutlined />}
										onClick={() =>
											navigate("/questions/add")
										}
									>
										New
									</Button>
								)}
								{hasResourcePermission(
									RoleResource.QUEST,
									RoleAction.ADD
								) && (
									<Button
										type="primary"
										icon={<UploadOutlined />}
										onClick={() => toggleBulkAddModal(true)}
									>
										Bulk MCQ
									</Button>
								)}
							</Flex>
						</Flex>
					</Header>
					<Content>
						<div
							style={{
								height: "100%",
								overflow: "auto",
								backgroundColor: "#FBFBFB",
							}}
						>
							<DataTable
								data={tableData}
								columns={filteredColumns}
								loading={loading}
								totalRecords={totalRecords}
								pageSize={tableState.pageSize}
								currentPage={tableState.currentPage}
								enableRowSelection={rowSelection}
								selectedKeys={selectedQuestions}
								onRowSelection={handleRowSelection}
								onTableChange={handleTableChange}
								isServerSide={true}
								locale={{
									emptyText: (
										<Empty
											description={emptyTableMessage(
												emptyMessageType
											)}
											image={Empty.PRESENTED_IMAGE_SIMPLE}
											styles={{ image: { height: 41 } }}
										/>
									),
								}}
							></DataTable>
						</div>
					</Content>
				</Layout>
			</Layout>
			{contextHolder}

			<AddBulkQuestionModal
				open={isBulkModalOpen}
				toggle={toggleBulkAddModal}
				onInit={() => console.log("uploading")}
				onFinish={() => fetchData()}
			/>
		</>
	);
}

const filterColumns = (
	columns: TableColumnsType<Question>,
	role: UserRole,
	dropDownValue: number
): TableColumnsType<Question> => {
	let filteredColumns: TableColumnsType<Question> = columns;
	if (role !== UserRole.ADMIN) {
		filteredColumns = columns.filter(item => item.title !== "Lock");
	}
	if (role !== UserRole.ADMIN && dropDownValue !== 1) {
		filteredColumns = columns.filter(
			item => item.title !== "Lock" && item.title !== "Publish"
		);
	}
	if (dropDownValue === 4) {
		filteredColumns = columns.filter(
			item => item.title !== "Lock" && item.title !== "Publish"
		);
	}
	if (dropDownValue !== 1 && role === UserRole.FACULTY) {
		filteredColumns = columns.filter(
			item => item.title !== "Lock" && item.title !== "Publish"
		);
	}
	return filteredColumns;
};
