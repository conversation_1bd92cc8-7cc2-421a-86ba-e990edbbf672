import { User } from "@/testList/data/data";
import { TestCase } from "@/testReport/data/data";

type CompileResult = {
	errors: string;
	output: string;
};

type CodeComponents = {
	solution: string;
	tail: string;
	body: string;
	head: string;
};

type CodeProgLang = {
	_id: string;
	defaultTrimmedCode: string;
	language: string;
	codeComponents: CodeComponents;
	defaultCompileResult: CompileResult;
};

type QuestionTypeCoding = {
	testCase: unknown[];
	codeproglang: CodeProgLang[];
};

type QuestionTypeWeb = {
	testCase: unknown[];
};

export interface Question {
	_id: string;
	updatedAt: string;
	createdAt: string;
	type: string;
	title: string;
	tags: string;
	createdBy: User;
	parentIdOfCreator: string;
	isPublic: boolean;
	questionTypeWeb: QuestionTypeWeb;
	questionTypeCoding: QuestionTypeCoding;
	score: number;
}

export const data: Question[] = [
	{
		_id: "67628914df8666f409a15351",
		updatedAt: "2024-12-18T08:34:29.973Z",
		createdAt: "2024-12-18T08:34:28.337Z",
		type: "4",
		title: "asdfasdfsdf",
		tags: "",
		createdBy: {
			_id: "6761569cd29748a905a079a1",
			email: "<EMAIL>",
			displayname: "Pixel org",
		},
		parentIdOfCreator: "59f9c87bbace049edfca78cf",
		isPublic: false,
		questionTypeWeb: {
			testCase: [],
		},
		questionTypeCoding: {
			testCase: [],
			codeproglang: [
				{
					_id: "67628914df8666f409a15353",
					defaultTrimmedCode:
						"/* add c headers if necessary*/\n#include <stdio.h>\nint main()\n{\n    return 0;\n}\n/* add tail for c if necessary*/",
					language: "7",
					codeComponents: {
						solution:
							"#include <stdio.h>\nint main()\n{\nddddd\n    return 0;\n}",
						tail: "/* add tail for c if necessary*/",
						body: "#include <stdio.h>\nint main()\n{\n\n    return 0;\n}",
						head: "/* add c headers if necessary*/",
					},
					defaultCompileResult: {
						errors: "",
						output: "\nOUTPUT:\n",
					},
				},
				{
					_id: "67628914df8666f409a15352",
					defaultTrimmedCode:
						"/* add c headers if necessary*/\n#include <stdio.h>\nint main()\n{\n    return 0;\n}\n/* add tail for c if necessary*/",
					language: "17",
					codeComponents: {
						solution:
							"#include <stdio.h>\nint main()\n{\ndddddd\n    return 0;\n}",
						tail: "/* add tail for c if necessary*/",
						body: "#include <stdio.h>\nint main()\n{\n\n    return 0;\n}",
						head: "/* add c headers if necessary*/",
					},
					defaultCompileResult: {
						errors: "",
						output: "\nOUTPUT:\n",
					},
				},
			],
		},
		score: 0,
	},
];

export const dummyTestCase: TestCase[] = [
	{
		sampleTest: true,
		attemptInMultiLine: false,
		codeprogexpectedoutput: "Possimus similique ",
		codeproginputparams: "Omnis nobis autem as",
		_id: "67d959ffc2f45815a0cf43aa",
		scoreip: 91,
	},
	{
		sampleTest: true,
		attemptInMultiLine: false,
		codeprogexpectedoutput: "Natus sed consectetu",
		codeproginputparams: "Qui animi corporis ",
		_id: "67d95c3544517510a86bd854",
		scoreip: 37,
	},
	{
		sampleTest: true,
		attemptInMultiLine: false,
		codeprogexpectedoutput: "Natus sed consectetu",
		codeproginputparams: "Qui animi corporis ",
		_id: "67d95c3d44517510a86bd855",
		scoreip: 37,
	},
	{
		sampleTest: false,
		attemptInMultiLine: false,
		codeprogexpectedoutput: "Similique perferendi",
		codeproginputparams: "Enim debitis digniss",
		_id: "67d95c5544517510a86bd856",
		scoreip: 61,
	},
	{
		sampleTest: false,
		attemptInMultiLine: false,
		codeprogexpectedoutput: "Similique perferendi",
		codeproginputparams: "Enim debitis digniss",
		_id: "67d95c8f44517510a86bd857",
		scoreip: 61,
	},
	{
		sampleTest: false,
		attemptInMultiLine: false,
		codeprogexpectedoutput: "Voluptate ullam pari",
		codeproginputparams: "Id molestiae enim p",
		_id: "67d95c9c44517510a86bd858",
		scoreip: 25,
	},
	{
		sampleTest: false,
		attemptInMultiLine: false,
		codeprogexpectedoutput: "Voluptate ullam pari",
		codeproginputparams: "Id molestiae enim p",
		_id: "67d95cdc44517510a86bd859",
		scoreip: 25,
	},
	{
		sampleTest: true,
		attemptInMultiLine: false,
		codeprogexpectedoutput: "Et qui aute non illu",
		codeproginputparams: "Lorem amet voluptas",
		_id: "67d9607044517510a86bd85a",
		scoreip: 77,
	},
	{
		sampleTest: true,
		attemptInMultiLine: false,
		codeprogexpectedoutput: "Sit doloremque eum ",
		codeproginputparams: "Elit ratione suscip",
		_id: "67d965f844517510a86bd85b",
		scoreip: 40,
	},
	{
		sampleTest: true,
		attemptInMultiLine: false,
		codeprogexpectedoutput: "Fuga Do et consecte",
		codeproginputparams: "Deleniti omnis aliqu",
		_id: "67da42d15a0604320cb9aa4e",
		scoreip: 72,
	},
	{
		sampleTest: false,
		attemptInMultiLine: false,
		codeprogexpectedoutput: "Nemo labore magnam p",
		codeproginputparams: "Non ut iste officia ",
		_id: "67da42ed5a0604320cb9aa4f",
		scoreip: 96,
	},
	{
		sampleTest: false,
		attemptInMultiLine: false,
		codeprogexpectedoutput: "Nemo labore magnam p",
		codeproginputparams: "Non ut iste officia ",
		_id: "67da430b5a0604320cb9aa50",
		scoreip: 96,
	},
];
