div.question-attempt-wrapper {
	flex: auto;
	display: flex;
	overflow: hidden;
}

div.question-attempt-wrapper.invalidated {
	flex-direction: column;
	justify-content: center;
	align-items: center;
}

div.question-attempt-wrapper > div.sidebar-container {
	flex: 0 0 65px;
	overflow-y: hidden;
}

div.question-attempt-wrapper > div.sidebar-container:hover {
	overflow-y: auto;
}

div.question-attempt-wrapper > div.sidebar-toggler {
	flex: 0 0 12px;
	display: flex;
	background-color: var(--primary-light);
	align-items: center;
	justify-content: center;
}

div.sidebar-toggler > button {
	height: 100%;
	padding: 0;
	width: 16px;
}

div.sidebar-toggler > button:focus:not(:focus-visible) {
	background-color: transparent;
}

div.question-attempt-wrapper > div.question-attempt-container {
	flex: auto;
	display: flex;
}

div.question-attempt-container > div > div.question-details-pane {
	display: flex;
	overflow: hidden;
	flex-direction: column;
	position: relative;
}

div.question-details-pane > div.details-pane-footer {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0.53rem 1rem;
	position: absolute;
	bottom: 0;
	width: 100%;
	background-color: #fff;
	box-shadow: 0 -2px 2px 0 #00000020;
}

div.details-pane-footer > button {
	font-family: "Hind", "sans-serif";
	font-weight: 500;
	position: relative;
}

div.details-pane-footer button > span.anticon {
	position: relative;
	top: 1px;
}

div.question-attempt-container > div > div.question-attempt-pane {
	display: flex;
}

div.question-attempt-container > div > div.question-attempt-pane-scroll {
	display: flex;
	overflow: auto;
}

div.question-attempt-container > div.question-details-attempt-divider {
	background-color: #ddd;
}

div.question-details-pane > div.question-details-container {
	display: flex;
	flex-direction: column;
	padding: 1.5rem;
	/* padding-bottom: 2.5rem; */
	margin-bottom: 3.1rem;
	min-height: 100%;
	overflow-y: auto;
}

div.question-details-pane
	> div.question-details-container::-webkit-scrollbar-thumb {
	background: #fff;
}

div.question-details-pane
	> div.question-details-container:hover::-webkit-scrollbar-thumb {
	background: #e5e5e5;
	border-radius: 0px;
}

div.question-details-pane
	> div.question-details-container:hover::-webkit-scrollbar-thumb:hover {
	background: #8e8e8e;
}

div.question-details-container > div.question-type {
	font-family: "Montserrat", sans-serif;
	font-size: 10px;
	text-transform: uppercase;
}

div.question-details-container > div.question-type > span {
	background-color: #f7f7f7;
	padding: 0.2rem;
	border-radius: 3px;
}

div.question-details-container > div.question-title {
	font-size: 22px;
	font-weight: 500;
	font-family: "Hind", sans-serif;
}

div.question-details-container div.question-description * {
	font-family: "Hind", sans-serif;
}

div.question-details-container div.question-description p,
div.question-details-container div.question-description li {
	font-size: 16px;
}

div.question-details-container div.question-description img {
	max-width: 100%;
}

div.question-details-container div.question-description pre,
div.question-details-container div.question-description code,
div.question-details-container div.question-description kbd,
div.question-details-container div.question-description samp,
div.question-details-container div.question-description pre * {
	font-family:
		"SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
	font-size: 13px;
}

div.question-details-container div.question-description pre.ql-syntax {
	background: #ebebeb;
	border: none;
	padding: 10px;
	border-radius: 4px;
}

h1.no-lang-text {
	width: 90%;
	margin: auto;
	font-weight: 700;
	margin-top: 10%;
}

.question-details-pane {
	height: 100%;
	overflow-wrap: break-word;
}
