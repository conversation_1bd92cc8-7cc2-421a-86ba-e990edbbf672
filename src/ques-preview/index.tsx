import { AttemptMCQ } from "@/components/preview-mcq";
import "./quiz-attempt.scoped.css";
import { Layout, Splitter } from "antd";
import { quizClient } from "../store";
import { useParams } from "react-router";
import { useEffect, useState } from "react";
import { Question } from "@/testReport/data/data";
import { QuestionType } from "@/client/test-add";
import PreviewSubjective from "@/components/preview-subjective/preview-subjective";
import PreviewCoding from "@/components/preview-coding/preview-coding";
import PreviewMQ from "@/components/preview-mq/preview-mq";
import PreviewWeb from "@/components/preview-web/preview-web";

const QuestionTypesMap: { [K in QuestionType]: string } = {
	[QuestionType.MCQ]: "MCQ",
	[QuestionType.SUBJECTIVE]: "Subjective",
	[QuestionType.CODING]: "Coding",
	[QuestionType.MULTIPLE]: "MQ",
	[QuestionType.WEB]: "Web",
};

export default function QuesPreview() {
	const { id } = useParams();
	const [quesData, setQuesData] = useState<Question>();

	useEffect(() => {
		if (id) {
			fetchQuestionData(id);
		}
	}, [id]);

	const fetchQuestionData = async (id: string) => {
		try {
			const res = await quizClient.getQuesPreviewData(id);
			if ("quesDetail" in res) {
				setQuesData(res.quesDetail.ques);
			}
			console.log(res);
		} catch (error) {
			console.log(error);
		}
	};

	const renderComponentByType = () => {
		if (!quesData) return null;

		switch (quesData.type) {
			case QuestionType.MCQ:
				return (
					<AttemptMCQ
						questionData={quesData?.questionTypeMCQ ?? undefined}
					/>
				);
			case QuestionType.SUBJECTIVE:
				return (
					<PreviewSubjective
						questionDetails={
							quesData?.questionTypeSubjective ?? undefined
						}
					/>
				);
			case QuestionType.CODING:
				return (
					<PreviewCoding
						questionDetails={
							quesData.questionTypeCoding ?? undefined
						}
						quesId={id ?? ""}
					/>
				);

			case QuestionType.MULTIPLE:
				return (
					<PreviewMQ
						questionData={quesData?.questionTypeCoding ?? undefined}
					/>
				);

			case QuestionType.WEB:
				return (
					<PreviewWeb
						questionId={quesData._id}
						questionData={
							quesData?.questionTypeWeb ?? {
								css: "",
								html: "",
								js: "",
								isCssAllowed: false,
								isHtmlAllowed: false,
								isJsAllowed: false,
								isReactQuestion: false,
								reactRendererCode: "",
								testRunnerFileContent: "",
								testRunnerNeeded: false,
								testCase: [],
							}
						}
					/>
				);

			default:
				return <div>Unsupported question type</div>;
		}
	};

	return (
		<Layout style={{ height: "100%" }}>
			<Splitter style={{ height: "100%" }}>
				<Splitter.Panel min={320} collapsible>
					<div className="question-details-pane">
						<div className="question-details-container">
							<div className="question-type">
								<span>
									{quesData?.type
										? QuestionTypesMap[
												quesData.type as QuestionType
											]
										: ""}
								</span>
							</div>
							<div className="question-title">
								<span>{quesData?.title}</span>
							</div>
							<div
								className="fancy-divider"
								style={{
									minHeight: 5.5,
									maxHeight: 5.5,
									margin: "0.25rem 0 0.45rem 0",
									maxWidth: 250,
								}}
							>
								<div />
								<div />
								<div />
							</div>
							<div
								className={`question-description selectable`}
								dangerouslySetInnerHTML={{
									__html: quesData?.text ?? "",
								}}
							/>
						</div>
					</div>
				</Splitter.Panel>
				<Splitter.Panel min={320}>
					{renderComponentByType()}
				</Splitter.Panel>
			</Splitter>
		</Layout>
	);
}
