:root {
	--primary-color: #de6834;
	--hue: 18.4;
	--lightness: 53.7%;
	--secondary-color: hsl(var(--hue), 81%, calc(var(--lightness) + 42%));
	--primary-border: hsl(var(--hue), 72%, calc(var(--lightness) + 18%));
	--primary-lighter: hsl(var(--hue), 36%, calc(var(--lightness) + 42%));
	--primary-light: hsl(var(--hue), 100%, calc(var(--lightness) + 40%));
	--primary-medium: hsl(var(--hue), 58%, calc(var(--lightness) + 27%));
	--primary-medium-dark: hsl(var(--hue), 55%, calc(var(--lightness) + 10%));
	--primary-medium-light: hsl(var(--hue), 45%, calc(var(--lightness) + 20%));
	--primary-dark: hsl(var(--hue), 79%, calc(var(--lightness) - 17%));
	--input-border: hsl(var(--hue), 93%, calc(var(--lightness) + 10%));
	--primary-hover: hsl(var(--hue), 66%, calc(var(--lightness) - 3%));
	--loading-1: hsl(var(--hue), 67%, calc(var(--lightness) - 7%));
	--loading-2: hsl(var(--hue), 53%, calc(var(--lightness) + 7%));
	--loading-3: hsl(var(--hue), 80%, calc(var(--lightness) - 18%));
	--slider-bg: hsl(var(--hue), 69%, calc(var(--lightness) + 37%));
	--slider-fg: hsl(var(--hue), 96%, calc(var(--lightness) + 17%));
	--primary-bg: #fff;
	--primary-bg-1: #ededed;
	--primary-text: #000;
	--select-bg: #d1d1d1;
}
