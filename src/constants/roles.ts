export const enum UserRole {
	ADMIN = 0,
	USER = 1,
	FACULTY = 2,
	SUB_ADMIN = 3,
	CONTENT_CREATOR = 4,
	SUPPORT = 5,
	CUSTOM = 7,
	SUPER_ORG = 8,
}

export const enum RoleAction {
	COURSE_ADMIN_PERMISSION = -1,
	ADD = 1,
	EDIT = 2,
	LIST = 3,
	DELETE = 4,
	DETAILS = 5,
	BATCH_IN_COURSE = 6,
	ADD_STUDENT = 7,
	REMOVE_STUDENT = 8,
	BULK = 9,
	QUESTION_IN_COURSE = 10,
	QUESTION_IN_QUIZ = 11,
	PREVIEW = 12,
	DETAIL = 13,
	ADD_FILE = 14,
	EMAIL_TEMPLATE = 15,
	EXPORT = 16,
	AWS = 17,
	VIEW = 18,
	TUTORIAL_IN_COURSE = 19,
	COMPLETE = 20,
	CLONE = 21,
	REMOVE_MENTOR = 22,
	CREATE_SEGMENTS = 23,
	ADD_BATCH = 24,
	REMOVE_BATCH = 25,
	LINK = 26,
	REPORT = 27,
	PROVIDE_EXTRA_TIME = 28,
	COURSE_ATTEMPT = 29,
	QUIZ_ATTEMPT = 30,
	MENTOR_IN_COURSE = 31,
	ARCHIVE = 32,
	SET_QUIZ = 33,
	DOC = 34,
	ADD_CONTENT = 35,
	REMOVE_CONTENT = 36,
	DASHBOARD = 37,
	DATA = 38,
	ADD_MENTOR = 39,
	CREATE = 40,
	JOIN = 41,
	UPDATE_STATUS = 42,
	VALIDATE = 43,
	START = 44,
	LOGOUT = 45,
	FORCE_LOGIN = 46,
	QUIZ_QUESTION = 47,
	QUESTION = 48,
	TUTORIAL = 49,
	CONTENT_LINK = 50,
	COMPLETE_CLASS = 51,
	REMAINING_TIME = 52,
	USER_REPORT = 53,
	CLASS_REPORT = 54,
	ENABLE_PROJECT = 55,
	ENABLE_DB = 56,
	SEND_MAIL = 57,
	ENABLE_USER = 58,
	CLASS_IN_COURSE = 59,
	TEST_IN_COURSE = 60,
	BULK_ADD = 61,
	BULK_ASSIGN = 62,
	BULK_UPDATE = 63,
	BULK_DELETE = 64,
	BULK_STATUS_UPDATE = 65,
	BULK_FOLLOW_UPDATE = 66,
	FOLLOW_UPDATE = 67,
	ASSIGN_TO = 68,
	UPDATE = 69,
	ACTIVITY = 70,
	ALL_LEADS = 71,
	CREATE_LINK = 72,
	LAST_CALLED = 73,
	EXPORT_ALL = 74,
	SUBJECTIVE_UPLOAD = 75,
	SUSPICIOUS = 76,
	ENROLMENT_BATCH = 77,
	PROJECT_QUESTION = 78,
	TRACE_TABLE_QUESTION = 79,
	ALLOW_ALL_TEST_SUBMISSION = 80,
}

export const enum RoleResource {
	USER = 1,
	BATCH = 2,
	QUEST = 3,
	TUTORIAL = 4,
	COURSE = 5,
	QUIZ = 6,
	SNAPSHOT = 7,
	TAGS = 8,
	ATTEMPTS = 9,
	SUGGEST_TEST_CASE = 10,
	ACC = 11,
	PROJECT = 12,
	ADMIN_PROJECT = 13,
	MENTOR = 14,
	ERROR = 15,
	POOL_QUEST = 16,
	CLASS = 17,
	FEEDBACK = 18,
	USER_QUIZ = 19,
	ATTEMPT = 20,
	USER_DASHBOARD = 21,
	BLOG = 22,
	INSTITUTION = 23,
	USER_BATCH_ACTIVITY = 24,
	CONTACT = 25,
	LEADS = 26,
	EMAIL = 28,
	QUIZ_DASHBOARD = 31,
}
