import { StepProps } from "antd";

export const ProctoringType = {
	ALL: 0,
	USER_EXIT: 1,
	USER_ENTER: 2,
	PROHIBITED_ITEMS: 3,
	MULTIPLE_PERSON: 4,
	RANDOM: 5,
};

export const subTypeMap: { [key: number]: string } = {
	[ProctoringType.ALL]: "All",
	[ProctoringType.USER_EXIT]: "User exit",
	[ProctoringType.USER_ENTER]: "User enter",
	[ProctoringType.PROHIBITED_ITEMS]: "Prohibited items",
	[ProctoringType.MULTIPLE_PERSON]: "Multiple person",
	[ProctoringType.RANDOM]: "Random",
};

export const enum CustomFieldType {
	TEXT = 0,
	SELECT = 1,
	CHECKBOX = 2,
	RADIO = 3,
}

export const CustomFieldTypeMap = {
	[CustomFieldType.TEXT]: { title: "Text" },
	[CustomFieldType.SELECT]: { title: "Dropdown" },
	[CustomFieldType.CHECKBOX]: { title: "Checkbox" },
	[CustomFieldType.RADIO]: { title: "Radio" },
};

export interface CustomField {
	key: string;
	label: string;
	type: CustomFieldType;
	values?: string[];
	active: boolean;
	editable: boolean;
	inEditMode: boolean;
}

export const defaultCustomFields: {
	key: string;
	label: string;
	type: CustomFieldType;
	active: boolean;
	values?: string[];
}[] = [
	{
		key: "id",
		label: "ID",
		type: CustomFieldType.TEXT,
		active: false,
	},
	{
		key: "group",
		label: "Group",
		type: CustomFieldType.TEXT,
		active: false,
	},
	{
		key: "college",
		label: "College",
		type: CustomFieldType.TEXT,
		active: false,
	},
	{
		key: "city",
		label: "City",
		type: CustomFieldType.TEXT,
		active: false,
	},
	{
		key: "state",
		label: "State",
		type: CustomFieldType.TEXT,
		active: false,
	},
];

export const defaultFieldKeys = new Set(
	defaultCustomFields.map(field => field.key)
);

export const markQuizAsResolvedStaged: Array<StepProps> = [
	{
		title: "Verifying Eligibility",
		description: "Checking if this quiz can be marked as resolved...",
	},
	{
		title: "Submitting Pending Attempts",
		description: "Finalizing any remaining quiz submissions...",
	},
	{
		title: "Completing Resolution",
		description: "Marking the quiz as resolved and finalized...",
	},
];
