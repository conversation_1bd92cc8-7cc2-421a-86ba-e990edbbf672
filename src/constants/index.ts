export { ProctoringType, subTypeMap } from "./test";
import * as Roles from "./roles";
export { Roles };

export const platformTitle = ["CodeQuotient", "Chitkara"];

export enum PlatformOrganisation {
	CQ = "cq",
	CHITKARA = "chitkara",
}

export const aiProctorScore = {
	illegalObject: 5,
	multipleUser: 3,
	userExit: 0.1,
	userEnter: 0.1,
};

export const tabsData = {
	general: {
		title: "General",
		description: "Manage basic settings such as title and duration.",
	},
	access: {
		title: "Access",
		description:
			"Configure access settings, including test availability, link, code, IP restrictions, and result visibility.",
	},
	content: {
		title: "Content",
		description:
			"Define content settings such as cut-off marks, allowed languages, keywords, question shuffling, randomization, section revisit rules, and custom fields.",
	},
	proctoring: {
		title: "Proctoring",
		description:
			// "Set up monitoring options such as webcam, screen recording, and anti-cheating measures.",
			"Configure test security settings, including tab switch prevention, copy-paste restrictions, fullscreen enforcement, webcam monitoring, secured mode, and recording.",
	},
	notifications: {
		title: "Notifications",
		description:
			"Set up automated email notifications for users and organizations, including test completion subject and message personalization.",
	},
};

export enum FeedBackRating {
	Poor = 1,
	Average = 2,
	Good = 3,
	Excellent = 4,
}
