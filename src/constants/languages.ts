// if you change this here please also change it in the CQ_Quiz_Server/Lib/constants.js
export const QuestionSupportedLanguage = {
	C: 7,
	C99: 17,
	"C++": 77,
	"C++11": 18,
	"C++14": 19,
	Java: 8,
	JavaScript: 4,
	Bash: 11,
	<PERSON><PERSON>: 5,
	MySql: 23,
	Oracle: 24,
	"Python 2": 0,
	"Python 3": 20,
	Ruby: 1,
	"C#": 10,
	Php: 3,
	"React-Jest": 2,
} as const;

export const QuestionSupportedValueToName = Object.fromEntries(
	Object.entries(QuestionSupportedLanguage).map(([k, v]) => [v, k])
);

export type QuestionSupportedLanguageValue =
	(typeof QuestionSupportedLanguage)[keyof typeof QuestionSupportedLanguage];
