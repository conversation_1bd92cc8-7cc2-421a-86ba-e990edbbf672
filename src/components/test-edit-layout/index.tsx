import { use<PERSON>allback, useEffect, useMemo, useState } from "react";
import { Flex, Space, Spin, Tabs, message } from "antd";
import { ExclamationCircleOutlined } from "@ant-design/icons";
import {
	Navigate,
	Outlet,
	useLocation,
	useMatch,
	useNavigate,
	useOutletContext,
	useParams,
} from "react-router";
import {
	TestData,
	TestSaveProgressStep,
	TestUpdateEndPointsMap,
} from "@/client/test-add";
import { quizClient } from "../../store";
import { TextLayoutContextType } from "../test-layout/test-layout";
import { tabsData, PlatformOrganisation } from "@/constants";
import { AppConfig } from "@/config";
import { useAppMessage } from "@/hooks/message";

type TabsKey = keyof TestUpdateEndPointsMap;

const allItems = [
	{
		key: "general",
		title: tabsData["general"].title,
	},
	{
		key: "access",
		title: tabsData["access"].title,
	},
	{
		key: "content",
		title: tabsData["content"].title,
	},
	{
		key: "proctoring",
		title: tabsData["proctoring"].title,
	},
	{
		key: "notifications",
		title: tabsData["notifications"].title,
	},
] as const;

export type TabsRequestMap = TestData["tabs"];

export type TestEditLayoutContextType = {
	progressStep?: TestSaveProgressStep;
	submissionType: "add" | "update" | "cloneQuizByToken";
	isFrozen: boolean;
	isFromToken: boolean;
	isTemplate: boolean;
	tabsData: Partial<TabsRequestMap>;
	disabledTabs: Set<TabsKey>;
	/** Enable the specified tab */
	enableTab: (id: TabsKey) => void;
	/** Disable the specified tab */
	disableTab: (id: TabsKey) => void;
	/** Disable all tabs except the specified one */
	disableTabs: (except: TabsKey) => void;
	/** Check if previous the previous tab can be visited or not */
	canVisitPreviousTab: () => boolean;
	/** Change the active tab to previous tab */
	visitPreviousTab: () => void;
	/** Check if next tab can be visited or not */
	canVisitNextTab: () => boolean;
	/** Change the active tab to Next tab */
	visitNextTab: () => void;
	/** Save the specified tab's data */
	saveTab: <K extends TabsKey>(
		id: K,
		values: TabsRequestMap[K]
	) => Promise<void>;
	unsavedTabs: TabsKey[];
	addUnsavedTab: (tabId: TabsKey) => void;
	removeUnsavedTab: (tabId: TabsKey) => void;
	isPartialCreated: () => boolean;
};

export default function TestEditLayout(props: {
	type: "add" | "update" | "cloneQuizByToken";
}) {
	const navigate = useNavigate();
	const location = useLocation();
	const params = useParams();
	const testId = params.id as string;
	const { token } = useParams();

	const match = useMatch(
		props.type === "add"
			? "/tests/add/*"
			: props.type === "update"
				? "/tests/:id/update/*"
				: `/tests/cloneQuizByToken/:token?`
	);
	const matchWithToken = useMatch(
		"/tests/cloneQuizByToken/:token/settings/:activeKey"
	);
	const activeTabKey = useMemo(() => {
		if (token) {
			const activeTabKey = matchWithToken?.params.activeKey;
			return activeTabKey as TabsKey;
		}
		return location.pathname
			.replace(match?.pathnameBase ?? "", "")
			.split("/")
			.pop() as TabsKey;
	}, [
		token,
		location.pathname,
		match?.pathnameBase,
		matchWithToken?.params.activeKey,
	]);

	const messageInstance = useAppMessage();
	const {
		progressStep,
		isFrozen = false,
		isTemplate = false,
		isFromToken = false,
		tabsData,
		setTabsData,
		setProgressStep,
		unsavedTabs,
		setUnsavedTabs,
	} = useOutletContext<TextLayoutContextType>() ?? {};

	const items = allItems.filter(item => {
		if (AppConfig.platformOrganisation === PlatformOrganisation.CHITKARA) {
			return item.key !== "notifications";
		}
		return true;
	});

	const tabKeysSet = new Set(items.map(item => item.key));

	// State to manage the disabled tabs
	const [disabledTabs, setDisabledTabs] = useState<Set<TabsKey>>(
		new Set(
			testId
				? []
				: items.map(item => item.key).filter(item => item !== "general")
		)
	);

	useEffect(() => {
		if (setUnsavedTabs) {
			setUnsavedTabs([]);
		}
	}, [setUnsavedTabs]);

	useEffect(
		function () {
			switch (progressStep) {
				case TestSaveProgressStep.GENERAL:
					enableTab("general");
					disableTab("content");
					disableTab("proctoring");
					disableTab("notifications");
					// navigate("access");
					break;
				case TestSaveProgressStep.ACCESS:
					enableTab("access");
					enableTab("content");
					disableTab("proctoring");
					disableTab("notifications");
					// navigate("content");
					break;
				case TestSaveProgressStep.CONTENT:
					enableTab("general");
					enableTab("access");
					enableTab("content");
					enableTab("proctoring");
					disableTab("notifications");
					// navigate("proctoring");
					break;
				case TestSaveProgressStep.PROCTORING:
				case TestSaveProgressStep.NOTIFICATIONS:
					enableTab("general");
					enableTab("access");
					enableTab("content");
					enableTab("proctoring");
					if (
						AppConfig.platformOrganisation ===
						PlatformOrganisation.CQ
					) {
						enableTab("notifications");
					}
					break;
				default:
					enableTab("general");
					disableTab("access");
					disableTab("content");
					disableTab("proctoring");
					disableTab("notifications");
			}
		},

		[progressStep]
	);

	const addUnsavedTab = useCallback(
		function (tabId: TabsKey) {
			const unsavedTabsSet = new Set(unsavedTabs);
			unsavedTabsSet.add(tabId);
			setUnsavedTabs(Array.from(unsavedTabsSet));
		},
		[setUnsavedTabs, unsavedTabs]
	);

	const removeUnsavedTab = useCallback(
		function (tabId: TabsKey) {
			const unsavedTabsSet = new Set(unsavedTabs);
			unsavedTabsSet.delete(tabId);
			setUnsavedTabs(Array.from(unsavedTabsSet));
		},
		[setUnsavedTabs, unsavedTabs]
	);

	if (!tabKeysSet.has(activeTabKey) || disabledTabs.has(activeTabKey)) {
		return <Navigate to="general" replace />;
	}

	function enableTab(id: TabsKey) {
		setDisabledTabs(prev => {
			const newSet = new Set(prev);
			newSet.delete(id);
			return newSet;
		});
	}

	function disableTab(id: TabsKey) {
		setDisabledTabs(prev => {
			const newSet = new Set(prev);
			newSet.add(id);
			return newSet;
		});
	}

	function disableTabs(except: TabsKey) {
		const newSet = new Set(items.map(item => item.key));
		newSet.delete(except);
		setDisabledTabs(newSet);
	}

	function saveTabData<K extends TabsKey>(key: K, values: TabsRequestMap[K]) {
		setTabsData({ ...tabsData, [key]: values });
	}

	function canVisitPreviousTab() {
		switch (activeTabKey) {
			case "general":
				return false;
		}
		return true;
	}
	function canVisitNextTab() {
		switch (activeTabKey) {
			case "general":
				return !disabledTabs.has("access");
			case "access":
				return !disabledTabs.has("content");
			case "content":
				return !disabledTabs.has("proctoring");
			case "proctoring":
				if (
					AppConfig.platformOrganisation ===
					PlatformOrganisation.CHITKARA
				) {
					return false;
				}
				return !disabledTabs.has("notifications");
			case "notifications":
				return false;
		}
	}

	function isPartialCreated() {
		if (
			AppConfig.platformOrganisation === PlatformOrganisation.CHITKARA &&
			progressStep !== TestSaveProgressStep.PROCTORING
		) {
			return true;
		} else if (
			AppConfig.platformOrganisation === PlatformOrganisation.CQ &&
			progressStep !== TestSaveProgressStep.NOTIFICATIONS
		) {
			return true;
		}
		return false;
	}

	function getRedirectionUrl(tab: string) {
		return tab;
	}

	function visitPreviousTab() {
		switch (activeTabKey) {
			case "access":
				navigate(getRedirectionUrl("general"));
				break;
			case "content":
				navigate(getRedirectionUrl("access"));
				break;
			case "proctoring":
				navigate(getRedirectionUrl("content"));
				break;
			case "notifications":
				navigate(getRedirectionUrl("proctoring"));
				break;
		}
	}
	function visitNextTab() {
		switch (activeTabKey) {
			case "general":
				navigate(getRedirectionUrl("access"));
				break;
			case "access":
				navigate(getRedirectionUrl("content"));
				break;
			case "content":
				navigate(getRedirectionUrl("proctoring"));
				break;
			case "proctoring":
				navigate(getRedirectionUrl("notifications"));
				break;
		}
	}

	async function saveTab<K extends TabsKey>(
		tabId: K,
		values: TabsRequestMap[K]
	) {
		try {
			switch (tabId) {
				case "general":
					{
						const { test } = await quizClient.createTestDetails(
							values as TabsRequestMap["general"],
							testId
						);
						saveTabData(tabId, values);
						removeUnsavedTab("general");
						if (disabledTabs.has("access")) {
							setProgressStep(TestSaveProgressStep.GENERAL);
						}
						messageInstance?.success("Test general details saved");
						if (testId === undefined) {
							return navigate(`/tests/${test._id}/update/access`);
						}
						if (disabledTabs.has("access")) {
							enableTab("access");
							navigate("access");
						}
					}
					break;

				case "access":
					await quizClient.saveTestAccess(
						testId,
						values as TabsRequestMap["access"]
					);
					saveTabData(tabId, values);
					removeUnsavedTab("access");
					if (disabledTabs.has("content")) {
						enableTab("content");
						navigate("content");
						setProgressStep(TestSaveProgressStep.ACCESS);
					}
					messageInstance?.success("Test access details saved");
					break;

				case "content": {
					let isValuesMissing = false;
					if ("customFields" in values) {
						values.customFields.forEach(field => {
							if (field.type !== 0) {
								if (field.values && field.values.length === 0) {
									isValuesMissing = true;
									messageInstance?.error(
										`Please Enter The Custom Field Values in ${field.label}`
									);
								}
							}
						});
					}
					if (isValuesMissing) {
						return;
					}
					await quizClient.saveTestContent(
						testId,
						values as TabsRequestMap["content"]
					);
					saveTabData(tabId, values);
					removeUnsavedTab("content");
					if (disabledTabs.has("proctoring")) {
						enableTab("proctoring");
						navigate("proctoring");
						setProgressStep(TestSaveProgressStep.CONTENT);
					}
					messageInstance?.success("Test content settings saved");
					break;
				}

				case "proctoring":
					await quizClient.saveTestProctoring(
						testId,
						values as TabsRequestMap["proctoring"]
					);
					saveTabData(tabId, values);
					removeUnsavedTab("proctoring");
					if (disabledTabs.has("notifications")) {
						setProgressStep(TestSaveProgressStep.PROCTORING);
					}
					messageInstance?.success("Test proctoring settings saved");
					if (
						AppConfig.platformOrganisation ===
							PlatformOrganisation.CQ &&
						disabledTabs.has("notifications")
					) {
						enableTab("notifications");
						navigate("notifications");
					} else if (
						progressStep &&
						progressStep < TestSaveProgressStep.PROCTORING
					) {
						navigate(`/tests/${testId}/content`);
					}
					break;

				case "notifications":
					await quizClient.saveTestNotifications(
						testId,
						values as TabsRequestMap["notifications"]
					);
					saveTabData(tabId, values);
					removeUnsavedTab("notifications");
					setProgressStep(TestSaveProgressStep.NOTIFICATIONS);
					messageInstance?.success(
						"Test notifications settings saved"
					);
					if (
						progressStep &&
						progressStep < TestSaveProgressStep.NOTIFICATIONS
					) {
						navigate(`/tests/${testId}/content`);
					}
					break;
			}
		} catch (ex) {
			messageInstance?.error(ex as string);
		}
	}

	function onTabChange(key: string) {
		if (unsavedTabs.length === 0) {
			return navigate(getRedirectionUrl(key));
		}
		message.info("Please save the changes first");
	}

	if (!tabsData) {
		return (
			<Flex
				align="center"
				justify="center"
				style={{ width: "100%", height: "100%" }}
			>
				<Spin />
			</Flex>
		);
	}

	const contextValue: TestEditLayoutContextType = {
		progressStep,
		submissionType: props.type,
		isFrozen,
		isFromToken,
		isTemplate,
		tabsData,
		disabledTabs,
		unsavedTabs,
		addUnsavedTab,
		removeUnsavedTab,
		enableTab,
		disableTab,
		disableTabs,
		saveTab,
		canVisitPreviousTab,
		visitPreviousTab,
		visitNextTab,
		canVisitNextTab,
		isPartialCreated,
	};

	return (
		<>
			<Flex
				justify="center"
				style={{ marginLeft: "15%", height: "100%" }}
			>
				<Tabs
					size="small"
					tabBarStyle={{ textAlign: "right" }}
					tabBarGutter={0}
					indicator={{ size: 32 }}
					activeKey={activeTabKey}
					onChange={onTabChange}
					tabPosition="left"
					destroyInactiveTabPane
					// renderTabBar={(props, DefaultTabBar) => (
					// 	<DefaultTabBar
					// 		{...props}
					// 		style={{ position: "sticky", top: "0px" }}
					// 	/>
					// )}
					style={{
						width: "100%",
						height: "100%",
						overflow: "hidden",
					}}
					items={items.map(item => ({
						disabled: contextValue.disabledTabs.has(item.key),
						key: item.key,
						style: { maxHeight: "100%", overflow: "auto" },
						label: (
							<div
								style={{
									minWidth: "160px",
									textAlign: "right",
								}}
							>
								<Space>
									{unsavedTabs.includes(item.key) && (
										<ExclamationCircleOutlined
											style={{ color: "#f62" }}
										/>
									)}
									{item.title}
								</Space>
							</div>
						),
						children: (
							<Flex
								style={{
									padding: "0.5rem 0.5rem 1.5rem 0px",
									// border: "1px solid red",
									height: "100%",
									// maxHeight: "100vh",
									// overflow: "auto",
								}}
							>
								{item.key === activeTabKey ? (
									<Outlet context={contextValue} />
								) : (
									<></>
								)}
							</Flex>
						),
					}))}
				></Tabs>
			</Flex>
		</>
	);
}
