import React, { useContext, useMemo } from "react";
import { HolderOutlined } from "@ant-design/icons";
import type { DragEndEvent, UniqueIdentifier } from "@dnd-kit/core";
import { DndContext } from "@dnd-kit/core";
import type { SyntheticListenerMap } from "@dnd-kit/core/dist/hooks/utilities";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import {
	SortableContext,
	useSortable,
	verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Button, Table } from "antd";
import type { GetProps, TableColumnsType } from "antd";

interface RowContextProps {
	setActivatorNodeRef?: (element: HTMLElement | null) => void;
	listeners?: SyntheticListenerMap;
}

const RowContext = React.createContext<RowContextProps>({});

const DragHandle: React.FC = () => {
	const { setActivatorNodeRef, listeners } = useContext(RowContext);
	return (
		<Button
			type="text"
			size="small"
			icon={<HolderOutlined />}
			style={{ cursor: "move" }}
			ref={setActivatorNodeRef}
			{...listeners}
		/>
	);
};

interface RowProps extends React.HTMLAttributes<HTMLTableRowElement> {
	"data-row-key": string;
}

const Row: React.FC<RowProps> = props => {
	const {
		attributes,
		listeners,
		setNodeRef,
		setActivatorNodeRef,
		transform,
		transition,
		isDragging,
	} = useSortable({ id: props["data-row-key"] });

	const style: React.CSSProperties = {
		...props.style,
		transform: CSS.Translate.toString(transform),
		transition,
		...(isDragging ? { position: "relative", zIndex: 9999 } : {}),
	};

	const contextValue = useMemo<RowContextProps>(
		() => ({ setActivatorNodeRef, listeners }),
		[setActivatorNodeRef, listeners]
	);

	return (
		<RowContext.Provider value={contextValue}>
			<tr {...props} ref={setNodeRef} style={style} {...attributes} />
		</RowContext.Provider>
	);
};

interface DragSortTableRowType {
	key: string;
}

interface DragSortTableProps<T extends DragSortTableRowType, M extends boolean>
	extends GetProps<typeof Table<T>> {
	moveByKey?: M;
	disabled?: boolean;
	moveData: M extends false
		? (from: number, to: number) => void
		: (recordKey: UniqueIdentifier, to: number) => void;
}

export default function DragSortTable<
	T extends DragSortTableRowType,
	M extends boolean = false,
>(props: DragSortTableProps<T, M>) {
	const {
		columns: initialColumns = [],
		dataSource = [],
		moveByKey = false,
		moveData,
		disabled = false,
		...tableProps
	} = props;
	const sortColumns: TableColumnsType<T> = [
		{
			key: "_sort",
			align: "center",
			width: 50,
			render: () => (dataSource.length <= 1 ? <></> : <DragHandle />),
		},
	];
	const columns: TableColumnsType<T> = [
		...(disabled ? [] : sortColumns),
		...initialColumns,
	];

	const onDragEnd = ({ active, over }: DragEndEvent) => {
		if (active.id !== over?.id) {
			const overIndex = dataSource.findIndex(
				record => record.key === over?.id
			);
			if (moveByKey) {
				return moveData(active?.id as number, overIndex);
			}
			const activeIndex = dataSource.findIndex(
				record => record.key === active?.id
			);
			return moveData(activeIndex, overIndex);
		}
	};

	return (
		<DndContext modifiers={[restrictToVerticalAxis]} onDragEnd={onDragEnd}>
			<SortableContext
				items={dataSource.map(i => i.key)}
				strategy={verticalListSortingStrategy}
				disabled={dataSource.length <= 1}
			>
				<Table<T>
					rowKey="key"
					components={{ body: { row: Row } }}
					columns={columns}
					dataSource={dataSource}
					pagination={false}
					{...tableProps}
				/>
			</SortableContext>
		</DndContext>
	);
}
