import { quizClient, useQuestionStore } from "@/store";
import { <PERSON><PERSON>, Card, Flex, Form, Spin, Tabs } from "antd";
import { useForm } from "antd/es/form/Form";
import { CheckOutlined, CloseOutlined } from "@ant-design/icons";

import { useCallback, useEffect, useState } from "react";
import useMessage from "antd/es/message/useMessage";
import { useSocketMutation, useSocketWatch } from "@/hooks/socket";
import { CQEditor } from "../editor";
import { QuestionSupportedValueToName } from "@/constants/languages";
import { Console } from "@/components/console";
import { useNavigate } from "react-router";
import useModal from "antd/es/modal/useModal";
import { useAppMessage } from "../../hooks/message";
import { TestCase } from "@/@types/client";
import TestCaseModal from "../testcase-modal/testcaseModal";

interface CodeBody {
	language: string;
	head: string;
	body: string;
	tail: string;
	solution?: string | undefined;
}

interface FormInstanceDataType {
	code: CodeBody[];
}

export interface OutputArray {
	userOutput: string;
	testCasePassed: boolean;
	score: number;
	testCase?: TestCase;
}

interface CompileResponse {
	language: number;
	errors?: string;
	outputArray?: Array<OutputArray>;
}

export const QuestionSolution = ({
	quesId,
	isNewQues,
	isFromTestModal,
}: {
	quesId?: string;
	isNewQues?: boolean;
	isFromTestModal: boolean;
}) => {
	const messageInstance = useAppMessage();
	const [formInstance] = useForm<FormInstanceDataType>();
	const initialQuestionData = useQuestionStore().initialQuestionData;
	const [message, context] = useMessage();
	const [modal, modalContext] = useModal();
	const navigate = useNavigate();
	const [saving, setSaving] = useState<boolean>(false);
	const [running, setRunning] = useState<Record<string, boolean>>({});
	const [activeTestCase, setActiveTestCase] = useState<
		(TestCase & { name: string }) | null
	>(null);
	const [responseObj, setResponseObj] = useState<
		Record<string, CompileResponse>
	>({});

	useSocketWatch("compile", {
		listnerFunction: data => {
			if (!data) {
				return;
			}
			const lang = parseInt(data.progLang);
			const outputArray: Array<OutputArray> = [];
			(data.outputArray ?? []).forEach((ele, index) => {
				outputArray.push({
					...ele,
					testCase: data?.testCase[index] ?? null,
				});
			});
			setResponseObj(prev => ({
				...prev,
				[lang]: {
					language: lang,
					errors: data.errors,
					outputArray: outputArray,
				},
			}));
			setRunning(prev => ({
				...prev,
				[lang]: false,
			}));
			return;
		},
		key: [quesId],
	});

	const { mutateAsync } = useSocketMutation<
		[string, { code: string; language: string }],
		void
	>(
		async (
			socket,
			questionId: string,
			code: { language: string; code: string }
		) => {
			socket.emit("compile", {
				code: code.code,
				language: code.language,
				questionId: questionId,
				stdin: "",
				isInvalidAttempt: true,
			});
			return;
		}
	);

	useEffect(() => {
		const codeComponentArray: FormInstanceDataType = { code: [] };
		if ("ques" in initialQuestionData) {
			initialQuestionData.ques.questionTypeCoding?.codeproglang.forEach(
				ele => {
					const obj = {
						body: ele.codeComponents.solution ?? "",
						head: ele.codeComponents.head ?? "",
						tail: ele.codeComponents.tail ?? "",
						language: ele.language,
					};
					if (!obj.body) {
						obj.body = ele.codeComponents.body ?? "";
					}
					codeComponentArray.code.push(obj);
				}
			);
		}
		formInstance.setFieldValue("code", codeComponentArray.code);
	}, [formInstance, initialQuestionData]);

	const calculateHeight = useCallback((text: string) => {
		const lines = text.split("\n").length;
		return `${Math.min(Math.max(lines * 26, 50), 100)}px`;
	}, []);

	const handleRun = useCallback(
		async (language: string) => {
			if (!quesId) {
				return;
			}
			setRunning(prev => ({
				...prev,
				[language]: true,
			}));
			const codeObj = formInstance.getFieldsValue();
			const codeToCompile = codeObj.code.find(
				ele => ele.language == language
			);
			if (!codeToCompile) {
				return;
			}
			const code = `${codeToCompile?.body ?? ""}`;
			try {
				await mutateAsync(quesId, {
					language: language.toString(),
					code,
				});
			} catch (error) {
				console.error(error);
			}
		},
		[formInstance, quesId, mutateAsync]
	);

	useEffect(() => {
		setResponseObj({});
	}, [quesId]);

	const saveSolutions = useCallback(
		async (value: FormInstanceDataType) => {
			try {
				setSaving(true);
				const content: Record<number, string> = {};
				value.code.forEach(singleCode => {
					content[parseInt(singleCode.language)] = singleCode.body;
				});
				await quizClient.saveSolution(quesId!, content);
				message.success("Success");
			} catch (error) {
				if (error instanceof Error) {
					return message.error(error?.message);
				}
				if (typeof error === "string") {
					return message.error(error);
				}
				return message.error("Something went wrong");
			} finally {
				setSaving(false);
			}
		},
		[message, quesId]
	);

	const exportQuestionToCourse = async (instance: {
		destroy: () => void;
		update: (config: { cancelButtonProps?: { disabled: boolean } }) => void;
	}) => {
		try {
			instance.update({
				cancelButtonProps: { disabled: true },
			});

			const response = await quizClient.exportQuestion(quesId!);
			if ("error" in response) {
				throw new Error(response.error);
			}
			messageInstance?.success("Question exported successfully.");
			if (!isFromTestModal) {
				navigate("/questions");
			}
		} catch (error) {
			message.error(
				error instanceof Error ? error.message : String(error)
			);
			console.log(error);
		} finally {
			instance.destroy();
		}
	};

	if (!initialQuestionData) {
		return (
			<>
				<Spin />
			</>
		);
	}

	return (
		<div style={{ margin: "auto", padding: 16, maxWidth: "1400px" }}>
			{modalContext}
			{context}
			{activeTestCase && (
				<TestCaseModal
					title={"Preview Test Case"}
					previewTestCase={{
						name: activeTestCase.name,
						scoreip: activeTestCase.scoreip,
						codeproginputparams: activeTestCase.codeproginputparams,
						codeprogexpectedoutput:
							activeTestCase.codeprogexpectedoutput,
					}}
					showModal={!!activeTestCase}
					onCancel={() => {
						setActiveTestCase(null);
					}}
				/>
			)}
			<Form form={formInstance} onFinish={saveSolutions}>
				<Card
					styles={{
						body: {
							padding: 0,
							border: "1px solid #ddd",
							overflow: "hidden",
							borderRadius: 8,
						},
					}}
				>
					<Form.List name="code">
						{fields => {
							const tabItems = fields.map(({ key, name }) => {
								const lang = formInstance.getFieldValue([
									"code",
									name,
									"language",
								]);

								const label = QuestionSupportedValueToName[
									lang as keyof typeof QuestionSupportedValueToName
								] as unknown as string;

								return {
									key: key.toString(),
									label: label,
									children: (
										<>
											<Form.Item
												className="read-only-ace"
												name={[name, "head"]}
											>
												<CQEditor
													lang={lang}
													readOnly={true}
													size={{
														width: "100%",
														height: calculateHeight(
															formInstance.getFieldValue(
																[
																	"code",
																	name,
																	"head",
																]
															)
														),
													}}
													enableSyntaxSupport={false}
													className="solution"
													onLoad={editor => {
														editor.session.foldAll();
														editor.renderer.setPadding(
															5
														);
														editor.renderer.setScrollMargin(
															5,
															5,
															0,
															0
														);
														setTimeout(() => {
															editor.resize(true);
															editor.session.foldAll();
														}, 100);
													}}
												/>
											</Form.Item>
											<Form.Item name={[name, "body"]}>
												<CQEditor
													lang={lang}
													size={{
														width: "100%",
														height: "300px",
													}}
													enableSyntaxSupport={true}
													className="solution"
												/>
											</Form.Item>
											<Form.Item name={[name, "tail"]}>
												<CQEditor
													lang={lang}
													readOnly={true}
													size={{
														width: "100%",
														height: calculateHeight(
															formInstance.getFieldValue(
																[
																	"code",
																	name,
																	"tail",
																]
															)
														),
													}}
													enableSyntaxSupport={false}
													className="solution"
													onLoad={editor => {
														editor.renderer.setPadding(
															5
														);
														editor.renderer.setScrollMargin(
															5,
															5,
															0,
															0
														);
														setTimeout(() => {
															editor.resize(true);
															editor.session.foldAll();
														}, 100);
													}}
												/>
											</Form.Item>
											<div
												style={{
													backgroundColor: "#F8F3F0",
													paddingLeft: "63px",
													width: "100%",
												}}
											>
												<Button
													style={{
														borderRadius: "0px",
														width: "80px",
													}}
													type="primary"
													loading={
														running[lang] ?? false
													}
													onClick={() => {
														handleRun(lang);
													}}
												>
													Run
												</Button>
											</div>
											<Flex
												wrap="wrap"
												gap="large"
												style={{
													width: "100%",
													backgroundColor: "#f1f1f1",
													paddingTop: "16px",
												}}
											>
												{responseObj[lang]?.errors ? (
													<Console
														errors={
															responseObj[lang]
																.errors
														}
													/>
												) : (
													(
														(
															responseObj[lang] ??
															[]
														)?.outputArray ?? []
													).map((ele, index) => (
														<span
															key={index}
															onClick={() => {
																if (
																	ele.testCase
																) {
																	return setActiveTestCase(
																		{
																			...ele.testCase,
																			name: `Test Case ${index + 1}`,
																		}
																	);
																}
																return setActiveTestCase(
																	null
																);
															}}
															style={{
																display: "flex",
																alignItems:
																	"center",
																gap: "8px",
																fontSize:
																	"16px",
																padding: "8px",
																border: "1px solid #ddd",
																borderRadius:
																	"4px",
																cursor: "pointer",
																backgroundColor:
																	"white",
															}}
														>
															Test Case{" "}
															{index + 1}
															{!ele.testCasePassed ? (
																<CloseOutlined
																	style={{
																		color: "red",
																	}}
																/>
															) : (
																<CheckOutlined
																	style={{
																		color: "green",
																	}}
																/>
															)}
														</span>
													))
												)}
											</Flex>
										</>
									),
								};
							});
							return (
								formInstance.getFieldValue("code")?.length >
									0 && (
									<Tabs
										items={tabItems}
										tabBarStyle={{
											marginBottom: 0,
											paddingLeft: 63,
										}}
									/>
								)
							);
						}}
					</Form.List>
				</Card>
				<Flex align="center" style={{ marginTop: "30px" }} gap="large">
					<Button loading={saving} htmlType="submit" type="primary">
						Update Solution
					</Button>
					{isNewQues && (
						<Button
							type="primary"
							onClick={() => {
								const instance = modal.confirm({
									title: "Want to save on course side as well?",
									okText: "Yes",
									cancelText: "No",
									onOk: () =>
										exportQuestionToCourse(instance),
									onCancel: () => {
										if (!isFromTestModal) {
											navigate("/questions");
										}
									},
								});
							}}
						>
							Save
						</Button>
					)}
				</Flex>
			</Form>
		</div>
	);
};
