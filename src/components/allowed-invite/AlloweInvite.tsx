import { Button, Flex, Form, Modal, Typography, Upload } from "antd";
import { useState } from "react";
import { PaperClipOutlined } from "@ant-design/icons";
import * as XLSX from "xlsx-js-style";
import type { RcFile, UploadProps } from "antd/es/upload";
import Tags from "../tags";
import { useTestStore } from "../../store";
import { AnyObject } from "antd/es/_util/type";
import { testEmailRegex } from "@/testList/common/common";
import { AppConfig } from "@/config";
import { useAppMessage } from "@/hooks/message";

const { Text, Link } = Typography;

type FieldType = {
	emails?: string[];
};
const AllowedInvite = ({
	onClose,
	isAllowedInviteOpen,
	quizId,
	setFailedEmails,
}: {
	onClose: () => void;
	isAllowedInviteOpen: boolean;
	quizId: string;
	setFailedEmails: (_value: AnyObject[]) => void;
}) => {
	const messageInstance = useAppMessage();
	const [loading, setLoading] = useState(false);
	const [fileName, setFileName] = useState<string>("");
	const [allowedEmails, setAllowedEmails] = useState<Set<string>>(new Set());

	const handleCancel = () => {
		setFileName("");
		setAllowedEmails(new Set());
		setFailedEmails([]);
		onClose();
	};

	const readExcel = (file: RcFile): void => {
		const reader = new FileReader();
		reader.onload = (e: ProgressEvent<FileReader>) => {
			if (e.target && e.target?.result) {
				const data = new Uint8Array(e.target?.result as ArrayBuffer);
				const workbook = XLSX.read(data, { type: "array" });
				const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
				const result: string[][] = XLSX.utils.sheet_to_json(
					firstSheet,
					{
						header: 1,
					}
				);

				if (result.length === 0) {
					messageInstance?.error("The provided file was empty.");
					return;
				}
				const emailIndex = result[0].findIndex(header =>
					["Email", "Emails", "email", "emails"].includes(header)
				);

				if (emailIndex === -1) {
					messageInstance?.error(
						"The provided file does not have any 'Email' ,'Emails', 'email', 'emails' as column name."
					);
					return;
				}

				const validEmails = new Set<string>();
				const invalidEmails: AnyObject[] = [];
				for (let i = 1; i < result.length; i++) {
					const rawEmail = result[i][emailIndex];
					const email = rawEmail ? String(rawEmail).trim() : "";
					if (email && testEmailRegex.test(email)) {
						validEmails.add(email.toLowerCase());
					} else if (email) {
						invalidEmails.push({ email: email });
					}
				}

				setAllowedEmails(validEmails);
				setFailedEmails(invalidEmails);
				messageInstance?.success("Emails successfully extracted.");
			}
		};
		reader.onerror = () => {
			messageInstance?.error("Failed to read the Excel file!");
		};
		reader.readAsArrayBuffer(file);
	};

	const handleFileChange = (file: RcFile) => {
		const isExcel =
			file.type === "application/vnd.ms-excel" ||
			file.type ===
				"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";

		if (!isExcel) {
			messageInstance?.error(
				"You can only upload Excel files (.xls or .xlsx)!"
			);
			return Upload.LIST_IGNORE;
		}
		setFileName(file.name);
		readExcel(file);
		return false;
	};

	function validateAllowedEmails(
		_rule: unknown,
		value: string[]
	): Promise<void> {
		if (!value || value.length === 0) {
			return Promise.resolve();
		}
		for (const email of value) {
			if (!testEmailRegex.test(email)) {
				return Promise.reject(
					new Error(`"${email}" is not a valid e-mail address!`)
				);
			}
		}

		return Promise.resolve();
	}

	const testStore = useTestStore();

	const [form] = Form.useForm();

	const handleUpload = async (values: { emails: string[] }) => {
		try {
			const inputEmails = values.emails || [];
			if (!inputEmails.length && !fileName) {
				messageInstance?.error(
					"Please upload an Excel file or add an email."
				);
				return;
			}
			let emailsToUpload = new Set(allowedEmails);
			if (inputEmails.length) {
				emailsToUpload = new Set([...allowedEmails, ...inputEmails]);
			}
			setLoading(true);

			await testStore.allowCandidates(quizId, [...emailsToUpload]);
			messageInstance?.success("Emails Added successfully.");
		} catch (error) {
			messageInstance?.error(String(error));
			console.error("Validation failed:", error);
		} finally {
			setLoading(false);
		}
		onClose();
	};

	const props: UploadProps = {
		showUploadList: {
			showRemoveIcon: true,
		},
		onRemove: () => {
			setAllowedEmails(new Set());
			setFileName("");
			setFailedEmails([]);
		},
		beforeUpload: (file: RcFile) => handleFileChange(file),
		maxCount: 1,
		accept: ".xls, .xlsx",
	};
	return (
		<>
			<Modal
				open={isAllowedInviteOpen}
				title="Allowed Candidates"
				onCancel={handleCancel}
				footer={[
					<Button
						key="submit"
						type="primary"
						loading={loading}
						onClick={form.submit}
					>
						Upload
					</Button>,
				]}
				destroyOnClose={true}
			>
				<Flex vertical gap="middle">
					<Upload {...props}>
						<Button icon={<PaperClipOutlined />} iconPosition="end">
							Choose File
						</Button>
					</Upload>
					<Text type="secondary">
						Choose file to import candidates. Supported extensions
						are (.xls, .xlsx) or download{" "}
						<Link
							href={`${AppConfig.quizServerURL}/template_files/cq/bulk-allowed-candidates.xlsx`}
							target="_blank"
						>
							'add candidates template'.
						</Link>
					</Text>

					<Form name="basic" form={form} onFinish={handleUpload}>
						<Form.Item<FieldType>
							label="Emails"
							name="emails"
							rules={[{ validator: validateAllowedEmails }]}
							normalize={value => {
								if (typeof value === "string") {
									return value.toLowerCase();
								}
								if (Array.isArray(value)) {
									const unique = new Set<string>();
									for (const item of value)
										unique.add(item.toLowerCase());
									return [...unique];
								}
								return value;
							}}
						>
							<Tags separator={/[ ,;]+/} max={500} />
						</Form.Item>
					</Form>
				</Flex>
			</Modal>
		</>
	);
};

export default AllowedInvite;
