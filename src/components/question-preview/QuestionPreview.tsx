import { QuestionTypesMap } from "@/client/test-add";
import { Button, Modal, Space, Tag } from "antd";
import { Typography } from "antd";
const { Title } = Typography;
// import "./questionPreview.css";`
import "react-quill/dist/quill.snow.css";

export type QuestionPreviewProps = {
	title: string;
	count: number;
	description: string;
	type: string;
	id: string;
	onClose?: () => void;
};
function QuestionPreview({
	title,
	count,
	description,
	type,
	id,
	onClose,
}: QuestionPreviewProps) {
	console.log(id, onClose);
	return (
		<Modal
			open={title !== ""}
			onCancel={() => onClose?.()}
			title="Question Preview"
			footer={[
				<Button
					key="preview"
					type="primary"
					onClick={() =>
						window.open(`/questions/preview/${id}`, "blank")
					}
				>
					Preview
				</Button>,
			]}
			width={"70%"}
			styles={{ body: { maxHeight: "60vh", overflow: "auto" } }}
			centered
		>
			<Space
				direction="vertical"
				style={{ width: "100%", height: "100%" }}
			>
				<Tag>
					{
						QuestionTypesMap[
							type as unknown as keyof typeof QuestionTypesMap
						]
					}
				</Tag>
				<Title level={5}>{title}</Title>
				{count ? (
					<Space align="baseline">
						<Title level={5}>Testcase:</Title>
						<span> {count}</span>
					</Space>
				) : null}
				<div>
					<Title level={5}>Description:</Title>
					<div
						className="ql-editor"
						dangerouslySetInnerHTML={{ __html: description }}
					/>
				</div>
			</Space>
		</Modal>
	);
}

export default QuestionPreview;
