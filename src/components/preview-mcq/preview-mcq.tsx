import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>lex, Form, Radio, Tooltip, Typography } from "antd";
import React, { useCallback, useState } from "react";

import "./attempt-mcq.scoped.css";
import { QuestionTypeMcq } from "@/testReport/data/data";
import { CheckOutlined, CloseOutlined, UndoOutlined } from "@ant-design/icons";

interface AttemptMCQProps {
	questionData?: QuestionTypeMcq;
}

export const AttemptMCQ: React.FunctionComponent<AttemptMCQProps> = props => {
	const { questionData } = props;

	const [form] = Form.useForm();

	const [answerError, setAnswerError] = useState<string | undefined>();
	const [submittedIndex, setSubmittedIndex] = useState<number | null>(null);

	const handleMCQFormSubmit = useCallback(() => {
		const selectedOption = form.getFieldValue("answer");

		if (!selectedOption || !questionData?.options) return;

		const selectedIndex = questionData.options.findIndex(
			option => option === selectedOption
		);

		setSubmittedIndex(selectedIndex);
	}, [form, questionData]);

	return (
		<div className="attempt-mcq-container">
			<div style={{ padding: "1.5rem" }}>
				<span className="info-text">Choose any one</span>
				<Divider
					style={{
						margin: "0.5rem 0 1.5rem 0",
						width: "50%",
						minWidth: "unset",
					}}
				/>
				<div>
					<Form
						form={form}
						onFinish={handleMCQFormSubmit}
						onFieldsChange={() => setAnswerError(undefined)}
					>
						<Form.Item
							name="answer"
							rules={[
								{
									required: true,
									message: "Please select an option!",
								},
							]}
						>
							<Radio.Group
								style={{
									display: "flex",
									flexDirection: "column",
									gap: 16,
								}}
								options={questionData?.options?.map(
									(option, index) => {
										const isSubmitted =
											submittedIndex !== null;
										const isSelected =
											submittedIndex === index;
										const isCorrect =
											questionData.correctAnswers?.includes(
												index
											);

										let resultText;
										if (isSubmitted && isSelected) {
											resultText = isCorrect ? (
												<Typography.Text type="success">
													<CheckOutlined
														style={{
															marginRight: 8,
														}}
													/>
													Correct
												</Typography.Text>
											) : (
												<Typography.Text type="danger">
													<CloseOutlined
														style={{
															marginRight: 8,
														}}
													/>
													Incorrect
												</Typography.Text>
											);
										}

										return {
											label: (
												<Flex gap={32}>
													<pre>{option}</pre>
													{resultText && (
														<span className="result-text">
															{resultText}
														</span>
													)}
												</Flex>
											),
											value: option,
											className: "mcqOptions",
										};
									}
								)}
							></Radio.Group>
						</Form.Item>
					</Form>
				</div>
			</div>
			<div className="submit-mcq-container">
				<span className="error-message">{answerError}</span>
				<Flex justify="flex-end" gap={16}>
					<Tooltip title="Reset" placement="left">
						<Button
							type="text"
							shape="circle"
							onClick={() => {
								form.setFieldsValue({
									answer: null,
								});
								setSubmittedIndex(null);
							}}
							icon={<UndoOutlined />}
						/>
					</Tooltip>
					<Button type="primary" onClick={() => form.submit()}>
						Submit
					</Button>
				</Flex>
			</div>
		</div>
	);
};
