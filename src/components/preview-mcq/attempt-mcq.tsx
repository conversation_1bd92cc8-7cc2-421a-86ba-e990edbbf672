import { <PERSON><PERSON>, Divider, Form, Radio, Space } from "antd";
import React, { useCallback, useState } from "react";

import "./attempt-mcq.scoped.css";

interface Option {
	label: string;
	value: string | number;
}

interface QuestionData {
	options: Option[];
	// add other properties if needed
}

interface AttemptMCQProps {
	questionData: QuestionData;
}

export const AttemptMCQ: React.FunctionComponent<AttemptMCQProps> = props => {
	const { questionData } = props;

	const [form] = Form.useForm();

	const [answerError, setAnswerError] = useState<string | undefined>();

	const handleMCQFormSubmit = useCallback(async () => {
		// await onSubmit({
		// 	inputMCQ: `chkOpt${values.answer}`,
		// 	chk: `chkOpt${values.answer}`,
		// });
	}, []);

	const handleMCQFormSubmitFailed = useCallback(
		() => {},
		[]
		// async ({ values, errorFields, outOfDate }) => {
		// 	if (errorFields[0].errors[0]) {
		// 		setAnswerError(errorFields[0].errors[0]);
		// 	}
		// },
		// []
	);

	return (
		<div className="attempt-mcq-container">
			<div>
				<span className="info-text">Choose any one</span>
				<Divider
					style={{
						margin: "0.5rem 0 1.5rem 0",
						width: "50%",
						minWidth: "unset",
					}}
				/>
				<div>
					<Form
						form={form}
						onFinish={handleMCQFormSubmit}
						onFinishFailed={handleMCQFormSubmitFailed}
						onFieldsChange={() => setAnswerError(undefined)}
					>
						<Form.Item
							name="answer"
							rules={[
								{
									required: true,
									message: "Please select an option!",
								},
							]}
						>
							<Radio.Group>
								<Space direction="vertical">
									{questionData?.options?.map(option => (
										<Radio
											key={option.label}
											value={option.value}
										>
											{option.label}
										</Radio>
									))}
								</Space>
							</Radio.Group>
						</Form.Item>
						<Form.Item>
							<Button
								type="text"
								size="small"
								onClick={() =>
									form.setFieldsValue({
										answer: null,
									})
								}
							>
								Clear selection
							</Button>
						</Form.Item>
					</Form>
				</div>
			</div>
			<div className="submit-mcq-container">
				<span className="error-message">{answerError}</span>
				<Button
					type="primary"
					size="large"
					onClick={() => form.submit()}
				>
					submit
				</Button>
			</div>
		</div>
	);
};
