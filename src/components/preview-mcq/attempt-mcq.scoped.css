div.attempt-mcq-container {
	height: 100%;
	flex: auto;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	position: relative;
}

div.attempt-mcq-container > div.submit-mcq-container {
	display: flex;
	flex-direction: column;
	margin-top: auto;
	border-top: 1px solid #ccc;
	width: 100%;
	padding: 0.5rem;
	background-color: #fff;
}

div.submit-mcq-container > button {
	border-radius: 5px;
	font-family: "Hind", sans-serif;
	padding: 0.5rem 2rem;
}

div.submit-mcq-container > span.error-message {
	color: #ff4d4f;
	margin-right: 1rem;
}

span.info-text {
	font-size: 20px;
	font-family: "Hind", sans-serif;
	font-weight: 500;
}

div.attempt-mcq-container /deep/ form > div:not(:last-child) * {
	font-family: "Hind", sans-serif;
	font-size: 15px;
	white-space: pre-wrap;
}

/* div.attempt-mcq-container /deep/ form > div:first-child {
	margin-left: 1.5rem;
} */

div.attempt-mcq-container /deep/ form > div:first-child span.ant-radio-inner {
	border: none;
	background-color: var(--primary-light);
}

/* div.attempt-mcq-container /deep/ form > div:first-child span.ant-radio-inner::after {
	top: 3.5px;
	left: 3.5px;
	height: 9px;
	width: 9px;
}  */

div.attempt-mcq-container /deep/ form > div:last-child button {
	color: var(--primary-color);
	font-weight: 500;
	font-size: 15px;
	padding: 0;
	font-family: "Hind", sans-serif;
}

div.attempt-mcq-container
	/deep/
	form
	> div:last-child
	button:focus:not(:focus-visible) {
	background-color: transparent;
}

div.attempt-mcq-container /deep/ form div.ant-row.ant-form-item {
	margin-bottom: 0;
}

div.attempt-mcq-container /deep/ form div.ant-form-item-explain {
	display: none;
}

.mcqOptions .ant-radio {
	align-self: flex-start !important;
	line-height: 50px;
	margin-top: 0.25em;
}
