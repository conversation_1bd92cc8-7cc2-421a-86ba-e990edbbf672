import { QuestionSupportedLanguage } from "@/constants/languages";
import { CodeComponents, QuestionTypeCoding } from "@/testReport/data/data";
import {
	Button,
	Checkbox,
	CheckboxChangeEvent,
	Flex,
	Input,
	message,
	Modal,
	Select,
	Spin,
	Tabs,
	Tooltip,
} from "antd";
import { CQEditor } from "../editor";
import { useCallback, useMemo, useState } from "react";
import {
	CaretDownOutlined,
	CaretUpOutlined,
	CheckOutlined,
	CloseOutlined,
	CopyOutlined,
	FileExcelOutlined,
	LoadingOutlined,
	MinusCircleFilled,
	WarningFilled,
} from "@ant-design/icons";
import "./attempt-coding.scoped.css";
import { useSocketMutation, useSocketWatch } from "@/hooks/socket";
import { OutputArray } from "../question-solution";
import Title from "antd/es/typography/Title";

enum ConsoleTabKeys {
	TestCases = "test-cases",
	ConsoleScreen = "console-screen",
	CustomInput = "custom-input",
	SampleTestCases = "sample-test-cases",
}

// enum TestCaseStatus {
// 	NotExecuted = 0,
// 	Running = 1,
// 	Passed = 2,
// 	Failed = 3,
// 	Unknown = 4,
// }

interface CodingTestCaseMeta {
	_id: string;
	sampleTest: boolean;
	attemptInMultiLine: boolean;
	codeprogexpectedoutput: string;
	codeproginputparams: string;
	scoreip: number;
	onCloud?: boolean;
}

export default function PreviewCoding({
	questionDetails,
	quesId,
}: {
	questionDetails?: QuestionTypeCoding;
	quesId: string;
}) {
	const [lang, setLang] = useState<number>(
		Number(questionDetails?.codeproglang[0].language)
	);
	const [consoleActiveTab, setConsoleActiveTab] = useState<string>(
		ConsoleTabKeys.ConsoleScreen
	);
	const [consoleVisible, setConsoleVisible] = useState<boolean>(false);
	const [customInputAllowed, setCustomInputAllowed] =
		useState<boolean>(false);
	const [compiling, setCompiling] = useState<boolean>(false);
	const [selectedTestCase, setSelectedTestCase] = useState<
		CodingTestCaseMeta | undefined
	>();
	const codeComponent = useMemo<CodeComponents>(() => {
		return (
			questionDetails?.codeproglang.find(
				language => language.language === lang.toString()
			)?.codeComponents ?? { body: "", head: "", tail: "", solution: "" }
		);
	}, [lang, questionDetails]);
	const [compiledTestCases, setCompiledTestCases] = useState<
		Array<OutputArray>
	>([]);

	const [codeByLanguage, setCodeByLanguage] = useState<
		Record<number, string>
	>(() => {
		const initialCodes: Record<number, string> = {};
		questionDetails?.codeproglang.forEach(langConfig => {
			const langId = Number(langConfig.language);
			initialCodes[langId] = langConfig.codeComponents.body;
		});
		return initialCodes;
	});

	const code = codeByLanguage[lang] || codeComponent.body;

	const setCode = useCallback(
		(newCode: string) => {
			setCodeByLanguage(prev => ({
				...prev,
				[lang]: newCode,
			}));
		},
		[lang]
	);

	const [error, setError] = useState<string>("");
	const [customInputValue, setCustomInputValue] = useState<string>();
	const [isSolutionModalOpen, setIsSolutionModalOpen] =
		useState<boolean>(false);

	const [messageInstance, contextHolder] = message.useMessage({
		maxCount: 1,
	});

	const handleConsoleTabChanged = useCallback(
		(activeKey: string) => {
			// if (
			// 	(consoleActiveTab === ConsoleTabKeys.TestCases &&
			// 		activeKey === ConsoleTabKeys.SampleTestCases) ||
			// 	(consoleActiveTab === ConsoleTabKeys.SampleTestCases &&
			// 		activeKey === ConsoleTabKeys.TestCases) ||
			// 	1 // reseting all execution on tab switch
			// ) {
			// 	questionData.questionTypeCoding?.testCases.forEach(
			// 		async (testCase, index) => {
			// 			setTestCaseStatus(index, TestCaseStatus.NotExecuted);
			// 		}
			// 	);
			// }

			setConsoleActiveTab(activeKey);
			const testCase = questionDetails?.testCase[0];
			if (testCase) {
				setSelectedTestCase(testCase);
			}

			// if (activeKey === ConsoleTabKeys.TestCases && !selectedTestCase) {
			// 	const testCase = questionData.questionTypeCoding?.testCases[0];
			// 	if (testCase) {
			// 		setSelectedTestCase(testCase);
			// 	}
			// } else if (
			// 	activeKey === ConsoleTabKeys.SampleTestCases &&
			// 	!selectedSampleTestCase
			// ) {
			// 	const testCase =
			// 		questionData.questionTypeCoding?.testCases.filter(
			// 			el => el.sampleTest
			// 		)[0];
			// 	if (testCase) {
			// 		setSelectedSampleTestCase(testCase);
			// 	}
			// }
		},
		[questionDetails?.testCase, selectedTestCase, consoleActiveTab]
	);

	const handleCustomInputAllowed = useCallback((e: CheckboxChangeEvent) => {
		if (e.target.checked) {
			setConsoleVisible(true);
			setConsoleActiveTab(ConsoleTabKeys.CustomInput);
		} else {
			setConsoleActiveTab(ConsoleTabKeys.TestCases);
		}
		setCustomInputAllowed(e.target.checked);
	}, []);

	const toggleConsoleVisible = useCallback(() => {
		setConsoleVisible(!consoleVisible);
	}, [consoleVisible, setConsoleVisible]);

	useSocketWatch("compile", {
		listnerFunction: data => {
			if (!data) {
				return;
			}
			setConsoleVisible(true);
			const outputArray: Array<OutputArray> = [];
			if (data.errors) {
				setError(data.errors);
				setConsoleActiveTab(ConsoleTabKeys.ConsoleScreen);
				setCompiling(false);
				return;
			}
			if (!customInputAllowed) {
				(data.outputArray ?? []).forEach((ele, index) => {
					outputArray.push({
						...ele,
						testCase: data?.testCase[index] ?? null,
					});
				});
				setCompiledTestCases(outputArray);
				if (questionDetails?.testCase.length === 0) {
					setError("compiled successfull");
					setConsoleActiveTab(ConsoleTabKeys.ConsoleScreen);
				} else {
					const testCase = questionDetails?.testCase[0];
					if (testCase) {
						setSelectedTestCase(testCase);
					}
					setConsoleActiveTab(ConsoleTabKeys.TestCases);
				}
			} else {
				setError(data.output);
				setConsoleActiveTab(ConsoleTabKeys.ConsoleScreen);
			}
			setCompiling(false);
			return;
		},
		key: [quesId],
	});

	const { mutateAsync } = useSocketMutation<
		[
			string,
			{
				code: string;
				language: string;
				isCustomInput: boolean;
				customInputValue: string;
			},
		],
		void
	>(
		async (
			socket,
			questionId: string,
			code: {
				language: string;
				code: string;
				isCustomInput: boolean;
				customInputValue: string;
			}
		) => {
			socket.emit("compile", {
				code: code.code,
				language: code.language,
				questionId: questionId,
				stdin: code.customInputValue,
				isInvalidAttempt: true,
				isCustomInput: code.isCustomInput,
			});
			return;
		}
	);

	const handleSubmit = useCallback(async () => {
		try {
			setCompiling(true);
			await mutateAsync(quesId, {
				code,
				language: lang.toString(),
				isCustomInput: customInputAllowed,
				customInputValue: customInputValue ?? "",
			});
			console.log("Hell0", customInputAllowed);
			if (customInputAllowed) {
				setConsoleActiveTab(ConsoleTabKeys.CustomInput);
			} else if (consoleVisible) {
				setConsoleActiveTab(ConsoleTabKeys.TestCases);
				const testCase = questionDetails?.testCase[0];
				if (testCase) {
					setSelectedTestCase(testCase);
				}
			}
		} catch (error) {
			console.log(error);
			setCompiling(false);
		}
	}, [
		code,
		lang,
		mutateAsync,
		quesId,
		customInputAllowed,
		questionDetails?.testCase,
		setSelectedTestCase,
		customInputValue,
	]);

	const calculateHeight = useCallback((text: string) => {
		const lines = text.split("\n").length;
		return `${Math.min(Math.max(lines * 26, 50), 100)}px`;
	}, []);

	return (
		<>
			<Flex style={{ width: "100%", height: "100%" }} vertical gap={8}>
				{contextHolder}
				<Flex justify="space-between" style={{ paddingTop: "0.5em" }}>
					<Select
						style={{ width: 120 }}
						defaultValue={questionDetails?.codeproglang[0].language}
						options={questionDetails?.codeproglang.map(
							language => ({
								value: language.language,
								label: getLanguageName(
									Number(language.language)
								),
							})
						)}
						onSelect={value => setLang(Number(value))}
					/>
					<Button
						type="primary"
						onClick={() => setIsSolutionModalOpen(true)}
					>
						Solution
					</Button>
				</Flex>
				<Flex style={{ width: "100%", height: "90%" }} vertical>
					{codeComponent.head ? (
						<CQEditor
							lang={lang ?? 7}
							size={{
								width: "100%",
								height: calculateHeight(codeComponent.head),
							}}
							readOnly={true}
							value={codeComponent.head}
							className="head"
						/>
					) : (
						<></>
					)}
					<CQEditor
						lang={lang ?? 7}
						size={{
							width: "100%",
							height: "100%",
						}}
						enableSyntaxSupport={true}
						className="body"
						value={code}
						onChange={val => setCode(val)}
					/>
					{codeComponent.tail ? (
						<CQEditor
							lang={lang ?? 7}
							size={{
								width: "100%",
								height: calculateHeight(codeComponent.tail),
							}}
							readOnly={true}
							value={codeComponent.tail}
							className="tail"
						/>
					) : (
						<></>
					)}
					{consoleVisible ? (
						<div className="console-container">
							<Tabs
								type="card"
								size="small"
								activeKey={consoleActiveTab}
								onChange={handleConsoleTabChanged}
								tabBarExtraContent={{
									right: (
										<Button
											type="text"
											shape="circle"
											icon={
												<MinusCircleFilled
													style={{
														color: "var(--primary-color)",
														opacity: 0.75,
													}}
												/>
											}
											onClick={toggleConsoleVisible}
										/>
									),
								}}
								items={[
									{
										label: "Test Cases",
										key: `${ConsoleTabKeys.TestCases}`,
										children: (
											<Flex style={{ height: "100%" }}>
												<Flex
													vertical
													style={{
														width: 150,
														overflow: "auto",
														height: "100%",
													}}
												>
													{questionDetails?.testCase.map(
														(testCase, index) => {
															return (
																<Button
																	className={
																		selectedTestCase?._id ===
																		testCase._id
																			? "active"
																			: ""
																	}
																	key={
																		testCase._id
																	}
																	style={{
																		padding:
																			"0.5em",
																	}}
																	type="text"
																	size="large"
																	icon={(() => {
																		if (
																			compiling
																		) {
																			return (
																				<Spin
																					indicator={
																						<LoadingOutlined
																							spin
																						/>
																					}
																				/>
																			);
																		}
																		if (
																			compiledTestCases.length ===
																			0
																		) {
																			return (
																				<WarningFilled
																					style={{
																						color: "#fed639",
																						fontSize: 18,
																					}}
																				/>
																			);
																		}
																		if (
																			compiledTestCases[
																				index
																			]
																				.testCasePassed
																		) {
																			return (
																				<CheckOutlined
																					style={{
																						color: "green",
																						fontSize: 18,
																					}}
																				/>
																			);
																		}
																		if (
																			compiledTestCases[
																				index
																			]
																				.testCasePassed ===
																			false
																		) {
																			return (
																				<CloseOutlined
																					style={{
																						color: "red",
																						fontSize: 18,
																					}}
																				/>
																			);
																		}
																	})()}
																	onClick={() => {
																		setSelectedTestCase(
																			testCase
																		);
																	}}
																>
																	Test Case{" "}
																	{index + 1}
																</Button>
															);
														}
													)}
												</Flex>
												<Flex
													style={{ flex: 1 }}
													className="test-case-details-container"
												>
													<div>
														{selectedTestCase && (
															<>
																<div
																	style={{
																		display:
																			"flex",
																		alignItems:
																			"center",
																		justifyContent:
																			"space-between",
																	}}
																>
																	<span>
																		Input
																		Parameters:
																	</span>
																	{selectedTestCase.onCloud && (
																		<Tooltip
																			title="Show full testcase"
																			showArrow={
																				false
																			}
																		>
																			<Button
																				icon={
																					<FileExcelOutlined />
																				}
																				size="middle"
																				// loading={
																				// 	showTestCaseModel
																				// }
																				onClick={() => {
																					// handleShowCloudTestCase(
																					// 	selectedTestCase._id
																					// );
																				}}
																			/>
																		</Tooltip>
																	)}
																</div>
																<pre className="test-case-preTag">
																	{
																		selectedTestCase.codeproginputparams
																	}
																</pre>
																<span>
																	Expected
																	Output:
																</span>
																<pre className="test-case-preTag">
																	{
																		selectedTestCase.codeprogexpectedoutput
																	}
																</pre>
																<span>
																	Your Output:
																</span>
																<pre className="test-case-preTag">
																	{compiledTestCases.length !=
																		0 &&
																		compiledTestCases.find(
																			testCase =>
																				testCase
																					.testCase
																					?._id ===
																				selectedTestCase._id
																		)
																			?.userOutput}
																</pre>
															</>
														)}
													</div>
												</Flex>
											</Flex>
										),
									},
									{
										label: "Console",
										key: `${ConsoleTabKeys.ConsoleScreen}`,
										children: (
											<div className="console-screen">
												<pre>{error}</pre>
											</div>
										),
									},
									{
										label: "Custom Input",
										key: `${ConsoleTabKeys.CustomInput}`,
										children: (
											<div className="custom-input-container">
												<Input.TextArea
													value={customInputValue}
													onChange={e =>
														setCustomInputValue(
															e.target.value
														)
													}
													autoSize={{
														minRows: 9,
														maxRows: 9,
													}}
												/>
											</div>
										),
									},
								]}
							></Tabs>
						</div>
					) : (
						<></>
					)}
				</Flex>
				<Flex style={{ padding: "0.5rem" }}>
					<Flex gap={8}>
						<Button
							onClick={() => setConsoleVisible(prev => !prev)}
							icon={
								consoleVisible ? (
									<CaretDownOutlined />
								) : (
									<CaretUpOutlined />
								)
							}
							iconPosition="end"
						>
							Console
						</Button>
						<Checkbox
							checked={customInputAllowed}
							onChange={handleCustomInputAllowed}
							disabled={compiling}
							style={{ alignItems: "center" }}
						>
							custom input
						</Checkbox>
					</Flex>
					<div style={{ marginLeft: "auto" }}>
						<Button
							type="primary"
							onClick={handleSubmit}
							loading={compiling}
						>
							Submit
						</Button>
					</div>
				</Flex>
			</Flex>
			<Modal
				title={
					<Flex gap={8} align="center">
						<Title level={4} style={{ marginBottom: 0 }}>
							Solution
						</Title>
						<CopyOutlined
							onClick={async () => {
								if (codeComponent.solution) {
									await navigator.clipboard.writeText(
										codeComponent.solution
									);
									messageInstance.success(
										"Solution copied successfully"
									);
								}
							}}
						/>
					</Flex>
				}
				open={isSolutionModalOpen}
				onCancel={() => setIsSolutionModalOpen(false)}
				width="50%"
				footer={
					<Button
						type="primary"
						onClick={() => setIsSolutionModalOpen(false)}
					>
						Cancel
					</Button>
				}
			>
				<CQEditor
					lang={lang ?? 7}
					size={{
						width: "100%",
						height: "500px",
					}}
					readOnly={true}
					value={codeComponent.solution}
					className="solution"
				/>
			</Modal>
		</>
	);
}

function getLanguageName(langId: number): string | null {
	const match = Object.entries(QuestionSupportedLanguage).find(
		([, id]) => id === langId
	);
	return match ? match[0] : null;
}
