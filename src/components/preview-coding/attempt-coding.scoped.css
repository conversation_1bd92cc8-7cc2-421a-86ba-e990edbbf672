div.attempt-coding-container {
	flex: auto;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	max-width: 100%;
}

div.attempt-coding-container > div.footer {
	flex: 0 0 50px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	border-top: 1px solid #d0d0d0;
	padding: 0 1rem;
	background-color: #fff;
	z-index: 1;
}

div.footer > div:first-child > button {
	padding: 4px 10px;
}

div.footer > div:first-child > button span.anticon {
	position: relative;
	top: 1px;
}

div.footer button {
	border-radius: 4px;
	font-family: "Hind", "sans-serif";
	font-weight: 500;
}

div.footer > div:nth-child(2) > button {
	margin: 0 0.5rem;
}

div.attempt-coding-container > div.main {
	flex: auto;
	display: flex;
	flex-direction: column;
	/* overflow: auto; */
}

div.main > div.code-editor-wrapper:fullscreen {
	background-color: #fff;
}

div.main > div.code-editor-wrapper,
div.code-editor-wrapper > div.code-editor-container {
	flex: auto;
	display: flex;
	flex-direction: column;
	overflow: auto;
	z-index: 0;
}

div.code-editor-wrapper > div.editor-header {
	flex: 0 0 40px;
	display: flex;
	background-color: #fafafa;
	align-items: center;
	justify-content: space-between;
	padding: 0 1rem;
	height: 40px;
}

div.code-editor-container > div.editor-head,
div.code-editor-container > div.editor-tail {
	/* flex: auto; */
	/* display: flex; */
	/* overflow: auto; */
	position: relative;
	cursor: not-allowed;
}

div.code-editor-container /deep/ div.editor-head div.ace_content,
div.code-editor-container /deep/ div.editor-tail div.ace_content {
	background-color: #fafafa;
	opacity: 0.7;
	/* cursor: not-allowed; */
}

div.code-editor-container /deep/ div.editor-head div.ace_gutter,
div.code-editor-container /deep/ div.editor-tail div.ace_gutter {
	min-width: 42px;
}

div.code-editor-container /deep/ div.editor-head div.ace_scroller,
div.code-editor-container /deep/ div.editor-tail div.ace_scroller {
	left: 42px !important;
}

div.code-editor-container
	/deep/
	div.editor-main
	div.ace_scroller.ace_scroll-left {
	box-shadow: 17px 0 16px -16px rgb(0 0 0 / 12%) inset !important;
}

div.code-editor-container > div.editor-main {
	flex: auto;
	display: flex;
	/* overflow: auto; */
}

div.code-editor-container /deep/ div.editor-main > div#main {
	flex: auto !important;
}

div.console-container {
	display: flex;
	flex-direction: column;
	/* flex: 0 0 35%; */
	overflow: hidden;
	max-height: 350px;
}

div.main /deep/ div.console-container div.ant-tabs,
div.main /deep/ div.console-container div.ant-tabs-content {
	flex: auto;
	height: 100%;
}

div.main
	/deep/
	div.console-container
	div.ant-tabs-content
	div.ant-tabs-tabpane-hidden {
	display: none;
}

div.main
	/deep/
	div.console-container
	div.ant-tabs-content
	div.ant-tabs-tabpane-hidden {
	display: none;
}

div.main /deep/ div.console-container div.ant-tabs-content-holder {
	display: flex;
}

div.main /deep/ div.console-container div.ant-tabs-nav {
	margin: 0;
	padding: 0.25rem 0.75rem 0 0.75rem;
	background-color: var(--primary-light);
}

div.main /deep/ div.console-container div.ant-tabs-tab {
	background-color: var(--primary-light);
	font-family: "Hind", sans-serif;
	border: none;
}

div.main /deep/ div.console-container div.ant-tabs-tab > div {
	color: #818181;
	font-weight: 500;
}

div.main /deep/ div.console-container div.ant-tabs-tab-active {
	background-color: #fff;
	border-radius: 10px 10px 0 0;
}

div.main /deep/ div.console-container div.ant-tabs-tab-active > div {
	color: #33333399;
	font-weight: 500;
}

div.main /deep/ div.console-container div.ant-tabs-tabpane {
	flex: auto;
	display: flex;
	flex-direction: column;
	height: 100%;
}

div.custom-input-container {
	display: flex;
	padding: 0.25rem 0.75rem;
	height: 100%;
	overflow: hidden;
	min-height: 350px;
}

div.custom-input-container textarea {
	resize: none;
}

div.console-screen {
	display: flex;
	padding: 0.25rem 0.75rem;
	background-color: #000;
	color: #fff;
	height: 100%;
	overflow: hidden;
	min-height: 350px;
}

div.console-screen > pre {
	flex: auto;
	overflow: auto;
	margin: 0;
}

div.test-cases-message-container {
	flex: auto;
	display: flex;
	padding: 1rem;
	overflow: auto;
}

div.test-cases-wrapper {
	padding: 1rem;
	display: flex;
}

div.test-cases-wrapper div.test-cases-container {
	flex: 0 0 150px;
	display: flex;
	flex-direction: column;
	overflow: auto;
}

/* div.test-cases-wrapper div.test-cases-container.hidden-tests {
	flex: 0 0 200px;
} */

div.test-cases-wrapper > div.test-cases-container > button {
	/* font-family: "Hind", "sans-serif"; */
	font-weight: 500;
	font-size: 15px;
	color: var(--primary-color);
}

div.test-cases-wrapper > div.test-cases-container > button.active {
	background-color: #f7f7f7;
}

.test-case-details-container {
	flex: auto;
	display: flex;
	flex-direction: column;
	padding: 0 1rem;
	overflow: auto;
}

div.test-case-details-container > div > span {
	font-family: "Hind", "sans-serif";
	font-size: 14px;
	font-weight: 500;
	color: #818181;
}

div.test-case-details-container > div > pre {
	display: flex;
	background-color: #f8f8f8;
	padding: 0.5rem;
	overflow: unset;
	overflow-x: auto;
}

div.hidden-test-cases-wrapper {
	padding: 1rem 2rem;
	flex: auto;
	display: flex;
	overflow-y: scroll;
	flex-direction: column;
}
div.hidden-test-cases-wrapper .hidden-test-case-btn {
	cursor: default;
}

div.hidden-test-cases-wrapper * {
	font-family: "Hind", "sans-serif";
}

div.hidden-test-cases-wrapper > div:first-child {
	margin-bottom: 1.25rem;
}

div.hidden-test-cases-wrapper > div:first-child > button {
	padding: 0.5rem 0.75rem;
	border-radius: 5px;
	background-color: #f7f7f7;
	color: var(--primary-color);
	font-weight: 500;
	font-size: 15px;
	margin-right: 0.75rem;
	height: unset;
}

div.hidden-test-cases-wrapper > div:first-child > span:nth-child(2) {
	padding: 0.5rem 0.75rem;
	font-weight: 500;
	font-size: 15px;
	margin-right: 1rem;
}

div.hidden-test-cases-wrapper > div:last-child {
	background-color: var(--primary-light);
	padding: 0.75rem;
	border-radius: 5px;
	font-size: 13px;
}

.consoleBtn:active,
.consoleBtn:focus,
.consoleBtn:hover {
	color: rgba(0, 0, 0, 0.85);
	border-color: #d9d9d9;
	background: transparent;
}

.consoleBtn.consoleOpened {
	color: #eb8b5b;
	border-color: #eb8b5b;
	background: transparent;
}

.console-container .ant-tabs-content {
	height: 100%;
}

.console-container .ant-tabs-tabpane {
	height: 100%;
}
