import { quizClient } from "@/store/index";
import { useCallback, useEffect, useState } from "react";
import { Link } from "react-router";
import { DataTable } from "@/components/dataTable/dataTable";
import { TableColumnsType } from "antd";
import timeDate from "@/utils/timeDate";
import { UserSession } from "@/testReport/data/data";
import PageHelmet from "../page-helmet/PageHelmet";

export type LearnerAttemptRoot = LearnerAttempt[];

export interface LearnerAttempt {
	_id: string;
	userId: string;
	quizId: Quiz;
	startTime: string;
	quizSubmittedQuestion: SubmittedQuestion[];
	endTime?: string;
	userSessions?: UserSession[];
}
export interface TableDataType {
	_id: string;
	userId: string;
	quizId: Quiz;
	startTime: string;
	quizSubmittedQuestion: SubmittedQuestion[];
	endTime?: string;
	timeTaken?: string | number;
}

export interface Quiz {
	_id: string;
	title: string;
	description: string;
	endTime: string;
	createdBy: string;
	completed?: boolean;
}

export interface SubmittedQuestion {
	questionId: string;
	startTime: string;
	_id: string;
	userOutputCoding: string[];
	hasSubmitted: boolean;
	submissions: string[];
	score: number;
	userInputMCQ?: string;
	userLanguage?: string;
	userProgram?: string;
}

const LearnerDashBoard = () => {
	const [loading, setLoading] = useState(false);
	const [tableState, setTableState] = useState<{
		tableData: TableDataType[] | undefined;
		currentPage: number;
		pageSize: number;
		totalRecords: number;
		sorting: unknown;
	}>({
		tableData: [],
		currentPage: 1,
		pageSize: 10,
		totalRecords: 0,
		sorting: {},
	});

	const columns: TableColumnsType<TableDataType> = [
		{
			title: "Tests",
			key: "tests",
			sorter: (a, b) => a.quizId.title.localeCompare(b.quizId.title),
			showSorterTooltip: false,
			render: data => (
				<Link to={`/report/${data.quizId._id}`}>
					{data.quizId.title}
				</Link>
			),
		},
		{
			title: "Scores",
			key: "scores",
			sorter: (a, b) => {
				const totalScoreA = (a.quizSubmittedQuestion ?? []).reduce(
					(sum, question) => sum + question.score,
					0
				);
				const totalScoreB = (b.quizSubmittedQuestion ?? []).reduce(
					(sum, question) => sum + question.score,
					0
				);
				return totalScoreA - totalScoreB;
			},
			showSorterTooltip: false,
			render: (_, record) => {
				const totalScore = (record.quizSubmittedQuestion ?? []).reduce(
					(sum, question) => sum + question.score,
					0
				);
				return totalScore;
			},
		},
		{
			title: "Time taken",
			key: "timeTaken",
			render: (_, record) => {
				return `${record.timeTaken}`;
			},
		},
		{
			title: "Submitted",
			key: "submitted",
			showSorterTooltip: false,
			sorter: (a, b) => {
				if (a.endTime && b.endTime) {
					return (
						new Date(a.endTime).getTime() -
						new Date(b.endTime).getTime()
					);
				}
				return 0;
			},
			render: (_, record) => {
				return record.endTime ? timeDate(record.endTime) : "-NA-";
			},
		},
	];

	const fetchAttemptData = useCallback(async () => {
		try {
			setLoading(true);
			const quizarray = await quizClient.getLearnerDashBoard();
			const tableData: TableDataType[] = [];

			quizarray.forEach(quiz => {
				if (!quiz.quizId.completed && quiz) {
					let timeTakenInMins = "-NA-";
					const userReportStartTime = quiz.startTime;
					const userReportEndTime = quiz.endTime;
					const userReportUserSessions = quiz.userSessions;

					const userReportData = {
						startTime: userReportStartTime,
						endTime: userReportEndTime,
						userSessions: userReportUserSessions,
					};
					if (
						userReportData.startTime &&
						userReportData.endTime &&
						(!userReportData.userSessions ||
							userReportData.userSessions.length == 0)
					) {
						const diffTime =
							+(
								new Date(userReportData.endTime).getTime() -
								new Date(userReportData.startTime).getTime()
							) || 0;
						const minutes = +(diffTime / 1000 / 60).toFixed(2) || 0;

						const remainingMin = Math.floor(minutes);
						const remainingSec = Math.round(
							(minutes - remainingMin) * 60
						);

						if (remainingSec)
							timeTakenInMins = `${remainingMin} min ${remainingSec} sec`;
						else timeTakenInMins = `${remainingMin} min`;
					} else {
						if (
							userReportData.userSessions &&
							!Array.isArray(userReportData.userSessions)
						) {
							userReportData.userSessions = JSON.parse(
								userReportData.userSessions
							);
						}

						if (
							(userReportData.endTime ||
								(userReportData.startTime &&
									+userReportData.startTime === 0) ||
								!userReportData.startTime) &&
							userReportData.userSessions &&
							Array.isArray(userReportData.userSessions) &&
							userReportData.userSessions.length
						) {
							let totalsMins = 0;
							userReportData.userSessions.forEach(val => {
								const TimeOne = +val.startTime;
								const Timetwo = +val.endTime;

								const dateOne = isNaN(TimeOne)
									? val.startTime
									: TimeOne;
								const dateTwo = isNaN(Timetwo)
									? val.endTime
									: Timetwo;
								const msDifference =
									new Date(dateTwo).getTime() -
										new Date(dateOne).getTime() || 0;
								const minutes =
									+(msDifference / 1000 / 60).toFixed(2) || 0;
								totalsMins += minutes;
							});

							const remainingMin = Math.floor(totalsMins);
							const remainingSec = Math.round(
								(totalsMins - remainingMin) * 60
							);
							if (remainingSec)
								timeTakenInMins = `${remainingMin} min ${remainingSec} sec`;
							else timeTakenInMins = `${remainingMin} min`;
						}
					}
					tableData.push({
						_id: quiz._id,
						userId: quiz.userId,
						quizId: quiz.quizId,
						quizSubmittedQuestion: quiz.quizSubmittedQuestion,
						startTime: userReportStartTime,
						endTime: userReportEndTime,
						timeTaken: timeTakenInMins,
					});
				}
			});

			setTableState(prev => {
				return {
					...prev,
					tableData: tableData,
					totalRecords: tableData.length,
				};
			});
		} catch (error) {
			console.error(error);
		} finally {
			setLoading(false);
		}
	}, []);

	useEffect(() => {
		fetchAttemptData();
	}, [fetchAttemptData]);

	return (
		<>
			<PageHelmet title="Dashboard" />

			<div style={{ padding: "16px 0" }}>
				<DataTable<TableDataType>
					columns={columns}
					data={tableState.tableData ?? []}
					loading={loading}
					totalRecords={tableState.totalRecords}
					pagination={{
						current: tableState.currentPage,
						pageSize: tableState.pageSize,
						onChange: (current, pageSize) =>
							setTableState(prev => {
								return {
									...prev,
									currentPage: current,
									pageSize: pageSize,
								};
							}),
						style: {
							position: "sticky",
							bottom: "0px",
							padding: "0.75em",
							backgroundColor: "white",
							margin: 0,
							zIndex: "1",
							width: "100%",
						},
						position: ["bottomCenter"],
						showSizeChanger: true,
					}}
					pageSize={tableState.pageSize}
					currentPage={1}
				/>
			</div>
		</>
	);
};

export default LearnerDashBoard;
