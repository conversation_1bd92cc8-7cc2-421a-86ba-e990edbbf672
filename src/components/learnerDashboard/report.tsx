import { useEffect, useMemo, useState } from "react";
import {
	Typography,
	Divider,
	Image,
	Flex,
	Descriptions,
	Layout,
	Spin,
	Tooltip,
} from "antd";
import Content, {
	QuizSegmentsDataType,
	TestSectionQuestion,
} from "../userReports/content";
import { useNavigate, useParams } from "react-router";
import { EndPointResponseBody, GetEndPointsMap } from "@/client/test-add";
import { quizClient, useAppStore } from "@/store/index";
import {
	QuizContent,
	QuizSegment,
	QuizUserDetail,
	TestDetailsReportDataType,
	UserAttempt,
} from "@/testReport/data/data";
import { useAppMessage } from "@/hooks/message";
import { DescriptionsItemType } from "antd/es/descriptions";

const { Text } = Typography;
const { Sider } = Layout;

type UserReportData = EndPointResponseBody<
	GetEndPointsMap["/useranswer/userReport/:quizId/:userId"]
>;

const descriptionCustomFieldData = (
	data: QuizUserDetail[]
): DescriptionsItemType[] => {
	return data.map(item => {
		return {
			label:
				item.fieldLabel.charAt(0).toUpperCase() +
				item.fieldLabel.slice(1),
			children: (
				<Tooltip title={item.fieldValue as React.ReactNode}>
					<Text
						ellipsis
						style={{
							maxWidth: 250,
							display: "block",
						}}
					>
						{item.fieldValue ?? "-NA-"}
					</Text>
				</Tooltip>
			),
		};
	});
};

const LearnerUserReport = () => {
	const { session } = useAppStore();
	const { id } = useParams();
	const messageInstance = useAppMessage();
	const navigate = useNavigate();
	const [userData, setUserData] = useState<UserReportData | null>(null);

	useEffect(() => {
		const userId = session?.userId;
		if (!id || !userId) return;
		const fetchData = async () => {
			try {
				const data = await quizClient.getUserReport(id, userId);
				setUserData(data);
			} catch (ex) {
				messageInstance?.error("Unable to fetch data");
				navigate(`/`);
				console.log(ex);
			}
		};
		fetchData();
	}, [messageInstance, navigate, id, session?.userId]);

	const { testDetails, contentTabData } = useMemo(() => {
		if (!userData || !session || !session.userId) return {};
		const userId = session.userId;
		const { quiz } = userData;

		const submittedSectionsSet = new Set(
			Array.from(
				typeof quiz.userQuizSubmittedSegment?.quizSubmittedSegments ===
					"string"
					? JSON.parse(
							quiz.userQuizSubmittedSegment.quizSubmittedSegments
						)
					: quiz.userQuizSubmittedSegment?.quizSubmittedSegments
			).map(Number)
		);

		const questionPriority: Record<string, number> = {};

		const testDetails: TestDetailsReportDataType = {
			userScore: 0,
			totalScore: 0,
			userAttemptQues: 0,
			totalQues: 0,
		};

		let segmentsArray: QuizSegment[] = [];

		if (quiz && quiz.quizSegments && quiz.quizSegments.length) {
			segmentsArray = quiz.quizSegments;
		}

		let quizSubmittedQuestion: string[] | undefined = [];
		if (
			quiz.poolQuestion == true ||
			quiz.randomizeQuestion == true ||
			(quiz &&
				quiz.userQuizSubmittedSegment &&
				quiz.userQuizSubmittedSegment.questionId)
		) {
			quizSubmittedQuestion = quiz.userQuizSubmittedSegment?.questionId;
		} else {
			quizSubmittedQuestion = quiz.questionId;
		}

		if (quiz.userQuizSubmittedSegment?.questionId) {
			try {
				const questionIds: Array<string> = [];
				if (
					typeof quiz.userQuizSubmittedSegment.questionId === "string"
				) {
					const questionIdsParsed = JSON.parse(
						quiz.userQuizSubmittedSegment.questionId
					);
					questionIds.push(...questionIdsParsed);
				} else if (
					Array.isArray(quiz.userQuizSubmittedSegment.questionId)
				) {
					questionIds.push(
						...quiz.userQuizSubmittedSegment.questionId
					);
				}
				if (
					(quiz.randomizeQuestion || quiz.poolQuestion) &&
					questionIds.length
				) {
					questionIds.map((id, index) => {
						questionPriority[id] = index;
					});
				}
			} catch (error) {
				console.trace("Failed to set question priority", error);
			}
		}

		let content_counter: number = 0;
		const lastIndex: number = segmentsArray.length - 1;
		const contentTabData: QuizSegmentsDataType[] = [];
		const sectionTitles: string[] = [];

		segmentsArray.forEach((segment: QuizSegment, index: number) => {
			sectionTitles.push(segment.title);
			let contentArray: QuizContent[] = [];
			let segementScore: number = 0;
			let segmentTotalScore: number = 0;

			contentArray = quiz.quizContent.slice(
				content_counter,
				content_counter + segment.count
			);

			content_counter += segment.count;

			let questions: TestSectionQuestion[] = [];
			for (let i = 0; i < contentArray.length; i++) {
				const question: TestSectionQuestion = {
					_id: "",
					score: 0,
					title: "",
					type: "",
					outOfTotalScore: 0,
					status: "Not Submitted",
					userAttempts: [],
					description: "",
					testCaseCount: 0,
				};
				if (
					quizSubmittedQuestion &&
					quizSubmittedQuestion.indexOf(contentArray[i].id) !== -1
				) {
					testDetails.totalQues += 1;
					segmentTotalScore += contentArray[i].question?.score ?? 0;
					segementScore += +(
						contentArray[i].quizSubmittedQuestion?.score ?? 0
					);
					if (contentArray[i].question) {
						const questionScore =
							contentArray[i].quizSubmittedQuestion?.score ?? 0;
						const questionOutOfTotalScore =
							contentArray[i].question?.score ?? 0;
						question._id = contentArray[i].question?._id ?? "";
						question.title = contentArray[i].question?.title ?? "";
						question.type = contentArray[i].question?.type ?? "";
						question.score = questionScore;

						question.outOfTotalScore = questionOutOfTotalScore;
						question.status = contentArray[i].quizSubmittedQuestion
							? contentArray[i].quizSubmittedQuestion
									?.hasSubmitted
								? "submitted"
								: "Executed"
							: "Not Submitted";
						question.userAttempts =
							contentArray[i].attempts?.userAttempts.map(
								attempt =>
									({
										...attempt,
										attemptData: attempt.attemptData.map(
											attemptData =>
												attemptData.fromRedis
													? {
															...attemptData,
															fromRedisData: {
																quizId: id,
																userId,
																questionId:
																	attempt.questionId,
															},
														}
													: attemptData
										),
									}) as UserAttempt
							) || [];
						question.description =
							contentArray[i].question?.text ?? "";
						question.testCaseCount =
							contentArray[i].question?.questionTypeCoding
								?.testCase.length ?? 0;
					}
					questions.push(question);
				}
				if (question.status !== "Not Submitted") {
					testDetails.userAttemptQues += 1;
				}
			}

			if (
				questions?.length &&
				(quiz.randomizeQuestion || quiz.poolQuestion) &&
				questionPriority &&
				Object.keys(questionPriority).length
			) {
				questions = questions.sort((a, b) => {
					return questionPriority[a._id] - questionPriority[b._id];
				});
			}

			testDetails.totalScore += segmentTotalScore;
			testDetails.userScore += segementScore;

			const segmentData: QuizSegmentsDataType = {
				title: segment.title,
				_id: segment._id,
				questions: questions,
				userScore: segementScore,
				segementTotalScore: segmentTotalScore,
				status:
					quiz.revisitAllowed ||
					submittedSectionsSet.has(index) ||
					(lastIndex === index &&
						submittedSectionsSet.size === lastIndex &&
						quiz.userQuizSubmittedSegment?.endTime)
						? "submitted"
						: "not submitted",
			};
			contentTabData.push(segmentData);
		});

		return {
			testDetails,
			contentTabData,
			sectionTitles,
		};
	}, [id, session, userData]);

	return !userData ? (
		<Flex
			justify="center"
			align="center"
			style={{
				height: "100vh",
			}}
		>
			<Spin size="large" />
		</Flex>
	) : (
		<Layout style={{ height: "100%" }}>
			<Layout
				style={{
					flex: 1,
					display: "flex",
					height: "100%",
				}}
			>
				<Sider
					width={350}
					theme="light"
					style={{
						overflowY: "auto",
						height: "100%",
						position: "relative",
						scrollbarWidth: "thin",
						borderRight: "1px solid #e8e8e8",
						paddingTop: 24,
					}}
				>
					<Flex
						vertical
						align="center"
						style={{
							width: "100%",
							height: "100%",
							textAlign: "center",
							alignItems: "center",
						}}
					>
						<Image
							width={80}
							height={80}
							src={""}
							fallback={`https://api.dicebear.com/5.x/initials/svg?seed=${userData?.userQuizSubmittedSegmentObj.displayName}&backgroundColor=DE6834&chars=1`}
							alt="User"
							preview={false}
							style={{
								borderRadius: "50%",
							}}
						/>
						<Text
							style={{
								fontSize: "16px",
								fontWeight: "500",
								marginTop: "8px",
								padding: "0 16px",
							}}
						>
							{userData?.quiz.displayname}
						</Text>
						<Divider />

						{(() => {
							const detailsRaw =
								userData.userQuizSubmittedSegmentObj
									?.quizUserDetails;

							let parsedDetails = [];

							if (Array.isArray(detailsRaw)) {
								parsedDetails = detailsRaw;
							} else if (typeof detailsRaw === "string") {
								try {
									const temp = JSON.parse(detailsRaw);
									if (Array.isArray(temp)) {
										parsedDetails = temp;
									}
								} catch (e) {
									console.error(
										"Failed to parse quizUserDetails:",
										e
									);
								}
							}

							const generalInfoItems = [
								{
									label: "Email",
									children: (
										<Text>
											{userData?.quiz?.email || "-"}
										</Text>
									),
								},
								{
									label: "Roll Number",
									children: (
										<Text>
											{userData?.quiz?.enrollmentId ||
												"-NA-"}
										</Text>
									),
								},
							];

							const customFields =
								parsedDetails.length > 0
									? descriptionCustomFieldData(parsedDetails)
									: [];

							const combinedItems = [
								...generalInfoItems,
								...customFields,
							];

							return (
								<Descriptions
									title="About"
									column={1}
									items={combinedItems}
									styles={{
										title: {
											textAlign: "start",
										},
										content: {
											textAlign: "start",
										},
									}}
									style={{
										padding: "0 16px",
									}}
								/>
							);
						})()}
						<Divider />

						<Descriptions
							title="General Info"
							column={1}
							items={[
								{
									label: "Marks Obtained",
									children: (
										<Text>
											{`${testDetails?.userScore ?? 0} /
																	${testDetails?.totalScore}`}
										</Text>
									),
								},
								{
									label: "Questions Attempted",
									children: (
										<Text>{`${testDetails?.userAttemptQues ?? 0} / ${testDetails?.totalQues}`}</Text>
									),
								},
							]}
							styles={{
								title: {
									textAlign: "start",
								},
								content: {
									textAlign: "start",
								},
							}}
							style={{
								padding: "0 16px",
							}}
						/>
					</Flex>
				</Sider>

				<Layout
					style={{
						flex: 1,
						padding: "24px",
						height: "100%",
						overflow: "auto",
						backgroundColor: "#FFFFFF",
					}}
				>
					<Content segmentsArray={contentTabData} />
				</Layout>
			</Layout>
		</Layout>
	);
};

export default LearnerUserReport;
