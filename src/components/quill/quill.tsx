import ReactQuill from "react-quill";
import { useEffect, useRef, useState } from "react";
import "react-quill/dist/quill.snow.css";
import { Button, ConfigProvider, Flex, Space, Tag } from "antd";
import { AnyObject } from "antd/es/_util/type";

interface QuillProps {
	templateVariable?: AnyObject;
	value?: string;
	onChange?: (_value: string) => void;
}
export default function Quill(props: QuillProps) {
	const isFirstRender = useRef(true);
	const { templateVariable, value, onChange } = props;
	const quillRef = useRef<ReactQuill | null>(null);
	const [quillValue, setValue] = useState("");
	const [isFocused, setIsFocused] = useState(false);
	const { componentDisabled } = ConfigProvider.useConfig() ?? {};

	const [mounted, setMounted] = useState(false);

	useEffect(() => {
		setMounted(true);
	}, []);

	const insertVariable = (variable: string) => {
		const editor = quillRef.current?.getEditor();
		if (editor) {
			const range = editor.getSelection();
			if (range) {
				editor.insertText(range.index, `<${variable}>`, "bold", true);
			}
		}
	};

	useEffect(() => {
		setValue(value ?? "");
	}, [componentDisabled, value]);

	const handleChange = (val: string) => {
		if (isFirstRender.current) {
			isFirstRender.current = false;
			return;
		}
		setValue(val);
		if (onChange) {
			onChange(val);
		}
	};
	const handleFocus = () => setIsFocused(true);
	const handleBlur = () => setIsFocused(false);

	return (
		<Space
			direction="vertical"
			style={{ width: "100%", position: "relative" }}
		>
			{templateVariable && (
				<div>
					{!isFocused && (
						<Tag
							style={{
								position: "absolute",
								marginTop: "auto",
								left: "50%",
								transform: "translateX(-50%)",
							}}
						>
							Cursor must be present in the editor to use
							variables
						</Tag>
					)}

					<Flex
						gap={8}
						wrap
						style={{
							opacity: isFocused ? 1 : 0.5,
							pointerEvents: isFocused ? "auto" : "none",
							filter: isFocused ? "none" : "blur(2px)",
							transition:
								"opacity 0.3s ease-in-out, filter 0.3s ease-in-out",
							position: "relative",
						}}
					>
						{Object.keys(templateVariable).map(key => (
							<Button
								key={key}
								onClick={() =>
									insertVariable(
										templateVariable[key].templateString
									)
								}
								type="dashed"
								size="small"
								disabled={!isFocused}
							>
								{templateVariable[key].label}
							</Button>
						))}
					</Flex>
				</div>
			)}
			{mounted && (
				<ReactQuill
					ref={quillRef}
					theme="snow"
					value={quillValue}
					onChange={handleChange}
					onFocus={handleFocus}
					onBlur={handleBlur}
					readOnly={componentDisabled}
					className="invite-edit-form-container"
					bounds=".invite-edit-form-container"
				/>
			)}
		</Space>
	);
}
