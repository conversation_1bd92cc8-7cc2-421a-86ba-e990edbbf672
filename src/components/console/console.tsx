import React from "react";

interface ConsoleProps {
	errors?: string;
	output?: string;
}

export const Console: React.FC<ConsoleProps> = ({
	errors = "",
	output = "",
}) => {
	return (
		<div
			style={{
				padding: "16px",
				borderRadius: "8px",
				border: "1px solid #333",
				backgroundColor: "black",
				color: "white",
				fontFamily: "monospace",
				whiteSpace: "pre-wrap",
				wordBreak: "break-word",
				width: "100%",
			}}
		>
			{errors && (
				<div style={{ color: "red" }}>
					<h3>Errors:</h3>
					<pre
						style={{
							textWrap: "balance",
						}}
					>
						{errors}
					</pre>
				</div>
			)}
			{output && (
				<div style={{ marginTop: "8px" }}>
					<h3>Output:</h3>
					<pre
						style={{
							textWrap: "balance",
						}}
					>
						{output}
					</pre>
				</div>
			)}
		</div>
	);
};
