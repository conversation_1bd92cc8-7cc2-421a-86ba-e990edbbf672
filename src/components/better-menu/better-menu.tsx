import { Divider, Row } from "antd";
import React from "react";

export type BetterMenuItem = {
	key?: React.Key;
} & (
	| {
			type: "divider";
			margin?: number | string;
	  }
	| {
			type?: "item";
			label?: React.ReactNode;
	  }
	| {
			type: "group";
			label?: React.ReactNode;
			children: React.ReactNode | BetterMenuItem[];
	  }
	| {
			type: "radio-group";
			label?: React.ReactNode;
			items: { key: React.Key; value: React.ReactNode }[];
	  }
	| {
			type: "input";
			label?: React.ReactNode;
	  }
);

interface BetterMenuProps {
	items: BetterMenuItem[];
}

const itemLabelStyle: React.CSSProperties = {
	opacity: 0.5,
	marginBottom: 8,
	fontSize: 12,
};

export default function BetterMenu(props: BetterMenuProps) {
	return (
		<>
			{props.items.map((item, index) => {
				const itemKey = item.key ?? "bm-item-" + index;
				switch (item.type) {
					case "divider":
						return (
							<Divider
								key={itemKey}
								style={{
									marginTop: item.margin ?? 12,
									marginBottom: item.margin ?? 10,
								}}
							/>
						);

					case "group":
						return (
							<>
								{item.label === undefined ? undefined : (
									<div key={itemKey} style={itemLabelStyle}>
										{item.label}
									</div>
								)}
								{item.children instanceof Array ? (
									<BetterMenu
										key={item.key}
										items={item.children}
									/>
								) : (
									<>{item.children}</>
								)}
							</>
						);

					case "item":
						return <Row key={itemKey}>{item.label}</Row>;
				}
			})}
		</>
	);
}
