import { DataTable } from "@/components/dataTable/dataTable";
import { Button, Flex, Modal, TableColumnsType, Typography } from "antd";
import { DownloadOutlined } from "@ant-design/icons";
import * as XLSX from "xlsx-js-style";
import { AnyObject } from "antd/es/_util/type";

const { Text } = Typography;

const handleDownload = (data: unknown[]) => {
	try {
		const workbook = XLSX.utils.book_new();
		const worksheet = XLSX.utils.json_to_sheet(data);
		XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");
		XLSX.writeFile(workbook, "Error_Upload.xlsx");
	} catch (error) {
		console.log(error);
	}
};

interface InValidBulkUploadProps {
	data: AnyObject[];
	clearFailedData: () => void;
	columns: TableColumnsType<AnyObject>;
	title: string;
	description: string;
}

const InValidBulkUpload = (props: InValidBulkUploadProps) => {
	const { data, clearFailedData, columns, title, description } = props;

	return (
		<>
			<Modal
				open={data.length > 0}
				title={title}
				onCancel={() => clearFailedData()}
				centered
				footer={[
					<Button
						key="download"
						type="primary"
						icon={<DownloadOutlined />}
						onClick={() => handleDownload(data)}
					>
						Download
					</Button>,
				]}
				style={{ gap: "50px" }}
				destroyOnClose
				width={600}
				styles={{
					body: {
						maxHeight: "65vh",
						overflow: "auto",
					},
				}}
			>
				<Flex
					vertical
					gap="middle"
					style={{
						width: "100%",
						height: "100%",
						overflow: "auto",
					}}
				>
					<Text type="secondary">{description}</Text>
					<DataTable
						columns={columns}
						data={data}
						pagination={false}
					/>
				</Flex>
			</Modal>
		</>
	);
};

export default InValidBulkUpload;
