import { QuestionTypeSubjective } from "@/testReport/data/data";
import { PaperClipOutlined } from "@ant-design/icons";
import { Button, Divider, Flex, Upload } from "antd";
import Title from "antd/es/typography/Title";
import ReactQuill from "react-quill";

export default function PreviewSubjective({
	questionDetails,
}: {
	questionDetails?: QuestionTypeSubjective;
}) {
	const customMenu = [
		[{ header: [1, 2, 3, 4, 5, 6, false] }],
		[{ indent: "-1" }, { indent: "+1" }],
		["bold", "italic", "underline", "strike"],
		["image"],
		["clean"],
	];
	console.log(questionDetails);
	return (
		<Flex
			style={{ width: "100%", height: "100%", padding: "0.75em" }}
			vertical
			gap={16}
		>
			<div>
				<Title level={3}>Answer below:</Title>
				<Divider />
			</div>
			<ReactQuill
				theme="snow"
				bounds=".bound"
				style={{
					height: "250px",
					paddingBottom: 45,
					width: "100%",
				}}
				modules={{
					toolbar: customMenu,
				}}
			/>
			{questionDetails?.isFileUpload ? (
				<Upload>
					<Button icon={<PaperClipOutlined />} iconPosition="end">
						Choose File
					</Button>
				</Upload>
			) : (
				<></>
			)}
			<Flex style={{ marginTop: "auto" }}>
				<Button type="primary" style={{ marginLeft: "auto" }}>
					Submit
				</Button>
			</Flex>
		</Flex>
	);
}
