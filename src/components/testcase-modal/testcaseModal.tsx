// src/components/TestCaseModal.tsx

import { Button, Form, Input, InputNumber, Modal, Spin } from "antd";
import TextArea from "antd/es/input/TextArea";
import SwitchCard from "@/components/form/switch-card";
import { TestCase } from "@/testReport/data/data";
import { useEffect } from "react";
import { useForm } from "antd/es/form/Form";

interface TestCaseModalProps {
	title: string;
	showModal: boolean;
	loading?: boolean;
	editingTestCase?: (TestCase & { index: number }) | null;
	isModelDataLoading?: boolean;
	previewTestCase?: {
		name: string;
		scoreip: number;
		codeproginputparams: string;
		codeprogexpectedoutput: string;
	} | null;
	onCancel?: () => void;
	onSubmit?: (_data: {
		input: string;
		output: string;
		score: number;
		sampleTest: boolean;
	}) => Promise<void>;
	testCases?: TestCase[];
}

const TestCaseModal: React.FC<TestCaseModalProps> = ({
	title,
	showModal,
	loading = false,
	editingTestCase,
	previewTestCase,
	onCancel,
	onSubmit,
	testCases,
	isModelDataLoading,
}) => {
	const [testCaseForm] = useForm();

	useEffect(() => {
		if (editingTestCase && testCases) {
			// THIS CODE NEEDS REFACTOR
			testCaseForm.setFieldsValue({
				name: `Test case ${editingTestCase.index + 1}`,
				score: editingTestCase.scoreip,
				input: editingTestCase.codeproginputparams,
				output: editingTestCase.codeprogexpectedoutput,
				sampleTest: editingTestCase.sampleTest,
			});
		} else if (previewTestCase) {
			testCaseForm.setFieldsValue({
				name: previewTestCase.name,
				score: previewTestCase.scoreip,
				input: previewTestCase.codeproginputparams,
				output: previewTestCase.codeprogexpectedoutput,
			});
		} else {
			testCaseForm.resetFields();
		}
	}, [editingTestCase, testCases, testCaseForm, previewTestCase]);

	return (
		<Modal
			title={title}
			open={showModal}
			onCancel={onCancel}
			footer={
				previewTestCase
					? null
					: [
							<Button
								key="save"
								type="primary"
								htmlType="submit"
								loading={loading}
								onClick={() => testCaseForm.submit()}
							>
								Save
							</Button>,
						]
			}
			width="100%"
			style={{ maxWidth: "580px" }}
			destroyOnClose
			centered
		>
			{isModelDataLoading && (
				<div
					style={{
						width: "100%",
						display: "flex",
						justifyContent: "center",
					}}
				>
					<Spin size="default" />
				</div>
			)}
			<Form
				layout="vertical"
				requiredMark={false}
				form={testCaseForm}
				disabled={previewTestCase ? true : false}
				onFinish={onSubmit}
				initialValues={{
					name: `Test case ${testCases ? testCases.length + 1 : "1"}`,
					sampleTest: false,
				}}
				style={{
					display: isModelDataLoading ? "none" : "flex",
					flexDirection: "column",
					gap: "16px",
					maxHeight: "80vh",
					overflow: "auto",
					paddingRight: 16,
				}}
			>
				<Form.Item label="Name" name="name">
					<Input disabled />
				</Form.Item>

				<Form.Item
					label="Score"
					name="score"
					rules={[
						{
							validator: (_, value) => {
								if (value === undefined || value === null) {
									return Promise.reject(
										new Error("Please enter a score.")
									);
								}

								if (!testCases) return Promise.resolve();

								const totalScore =
									testCases.reduce(
										(acc, testCase) =>
											acc + (testCase.scoreip || 0),
										0
									) +
									value -
									(editingTestCase?.scoreip ?? 0);

								if (value < 0 || value > 9999) {
									return Promise.reject(
										new Error(
											"Score must be between 0 and 9999."
										)
									);
								}

								if (totalScore > 9999) {
									return Promise.reject(
										new Error(
											"Total score cannot exceed 9999."
										)
									);
								}

								return Promise.resolve();
							},
						},
					]}
				>
					<InputNumber style={{ width: "100%" }} min={0} />
				</Form.Item>

				<Form.Item
					label="Input"
					name="input"
					rules={[
						{
							required: true,
							message: "Test case input is required",
						},
					]}
				>
					<TextArea />
				</Form.Item>

				<Form.Item
					label="Output"
					name="output"
					rules={[
						{
							required: true,
							message: "Test case output is required",
						},
					]}
				>
					<TextArea />
				</Form.Item>

				{!previewTestCase && (
					<SwitchCard
						form={testCaseForm}
						name="sampleTest"
						labelTitle="Sample test case"
						labelDescription="Sample test case provides candidates an idea of expected output."
					/>
				)}
			</Form>
		</Modal>
	);
};

export default TestCaseModal;
