import { RoleAction, RoleResource } from "@/constants/roles";
import { useAppStore } from "../../store/index";
import AdminDashboard from "../homeDashboard/dashboard";
import LearnerDashBoard from "../learnerDashboard/LearnerDashBoard";
import { Typography, Flex } from "antd";
import { DashboardOutlined } from "@ant-design/icons";

const { Title, Text } = Typography;

const NoAccessMessage = () => (
	<Flex align="center" justify="center" style={{ height: "100%" }}>
		<div style={{ textAlign: "center", maxWidth: 400 }}>
			<DashboardOutlined
				style={{ fontSize: 48, marginBottom: 16, color: "#ff4d4f" }}
			/>
			<Title level={3} style={{ color: "#ff4d4f" }}>
				Dashboard Not Available
			</Title>
			<Text type="secondary" style={{ fontSize: 16 }}>
				You don't have permission to access any dashboard. Please
				contact your administrator for access.
			</Text>
		</div>
	</Flex>
);

export const Dashboard = () => {
	const hasResourcePermission = useAppStore(
		state => state.hasResourcePermission
	);
	if (hasResourcePermission(RoleResource.QUIZ_DASHBOARD, RoleAction.VIEW)) {
		return <AdminDashboard />;
	}
	if (hasResourcePermission(RoleResource.USER_DASHBOARD, RoleAction.VIEW)) {
		return <LearnerDashBoard />;
	}
	return <NoAccessMessage />;
};
