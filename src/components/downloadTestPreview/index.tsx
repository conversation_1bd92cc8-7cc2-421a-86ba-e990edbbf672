import { AppConfig } from "@/config";
import { useLocation, useSearchParams } from "react-router";
import PageHelmet from "../page-helmet/PageHelmet";

const QuizDownloadPreview = () => {
	const url = useLocation();
	const [searchParams] = useSearchParams();
	const title = searchParams.get("title");
	const withoutTestCases = searchParams.get("withoutTestCases");
	const includeCases = withoutTestCases ? "?withoutTestCases=1" : "";
	console.log(url, title);
	return (
		<>
			<PageHelmet title={title || ""} />
			<iframe
				src={`${AppConfig.quizServerURL}/test${url.pathname}${includeCases}`}
				style={{ width: "100%", height: "99%", border: "none" }}
			></iframe>
		</>
	);
};

export default QuizDownloadPreview;
