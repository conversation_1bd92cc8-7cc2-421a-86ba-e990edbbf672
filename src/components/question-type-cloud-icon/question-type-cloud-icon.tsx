import { QuestionType, QuestionTypesMap } from "@/client/test-add";
import { CloudOutlined } from "@ant-design/icons";
import { Space } from "antd";

export default function QuestionTypeCloudIcon(props: {
	question: { type: QuestionType; isOnCloud?: boolean };
}) {
	const searchParams = new URLSearchParams(window.location.search);
	const shouldShowCloudIcon =
		searchParams.has("show_cloud_icon") &&
		props.question.type === QuestionType.CODING &&
		props.question.isOnCloud;
	return (
		<Space>
			{QuestionTypesMap[props.question.type]}
			{shouldShowCloudIcon && <CloudOutlined />}
		</Space>
	);
}
