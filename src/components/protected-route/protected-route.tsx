import { RoleAction, RoleResource } from "@/constants/roles";
import { useAppStore } from "../../store";
import useMessage from "antd/es/message/useMessage";
import React from "react";
import { Navigate } from "react-router";

interface ProtectedRouteProps {
	children: React.ReactNode;
	resource: RoleResource;
	action?: RoleAction;
}

export default function ProtectedRoute(props: ProtectedRouteProps) {
	const hasResourcePermission = useAppStore().hasResourcePermission(
		props.resource,
		props.action
	);
	const [messageAPI, messageContext] = useMessage();
	if (!hasResourcePermission) {
		// Todo: create not authorised page
		messageAPI.error("Not authorised");
		return (
			<>
				{messageContext}
				<Navigate to="/error" />
			</>
		);
	}
	return props.children;
}
