import { <PERSON><PERSON>, Flex, <PERSON>u, <PERSON>, Typography } from "antd";
import { ItemType, MenuItemType } from "antd/es/menu/interface";
import {
	LoginOutlined,
	LogoutOutlined,
	PlusOutlined,
	SettingOutlined,
	UnorderedListOutlined,
	UserOutlined,
	MailOutlined,
} from "@ant-design/icons";
import { Link, useLocation, useNavigate } from "react-router";
import { AppConfig } from "@/config";
import { useAppStore } from "../../store";
import { useEffect, useState } from "react";
import { LogoIcon, PlayGroundIcon, QuestionIcon, TestIcon } from "./icons";
import { RoleAction, RoleResource, UserRole } from "@/constants/roles";
import { PlatformOrganisation } from "@/constants";
import CreateTestDrawer from "@/testList/CreateTestDrawer";

export default function Header() {
	const user = useAppStore(store => store.user);
	const session = useAppStore(store => store.session);
	const [current, setCurrent] = useState<string>("");
	const location = useLocation();
	const navigate = useNavigate();
	const hasResourcePermission = useAppStore(
		state => state.hasResourcePermission
	);
	const [showCreateTestDrawer, setShowCreateTestDrawer] = useState(false);

	const headerMenuLeftItems: ItemType<MenuItemType>[] = [
		!hasResourcePermission(RoleResource.QUIZ_DASHBOARD, RoleAction.VIEW)
			? null
			: { key: "dashboard", label: <Link to="/">Dashboard</Link> },
		!hasResourcePermission(RoleResource.QUIZ, RoleAction.LIST)
			? null
			: { key: "tests", label: <Link to="/tests">Tests</Link> },
		!hasResourcePermission(RoleResource.QUEST, RoleAction.LIST)
			? null
			: {
					key: "questions",
					label: <Link to="/questions">Questions</Link>,
				},
		//  Super admin, super org for CQ and Sub Admin for Chitkara
		hasResourcePermission(RoleResource.USER_DASHBOARD, RoleAction.VIEW) &&
		(user.role.id === UserRole.USER || user.role.id === UserRole.FACULTY) // Taken from Quiz Server
			? {
					key: "test-attempts",
					label: <Link to="/test-attempts">Test Attempts</Link>,
				}
			: null,
		user.role.id !== UserRole.USER &&
		user.role.id === UserRole.ADMIN &&
		AppConfig.platformOrganisation !== PlatformOrganisation.CHITKARA
			? {
					key: "roles",
					label: (
						<Link to={`${AppConfig.quizServerURL}/userRole`}>
							Roles
						</Link>
					),
				}
			: null,
	];
	const headerMenuRightItems: ItemType<MenuItemType>[] = [
		{
			key: "new",
			type: "submenu",
			label: (
				<Space
					style={{
						padding: "0px 1.35rem",
						backgroundColor: "#de6834",
						color: "white",
					}}
				>
					<PlusOutlined style={{ fontSize: 16 }} />
				</Space>
			),
			children: [
				!hasResourcePermission(RoleResource.QUIZ, RoleAction.ADD)
					? null
					: {
							key: "test",
							label: "New test",
							icon: <TestIcon />,

							onClick: () => setShowCreateTestDrawer(true),
						},
				!hasResourcePermission(RoleResource.QUEST, RoleAction.ADD)
					? null
					: {
							key: "question",
							label: "New question",
							icon: <QuestionIcon />,
							onClick: () => navigate("/questions/add"),
						},
				{
					key: "playgorund",
					label: (
						<Link
							to={`${AppConfig.quizServerURL}/playground`}
							target="_blank"
						>
							Playground
						</Link>
					),
					icon: <PlayGroundIcon />,
				},
			],
		},
		{
			key: "options",
			type: "submenu",
			label: (
				<Flex align="center">
					<Space size="middle">
						<Flex vertical align="flex-end" gap={0}>
							<Typography.Text
								style={{ fontSize: 16, lineHeight: 1.35 }}
							>
								{user?.displayName}
							</Typography.Text>
							<Typography.Text
								type="secondary"
								style={{
									fontWeight: 500,
									lineHeight: 1.35,
								}}
							>
								{[user?.role.title]}
							</Typography.Text>
						</Flex>
						<Avatar size={40}>
							{user?.displayName.charAt(0) ?? "U"}
						</Avatar>
					</Space>
				</Flex>
			),
			children: [
				hasResourcePermission(RoleResource.COURSE) ||
				hasResourcePermission(RoleResource.TUTORIAL) ||
				hasResourcePermission(RoleResource.BATCH) ||
				hasResourcePermission(RoleResource.ACC) ||
				hasResourcePermission(RoleResource.USER_DASHBOARD)
					? {
							key: "course",
							label: (
								<Link to={`${AppConfig.courseServerURL}`}>
									Course
								</Link>
							),
							icon: <UnorderedListOutlined />,
						}
					: null,
				hasResourcePermission(RoleResource.USER, RoleAction.LIST)
					? {
							key: "users",
							label: (
								<Link to={`${AppConfig.quizServerURL}/user`}>
									Users
								</Link>
							),
							icon: <UserOutlined />,
						}
					: null,
				hasResourcePermission(RoleResource.EMAIL, RoleAction.LIST)
					? {
							key: "emails",
							label: (
								<Link
									to={`${AppConfig.quizServerURL}/email?endpoint=/email/list`}
								>
									Emails
								</Link>
							),
							icon: <MailOutlined />,
						}
					: null,
				!session?.userId
					? {
							key: "login",
							label: (
								<Link to={`${AppConfig.quizServerURL}/login`}>
									Login
								</Link>
							),
							icon: <LoginOutlined />,
						}
					: null,
				session?.userId
					? {
							key: "settings",
							label: (
								<Link
									to={`${AppConfig.quizServerURL}/settings`}
								>
									Settings
								</Link>
							),
							icon: <SettingOutlined />,
						}
					: null,
				session?.userId
					? {
							key: "logout",
							label: "Logout",
							icon: <LogoutOutlined />,
							onClick: () => {
								localStorage.clear();
								window.location.href = `${AppConfig.quizServerURL}/logout`;
							},
						}
					: null,
			].filter(Boolean),
		},
	];
	useEffect(() => {
		const pathName = window.location.href;
		if (pathName.includes("tests")) {
			if (pathName.includes("dashboard")) {
				setCurrent("dashboard");
			} else {
				setCurrent("tests");
			}
		} else if (pathName.includes("questions")) {
			setCurrent("questions");
		} else if (pathName.includes("userRole")) {
			setCurrent("roles");
		} else if (pathName.includes("test-attempts")) {
			setCurrent("test-attempts");
		} else {
			if (user.role.id === UserRole.USER) {
				setCurrent("test-attempts");
			} else {
				setCurrent("dashboard");
			}
		}
	}, [location.pathname]);
	return (
		<>
			<Flex
				align="center"
				justify="space-between"
				style={{ width: "100%" }}
			>
				<Flex
					onClick={() => navigate("/")}
					style={{ padding: "0rem 1rem" }}
				>
					<LogoIcon />
				</Flex>
				<Menu
					theme="light"
					mode="horizontal"
					items={headerMenuLeftItems}
					selectedKeys={[current]}
					style={{ flex: 1 }}
				/>
				<Menu
					theme="light"
					mode="horizontal"
					defaultSelectedKeys={["dashboard"]}
					items={headerMenuRightItems}
					style={{
						flexGrow: 1,
						justifyContent: "flex-end",
					}}
				/>
			</Flex>
			<CreateTestDrawer
				open={showCreateTestDrawer}
				onClose={() => setShowCreateTestDrawer(false)}
				onFinish={() => setShowCreateTestDrawer(false)}
			/>
		</>
	);
}
