import { AppConfig } from "@/config";
import { PlatformOrganisation } from "@/constants";
import Icon from "@ant-design/icons";
import ChitkaraLogo from "../../assets/img/chitkara.png";

export const LogoIcon = function (props: object) {
	return AppConfig.platformOrganisation === PlatformOrganisation.CHITKARA ? (
		<img src={ChitkaraLogo} alt="chitkara logo" height={38} />
	) : (
		<Icon
			component={() => (
				<svg
					xmlns="http://www.w3.org/2000/svg"
					id="Component_10_6"
					data-name="Component 10 – 6"
					viewBox="0 -30 792.463 175"
					height="2em"
					width="100%"
				>
					<path
						id="Path_129"
						data-name="Path 129"
						d="M14.586-9.078q-10.3-10.4-10.3-27.234t10.3-27.234q10.3-10.4,26.112-10.4,12.852,0,22.236,6.783a31.115,31.115,0,0,1,12.24,18.411l-15.3,3.162a17.568,17.568,0,0,0-6.732-10.455A20.531,20.531,0,0,0,40.7-59.772q-9.792,0-15.249,6.324T19.992-36.312q0,10.812,5.457,17.136T40.7-12.852a20.531,20.531,0,0,0,12.444-3.723A17.568,17.568,0,0,0,59.874-27.03l15.3,3.162A31.115,31.115,0,0,1,62.934-5.457Q53.55,1.326,40.7,1.326,24.888,1.326,14.586-9.078ZM127.194-6.834q-7.752,8.058-20.3,8.058T86.6-6.885a27.631,27.631,0,0,1-7.752-19.839A27.631,27.631,0,0,1,86.6-46.563q7.752-8.109,20.3-8.109t20.3,8.109a27.631,27.631,0,0,1,7.752,19.839A27.807,27.807,0,0,1,127.194-6.834ZM97.1-37.842q-3.774,4.08-3.774,11.118t3.825,11.118a12.768,12.768,0,0,0,9.741,4.08,12.768,12.768,0,0,0,9.741-4.08q3.825-4.08,3.825-11.118t-3.825-11.118a12.768,12.768,0,0,0-9.741-4.08A12.93,12.93,0,0,0,97.1-37.842Zm81.7-9.894V-76.5H193.29V0H179.928V-6.528Q174.624,1.224,163,1.224q-11.322,0-17.952-7.956t-6.63-19.992q0-12.036,6.63-19.992T163-54.672a21.488,21.488,0,0,1,9.435,1.989A17.125,17.125,0,0,1,178.806-47.736ZM175.542-15.81a16.178,16.178,0,0,0,3.672-10.914,16.322,16.322,0,0,0-3.621-10.914,11.876,11.876,0,0,0-9.537-4.284,11.876,11.876,0,0,0-9.537,4.284A16.322,16.322,0,0,0,152.9-26.724a16.322,16.322,0,0,0,3.621,10.914,11.876,11.876,0,0,0,9.537,4.284A11.729,11.729,0,0,0,175.542-15.81Zm62.526-1.632,13.362,3.06A24.982,24.982,0,0,1,242.2-2.907q-6.171,4.131-15.147,4.131-11.832,0-19.38-7.854t-7.548-20.2q0-11.934,7.548-19.89t19.278-7.956q11.73,0,19.125,7.905t7.4,19.941l-.2,4.284h-38.76a12.813,12.813,0,0,0,4.08,8.415,13.1,13.1,0,0,0,8.874,2.907Q235.212-11.22,238.068-17.442Zm-11.22-25.6a11.965,11.965,0,0,0-8.058,2.7,12.539,12.539,0,0,0-4.08,7.395h24.276a12.232,12.232,0,0,0-3.978-7.344A11.974,11.974,0,0,0,226.848-43.044ZM295.494,1.53q-16.422,0-27.03-10.71T257.856-36.21q0-16.32,10.608-27.03t27.03-10.71q16.422,0,27.03,10.71t10.608,27.03a39.776,39.776,0,0,1-3.315,16.269,34.6,34.6,0,0,1-9.333,12.6l.816.918a21.737,21.737,0,0,0,6.069,5.61,13.1,13.1,0,0,0,6.375,1.53l2.04-.1V13.566q-1.632.1-2.142.1-13.566,0-22.542-10.914L308.448-.51A40.7,40.7,0,0,1,295.494,1.53Zm15.81-20.6q6.12-6.528,6.12-17.136T311.3-53.295q-6.12-6.477-15.81-6.477t-15.81,6.477q-6.12,6.477-6.12,17.085t6.12,17.085q6.12,6.477,15.81,6.477T311.3-19.074Zm66.3-3.978v-30.4h14.484V0H378.726V-6.528q-4.182,7.752-15.708,7.752-9.384,0-14.433-5.763t-5.049-15.555V-53.448H358.02v31.722q0,10.506,8.874,10.506,5.3,0,8.007-3.06T377.6-23.052ZM450.33-6.834q-7.752,8.058-20.3,8.058t-20.3-8.109a27.631,27.631,0,0,1-7.752-19.839,27.631,27.631,0,0,1,7.752-19.839q7.752-8.109,20.3-8.109t20.3,8.109a27.631,27.631,0,0,1,7.752,19.839A27.807,27.807,0,0,1,450.33-6.834ZM420.24-37.842q-3.774,4.08-3.774,11.118t3.825,11.118a12.768,12.768,0,0,0,9.741,4.08,12.768,12.768,0,0,0,9.741-4.08q3.825-4.08,3.825-11.118t-3.825-11.118a12.768,12.768,0,0,0-9.741-4.08A12.93,12.93,0,0,0,420.24-37.842Zm72.828,24.99V-.306a29.624,29.624,0,0,1-6.834.612q-16.422,0-16.422-16.32v-26.01H462.57V-53.448h7.242v-13.26H484.3v13.26h8.874v11.424H484.3v24.48q0,5.2,5.406,5.2Zm22.542-49.98a8.558,8.558,0,0,1-6.324,2.448,8.558,8.558,0,0,1-6.324-2.448,8.429,8.429,0,0,1-2.448-6.222,8.6,8.6,0,0,1,2.448-6.273,8.464,8.464,0,0,1,6.324-2.5,8.464,8.464,0,0,1,6.324,2.5,8.6,8.6,0,0,1,2.448,6.273A8.429,8.429,0,0,1,515.61-62.832ZM502.044,0V-53.448h14.484V0Zm62.322-17.442,13.362,3.06A24.982,24.982,0,0,1,568.5-2.907q-6.171,4.131-15.147,4.131-11.832,0-19.38-7.854t-7.548-20.2q0-11.934,7.548-19.89t19.278-7.956q11.73,0,19.125,7.905t7.395,19.941l-.2,4.284H540.8a12.813,12.813,0,0,0,4.08,8.415,13.1,13.1,0,0,0,8.874,2.907Q561.51-11.22,564.366-17.442Zm-11.22-25.6a11.965,11.965,0,0,0-8.058,2.7,12.539,12.539,0,0,0-4.08,7.395h24.276a12.232,12.232,0,0,0-3.978-7.344A11.974,11.974,0,0,0,553.146-43.044Zm49.776-10.4v6.528q4.182-7.752,15.912-7.752,9.486,0,14.586,5.763t5.1,15.555V0H624.036V-31.722q0-10.506-9.078-10.506-5.2,0-8.058,3.111T604.044-30.6V0H589.56V-53.448Zm73.44,40.6V-.306a29.624,29.624,0,0,1-6.834.612q-16.422,0-16.422-16.32v-26.01h-7.242V-53.448h7.242v-13.26H667.59v13.26h8.874v11.424H667.59v24.48q0,5.2,5.406,5.2Z"
						transform="translate(115.999 91.579)"
						fill="#de6834"
					/>
					<g
						id="Group_23"
						data-name="Group 23"
						transform="translate(-327 -258)"
					>
						<g
							id="Component_8_37"
							data-name="Component 8 – 37"
							transform="translate(327 258)"
						>
							<rect
								id="Rectangle_341"
								data-name="Rectangle 341"
								width="99"
								height="115"
								rx="6"
								fill="#de6834"
							/>
							<path
								id="Path_124"
								data-name="Path 124"
								d="M17.659,4.587a21.8,21.8,0,0,1-6.9-.939A9.354,9.354,0,0,1,6.247.47,14.548,14.548,0,0,1,3.719-5.38a42.549,42.549,0,0,1-.794-8.956,43.078,43.078,0,0,1,.794-8.992,14.255,14.255,0,0,1,2.528-5.85,9.438,9.438,0,0,1,4.55-3.142,21.868,21.868,0,0,1,6.861-.939q2.022,0,4.3.181t4.333.469a19.452,19.452,0,0,1,3.358.722L28.637-24.23a36.136,36.136,0,0,0-4.225-.758,37.7,37.7,0,0,0-4.442-.253q-3.106,0-4.333,1.3t-1.228,4.406V-4.008a20.137,20.137,0,0,0,5.561.578,24.274,24.274,0,0,0,4.622-.433,28.3,28.3,0,0,0,4.839-1.444l1.011,7.656a31.041,31.041,0,0,1-6.031,1.661A38.928,38.928,0,0,1,17.659,4.587ZM64.387-32.392h7.222V14.626L57.165,15.637V3.72A20.379,20.379,0,0,1,49.942,4.8a15.888,15.888,0,0,1-7.583-1.7A11.88,11.88,0,0,1,37.52-1.48a27.149,27.149,0,0,1-3.106-13.542q0-8.053,4.081-13.253t12.892-5.2a19.436,19.436,0,0,1,10.978,3.106ZM49.942-19.9V-5.741h2.239a9.777,9.777,0,0,0,3.792-.542q1.192-.542,1.192-2.492V-22.93H54.926a9.777,9.777,0,0,0-3.792.542Q49.942-21.847,49.942-19.9Z"
								transform="translate(12 71)"
								fill="#fff"
							/>
						</g>
					</g>
				</svg>
			)}
			{...props}
		/>
	);
};

export const PlayGroundIcon = function (props: object) {
	return (
		<Icon
			component={() => (
				<svg
					xmlns="http://www.w3.org/2000/svg"
					width="1em"
					height="1em"
					viewBox="0 0 24 26"
				>
					<g id="filled" fill="currentColor">
						<circle cx="5.5" cy="5.5" r="5.5" />
						<circle
							cx="5.5"
							cy="5.5"
							r="5.5"
							transform="translate(13 7)"
						/>
						<circle
							cx="5.5"
							cy="5.5"
							r="5.5"
							transform="translate(0 15)"
						/>
					</g>
				</svg>
			)}
			{...props}
		/>
	);
};

export const QuestionIcon = (props: object) => {
	return (
		<Icon
			component={() => (
				<svg
					xmlns="http://www.w3.org/2000/svg"
					viewBox="0 -960 960 960"
					width="1em"
					height="1em"
				>
					{/* fill currentColor */}
					<g id="filled" fill="white">
						<path d="M262.5-325q11.5 0 20.25-8.5t8.75-20q0-11.5-8.75-20.25t-20.25-8.75q-11.5 0-20 8.75T234-353.5q0 11.5 8.5 20t20 8.5Zm.175-128.5q11.825 0 20.075-8.375T291-482.5V-612q0-11.75-8.425-20.125-8.426-8.375-20.25-8.375-12.325 0-20.575 8.375T233.5-612v129.5q0 12.25 8.425 20.625 8.426 8.375 20.75 8.375ZM440-373.5h258q11.75 0 20.125-8.425 8.375-8.426 8.375-20.75 0-11.825-8.375-20.075T698-431H440q-12.25 0-20.625 8.425-8.375 8.426-8.375 20.25 0 12.325 8.375 20.575T440-373.5Zm0-169.5h258q11.75 0 20.125-8.425 8.375-8.426 8.375-20.75 0-11.825-8.375-20.075T698-600.5H440q-12.25 0-20.625 8.425-8.375 8.426-8.375 20.25 0 12.325 8.375 20.575T440-543ZM134.5-164.5q-23 0-40.25-17.25T77-222v-516q0-23 17.25-40.25t40.25-17.25h691q23 0 40.25 17.25T883-738v516q0 23-17.25 40.25T825.5-164.5h-691Z" />
					</g>
					<g id="outlined" fill="currentColor">
						<path d="M262.5-325q11.5 0 20.25-8.5t8.75-20q0-11.5-8.75-20.25t-20.25-8.75q-11.5 0-20 8.75T234-353.5q0 11.5 8.5 20t20 8.5Zm-.075-128.5q12.075 0 20.325-8.375T291-482.5V-612q0-11.675-8.463-20.088-8.463-8.412-20.212-8.412-12.325 0-20.575 8.412-8.25 8.413-8.25 20.088v129.5q0 12.25 8.425 20.625 8.426 8.375 20.5 8.375ZM440-373.5h258q11.675 0 20.088-8.425 8.412-8.426 8.412-20.5 0-12.075-8.412-20.325Q709.675-431 698-431H440q-12.25 0-20.625 8.463T411-402.325q0 12.325 8.375 20.575T440-373.5Zm0-169.5h258q11.675 0 20.088-8.425 8.412-8.426 8.412-20.5 0-12.075-8.412-20.325-8.413-8.25-20.088-8.25H440q-12.25 0-20.625 8.463T411-571.825q0 12.325 8.375 20.575T440-543ZM134.5-164.5q-22.969 0-40.234-17.266Q77-199.031 77-222v-516q0-22.969 17.266-40.234Q111.53-795.5 134.5-795.5h691q22.969 0 40.234 17.266Q883-760.969 883-738v516q0 22.969-17.266 40.234Q848.469-164.5 825.5-164.5h-691Zm0-57.5h691v-516h-691v516Zm0 0v-516 516Z" />
					</g>
				</svg>
			)}
			{...props}
		/>
	);
};

export const TestIcon = function (props: object) {
	return (
		<Icon
			component={() => (
				<svg
					xmlns="http://www.w3.org/2000/svg"
					viewBox="0 -960 960 960"
					width="1em"
					height="1em"
					fill="currentColor"
				>
					<g id="filled" fill="currentColor">
						<path d="M830.5-230.833 695.763-96.096q-4.93 4.93-10.491 7.18-5.562 2.25-11.439 2.25h-67.166q-13.333 0-22.417-9.25-9.083-9.25-9.083-22.584v-66.867q0-5.843 2.083-11.404 2.084-5.562 7.25-10.728l134.334-134.334 111.666 111ZM858.667-259 747-369.999l55.501-55.501q9.5-9.5 22.197-9.5t22.469 9.5l66.5 66.785q9.5 9.548 9.5 22.382 0 12.833-9.5 22.333l-55 55ZM391.833-633.334q-13.333 0-22.583-9.284-9.25-9.283-9.25-22.499 0-13.217 9.25-22.383 9.25-9.167 22.583-9.167h290.001q12.95 0 22.224 9.309 9.275 9.308 9.275 22.308 0 13.383-9.275 22.549-9.274 9.167-22.224 9.167H391.833Zm0 120q-13.333 0-22.583-9.284-9.25-9.283-9.25-22.499 0-13.217 9.25-22.383 9.25-9.167 22.583-9.167h290.001q12.95 0 22.224 9.309 9.275 9.308 9.275 22.308 0 13.383-9.275 22.549-9.274 9.167-22.224 9.167H391.833Zm-158.5 426.667q-44.333 0-75.5-30.916-31.166-30.917-31.166-75.083V-260q0-26.188 18.572-44.76 18.573-18.573 44.761-18.573h50V-810q0-26.188 18.573-44.761 18.573-18.572 44.76-18.572H770q26.188 0 44.761 18.572 18.572 18.573 18.572 44.761v264.001L770-482.666V-810H303.333v486.667h307.335l-79.834 79.834q-8.833 8.833-13.901 20.603-5.067 11.771-5.067 24.586v111.643H233.333Z" />
					</g>
					<g id="outlined" fill="currentColor">
						<path d="M227.5-85q-42.75 0-72.625-29.75T125-187v-78q0-23.719 16.891-40.609Q158.781-322.5 182.5-322.5H240v-495q0-23.719 16.891-40.609Q273.781-875 297.5-875h480q23.719 0 40.609 16.891Q835-841.219 835-817.5v326q-15.32 0-29.91 6.25T777.5-468v-349.5h-480v495H632L574.5-265h-392v78q0 18.912 12.959 31.706t30.898 12.794H524V-85H227.5ZM619-122.5h53L832.5-283l26 26L695.145-93.645Q690.517-89 685.446-87q-5.07 2-9.946 2H610q-12.25 0-20.375-8.375T581.5-114v-65.051q0-4.825 1.75-9.895Q585-194.017 590-199l163-163 26 26-160 160.5v53ZM858.5-257 753-362l62-62q8.5-8.5 20-8.5t20.441 8.441l64.618 65.051Q929-350 929-338.75t-9 20.25L858.5-257ZM389-645q-12.25 0-20.625-8.425-8.375-8.426-8.375-20.5 0-12.075 8.375-20.325T389-702.5h297.5q11.675 0 20.088 8.463Q715-685.574 715-673.825q0 12.325-8.412 20.575Q698.175-645 686.5-645H389Zm0 120q-12.25 0-20.625-8.425-8.375-8.426-8.375-20.5 0-12.075 8.375-20.325T389-582.5h297.5q11.675 0 20.088 8.463Q715-565.574 715-553.825q0 12.325-8.412 20.575Q698.175-525 686.5-525H389Zm135 382.5H182.5 524Z" />
					</g>
				</svg>
			)}
			{...props}
		/>
	);
};
