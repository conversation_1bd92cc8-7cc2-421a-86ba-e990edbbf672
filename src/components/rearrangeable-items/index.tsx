import React, { useState } from "react";
import { Tree, Button, message } from "antd";
import { PlusOutlined, DeleteOutlined } from "@ant-design/icons";
import { BasicDataNode } from "antd/es/tree";
import { AnyObject } from "antd/es/_util/type";

interface DraggableTreeNode extends BasicDataNode {
	title: React.ReactNode;
	key: string;
}

export default function DraggableTree() {
	const [treeData, setTreeData] = useState<DraggableTreeNode[]>([
		{
			title: "Node 1",
			key: "0",
		},
		{
			title: "Node 2",
			key: "1",
		},
	]);

	const handleDragEnd = (info: AnyObject) => {
		const { node, dragNode, dropPosition } = info;
		if (dropPosition === 0) {
			message.error("Cannot drop onto children.");
			return;
		}

		// Log the drag-and-drop event for demonstration
		message.info(
			`Dragged ${dragNode.title} to ${node.title} at position ${dropPosition}`
		);
	};

	const handleAddNode = () => {
		const newNode = {
			title: `New Node ${Math.random()}`,
			key: `${Math.random()}`,
		};

		const newTreeData = [
			...treeData,
			{
				title: newNode.title,
				key: newNode.key,
				children: [],
			},
		];

		setTreeData(newTreeData);
	};

	const handleDeleteNode = (key: string) => {
		const newTreeData = treeData.filter(item => item.key !== key);
		setTreeData(newTreeData);
	};

	return (
		<div>
			<Button
				type="primary"
				onClick={handleAddNode}
				icon={<PlusOutlined />}
				style={{ marginBottom: 16 }}
			>
				Add Node
			</Button>
			<Tree
				draggable
				treeData={treeData}
				onDragEnd={handleDragEnd}
				titleRender={nodeData => (
					<span>
						{nodeData.title}{" "}
						<DeleteOutlined
							onClick={() => handleDeleteNode(nodeData.key)}
							style={{ color: "red", marginLeft: 8 }}
						/>
					</span>
				)}
			/>
		</div>
	);
}
