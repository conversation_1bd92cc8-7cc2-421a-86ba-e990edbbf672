import TestAddClient from "@/client/test-add";
import { AppConfig } from "@/config";
import { Card, Col, Empty, Flex, Row, Space, Spin, Typography } from "antd";
import { useEffect, useMemo, useState } from "react";
import { useNavigate, useParams } from "react-router";
import {
	LineChartOutlined,
	ClockCircleOutlined,
	CheckCircleOutlined,
	PercentageOutlined,
	EditOutlined,
} from "@ant-design/icons";
import { useAppMessage } from "@/hooks/message";
import {
	CreateColumnMetrics,
	CreateLineMetrics,
	CreatePieMetrics,
} from "./charts";

interface Question {
	_id: string;
	type: string;
	score: number;
	index: number;
	successCount: number;
	compileError?: number;
	wrongCount?: number;
	totalAttempts?: number;
	langTotalAttempts?: unknown[];
	numberOfUsersCompleted?: number;
	metaDataFound?: boolean;
}

interface UserData {
	userId: string;
	startTime: string;
	idleTime: number;
	extraTime: number;
	score: number;
	attemptTime: number;
	endTime: string;
}

interface Data {
	_id: string;
	data: {
		questionsObjArr: Question[];
		userData: UserData[];
	};
	name: string;
	quizTime: number;
	isPrivate: boolean;
	cutOffMarks: number;
}

export interface statsResponseDataType {
	arrayOfEmailsAttemptedTest: string[];
	countOfInvitedUserThatAttemptedTest: number;
	invitedUsers: string[];
	totalInvitedUsers: number;
	allowedUsers: string[];
	totalAllowedUsers: number;
	countOfAllowedUserThatAttemptedTest: number;
	data: Data;
	quizId: string;
}

export type LineMetricsDataType = {
	students: number;
	marks?: number;
	minutes?: number;
	type: "marks" | "minutes";
};

export type BarMetricsDataType = {
	type: string;
	questions: number;
	successCount: number;
	compileErrorCount: number;
	wrongCount: number;
};

const testAPI = new TestAddClient(AppConfig.quizServerURL);

const Metrics = () => {
	const { id } = useParams();
	const messageInstance = useAppMessage();
	const navigate = useNavigate();
	const [statData, setStatData] = useState<statsResponseDataType>();
	const [loading, setLoading] = useState<boolean>(false);
	const [noGraphDataAvailable, setNoGraphDataAvailable] =
		useState<boolean>(true);

	const {
		studentVsMarks,
		studentVsminutes,
		studentVsQues,
		invitedPieData,
		allowedPieData,
	}: {
		studentVsMarks: LineMetricsDataType[];
		studentVsminutes: LineMetricsDataType[];
		studentVsQues: BarMetricsDataType[];
		invitedPieData: {
			attempted: number;
			notAttempted: number;
			totalInvitedUsers: number;
		};
		allowedPieData: {
			attempted: number;
			notAttempted: number;
			totalAllowedUsers: number;
		};
	} = useMemo(() => {
		if (!statData) {
			return {
				studentVsMarks: [],
				studentVsminutes: [],
				studentVsQues: [],
				invitedPieData: {
					attempted: 0,
					notAttempted: 0,
					totalInvitedUsers: 0,
				},
				allowedPieData: {
					attempted: 0,
					notAttempted: 0,
					totalAllowedUsers: 0,
				},
			};
		}

		// Process marks distribution
		const marksData = statData.data.data.userData.reduce<
			Record<number, number>
		>((acc, { score }) => {
			acc[score] = (acc[score] || 0) + 1;
			return acc;
		}, {});
		// Process time spent in minutes
		const minutesData = statData.data.data.userData.reduce<
			Record<number, number>
		>((acc, { attemptTime, extraTime, idleTime, startTime, endTime }) => {
			let totalSeconds = 0;
			if (startTime && endTime) {
				totalSeconds = attemptTime + extraTime - idleTime;
			} else if (startTime) {
				totalSeconds = 0;
			}
			acc[totalSeconds] = (acc[totalSeconds] || 0) + 1;
			return acc;
		}, {});

		const questionsData = statData.data.data.questionsObjArr.reduce<
			Record<
				number,
				{
					successCount: number;
					wrongCount: number;
					compileErrorCount: number;
				}
			>
		>((acc, question, index) => {
			acc[index + 1] = {
				successCount: question.successCount || 0,
				wrongCount: question.wrongCount || 0,
				compileErrorCount: question.compileError || 0,
			};
			return acc;
		}, {});
		return {
			studentVsMarks: Object.entries(marksData).map(
				([marks, students]) => ({
					type: "marks" as const,
					marks: Number(marks),
					students,
				})
			),
			studentVsminutes: Object.entries(minutesData).map(
				([minutes, students]) => ({
					type: "minutes" as const,
					minutes: Number(minutes),
					students,
				})
			),
			studentVsQues: Object.entries(questionsData).map(
				([questions, data]) => ({
					type: "Success Count" as const,
					questions: Number(questions),
					successCount: data.successCount,
					wrongCount: data.wrongCount,
					compileErrorCount: data.compileErrorCount,
				})
			),
			invitedPieData: {
				attempted: statData.countOfInvitedUserThatAttemptedTest,
				notAttempted:
					statData.totalInvitedUsers -
					statData.countOfInvitedUserThatAttemptedTest,
				totalInvitedUsers: statData.totalInvitedUsers,
			},
			allowedPieData: {
				attempted: statData.countOfAllowedUserThatAttemptedTest,
				notAttempted:
					statData.totalAllowedUsers -
					statData.countOfAllowedUserThatAttemptedTest,
				totalAllowedUsers: statData.totalAllowedUsers,
			},
		};
	}, [statData]);

	useEffect(() => {
		const noData =
			studentVsMarks.length === 0 &&
			studentVsminutes.length === 0 &&
			studentVsQues.length === 0 &&
			invitedPieData.totalInvitedUsers === 0 &&
			allowedPieData.totalAllowedUsers === 0;

		setNoGraphDataAvailable(noData);
	}, [
		studentVsMarks,
		studentVsminutes,
		studentVsQues,
		invitedPieData.totalInvitedUsers,
		allowedPieData.totalAllowedUsers,
	]);

	useEffect(() => {
		if (!id) return;
		const fetchData = async () => {
			try {
				setLoading(true);
				const response = await testAPI.getTestStats(id);
				if ("error" in response) {
					throw response.error;
				}
				setStatData(response);
			} catch (ex) {
				console.error(ex);
				messageInstance?.error(
					ex instanceof Error ? ex.message : String(ex)
				);
				navigate("/tests");
			} finally {
				setLoading(false);
			}
		};

		fetchData();
	}, [id, messageInstance, navigate]);

	const {
		totalAttempts,
		averageMarks,
		averageTimeSpent,
		totalStudentPass,
		totalPassFailStudents,
	} = useMemo(() => {
		if (!statData) return {};
		const userData = statData.data.data.userData;
		const totalAttempts = userData.length;
		const cutOffMarks = statData.data.cutOffMarks;

		const totalMarks = userData.reduce((sum, { score }) => sum + score, 0);
		const averageMarks = totalAttempts ? totalMarks / totalAttempts : 0;

		const totalTimeSpent = userData.reduce(
			(sum, { attemptTime, extraTime, idleTime }) => {
				return sum + Math.max(0, attemptTime + extraTime - idleTime);
			},
			0
		);

		const averageTimeSpentInMinutes = totalAttempts
			? Math.floor(totalTimeSpent / totalAttempts)
			: 0;

		const hours = Math.floor(averageTimeSpentInMinutes / 60);
		const minutes = averageTimeSpentInMinutes % 60;

		const averageTimeSpent =
			hours > 0
				? `${hours} hour${hours > 1 ? "s" : ""} ${
						minutes > 0
							? `${minutes} min${minutes > 1 ? "s" : ""}`
							: ""
					}`
				: `${minutes} min${minutes > 1 ? "s" : ""}`;

		const totalStudentPass = userData.filter(
			user => user.score >= cutOffMarks
		).length;

		const totalStudentPassPercentage =
			(totalStudentPass / totalAttempts) * 100;
		const totalStudentFailPercentage = 100 - totalStudentPassPercentage;

		const totalPassFailStudents = {
			pass: isNaN(totalStudentPassPercentage)
				? "0.00%"
				: `${totalStudentPassPercentage.toFixed(2)}%`,
			fail: isNaN(totalStudentFailPercentage)
				? "0.00%"
				: `${totalStudentFailPercentage.toFixed(2)}%`,
		};

		return {
			totalAttempts,
			averageMarks: averageMarks.toFixed(2),
			averageTimeSpent,
			totalStudentPass,
			totalPassFailStudents,
		};
	}, [statData]);

	const iconStyle: React.CSSProperties = {
		fontSize: "24px",
		color: "#de6834",
	};
	const headingStyle: React.CSSProperties = {
		color: "#8c8c8c",
		fontSize: "14px",
	};
	const numberStyle: React.CSSProperties = {
		fontSize: "22px",
		fontWeight: "bold",
		color: "#de6834",
	};

	return loading ? (
		<Flex
			align="center"
			justify="center"
			style={{ width: "100%", height: "100%" }}
		>
			<Spin />
		</Flex>
	) : (
		<>
			{noGraphDataAvailable ? (
				<Flex
					align="center"
					justify="center"
					style={{ height: "100%" }}
				>
					<Empty description="No candidates have yet appeared for the test." />
				</Flex>
			) : (
				<Flex
					vertical
					style={{
						margin: "auto",
						padding: "16px",
						height: "100%",
						overflowY: "auto",
						overflowX: "hidden",
						gap: "16px",
					}}
				>
					<Row
						gutter={[16, 16]}
						align="middle"
						justify="space-between"
					>
						<Col flex={1} style={{ minWidth: 200 }}>
							<Card styles={{ body: { padding: "20px" } }}>
								<Flex align="center" justify="center" gap={24}>
									<LineChartOutlined style={iconStyle} />
									<Space
										direction="vertical"
										style={{ rowGap: "0px" }}
									>
										<Typography.Text
											strong
											style={headingStyle}
										>
											Average Marks
										</Typography.Text>
										<Typography.Text style={numberStyle}>
											{averageMarks
												? averageMarks
												: "-NA-"}
										</Typography.Text>
									</Space>
								</Flex>
							</Card>
						</Col>
						<Col flex={1} style={{ minWidth: 200 }}>
							<Card styles={{ body: { padding: "20px" } }}>
								<Flex align="center" justify="center" gap={24}>
									<ClockCircleOutlined style={iconStyle} />
									<Space
										direction="vertical"
										style={{ rowGap: "0px" }}
									>
										<Typography.Text
											strong
											style={headingStyle}
										>
											Average Time
										</Typography.Text>
										<Typography.Text style={numberStyle}>
											{averageTimeSpent
												? averageTimeSpent
												: "-NA-"}
										</Typography.Text>
									</Space>
								</Flex>
							</Card>
						</Col>
						<Col flex={1} style={{ minWidth: 200 }}>
							<Card styles={{ body: { padding: "20px" } }}>
								<Flex align="center" justify="center" gap={24}>
									<CheckCircleOutlined style={iconStyle} />
									<Space
										direction="vertical"
										style={{ rowGap: "0px" }}
									>
										<Typography.Text
											strong
											style={headingStyle}
										>
											No. of Students Pass
										</Typography.Text>
										<Typography.Text style={numberStyle}>
											{totalStudentPass
												? `${totalStudentPass} / ${totalAttempts}`
												: "-NA-"}
										</Typography.Text>
									</Space>
								</Flex>
							</Card>
						</Col>
						<Col flex={1} style={{ minWidth: 200 }}>
							<Card styles={{ body: { padding: "20px" } }}>
								<Flex align="center" justify="center" gap={24}>
									<PercentageOutlined style={iconStyle} />
									<Space
										direction="vertical"
										style={{ rowGap: "0px" }}
									>
										<Typography.Text
											strong
											style={headingStyle}
										>
											Pass
										</Typography.Text>
										<Typography.Text style={numberStyle}>
											{totalPassFailStudents
												? `${totalPassFailStudents.pass}`
												: "-NA-"}
										</Typography.Text>
									</Space>
									<Space
										direction="vertical"
										style={{ rowGap: "0px" }}
									>
										<Typography.Text
											strong
											style={headingStyle}
										>
											/
										</Typography.Text>
										<Typography.Text style={numberStyle}>
											/
										</Typography.Text>
									</Space>
									<Space
										direction="vertical"
										style={{ rowGap: "0px" }}
									>
										<Typography.Text
											strong
											style={headingStyle}
										>
											Fail
										</Typography.Text>
										<Typography.Text style={numberStyle}>
											{totalPassFailStudents
												? `${totalPassFailStudents.fail}`
												: "-NA-"}
										</Typography.Text>
									</Space>
								</Flex>
							</Card>
						</Col>
						<Col flex={1} style={{ minWidth: 200 }}>
							<Card styles={{ body: { padding: "20px" } }}>
								<Flex align="center" justify="center" gap={24}>
									<EditOutlined style={iconStyle} />
									<Space
										direction="vertical"
										style={{ rowGap: "0px" }}
									>
										<Typography.Text
											strong
											style={headingStyle}
										>
											No. of Attempts
										</Typography.Text>
										<Typography.Text style={numberStyle}>
											{totalAttempts
												? totalAttempts
												: "-NA-"}
										</Typography.Text>
									</Space>
								</Flex>
							</Card>
						</Col>
					</Row>

					<Space
						direction="vertical"
						size={"middle"}
						style={{ marginTop: "20px", rowGap: 8 }}
					>
						{((studentVsMarks && studentVsMarks.length > 0) ||
							(studentVsminutes &&
								studentVsminutes.length > 0)) && (
							<Space direction="vertical" size={0}>
								<Typography.Title level={5}>
									Scoring Patterns
								</Typography.Title>
								<Typography.Text>
									Y axis - No. of students
								</Typography.Text>
							</Space>
						)}
						<>
							<Row
								gutter={[16, 16]}
								align={"middle"}
								justify={"center"}
							>
								{studentVsMarks &&
									studentVsMarks.length > 0 && (
										<Col xs={24} sm={12}>
											<Card>
												<CreateLineMetrics
													data={studentVsMarks}
													xField="marks"
													xTitle="Marks Obtained"
													yField="students"
												/>
											</Card>
										</Col>
									)}

								{studentVsminutes &&
									studentVsminutes.length > 0 && (
										<Col xs={24} sm={12}>
											<Card>
												<CreateLineMetrics
													data={studentVsminutes}
													xField="minutes"
													xTitle="Minutes Taken to Complete the Test"
													yField="students"
												/>
											</Card>
										</Col>
									)}
							</Row>
						</>
					</Space>

					<Space
						direction="vertical"
						size={"middle"}
						style={{ marginTop: "20px", rowGap: 0 }}
					>
						{studentVsQues && studentVsQues.length > 0 && (
							<Space direction="vertical" size={0}>
								<Typography.Title level={5}>
									Y axis - No. of students who successfully
									solved the question
								</Typography.Title>
							</Space>
						)}
						<>
							{(studentVsQues ||
								invitedPieData ||
								allowedPieData) && (
								<Row gutter={[16, 16]}>
									{studentVsQues &&
										studentVsQues.length > 0 && (
											<Col xs={24} sm={8}>
												<Card>
													<CreateColumnMetrics
														data={studentVsQues}
														xField="questions"
														xTitle="Questions"
														yField="successCount"
													/>
												</Card>
											</Col>
										)}

									{invitedPieData &&
										invitedPieData.totalInvitedUsers >
											0 && (
											<Col xs={24} sm={8}>
												<Card>
													<CreatePieMetrics
														data={[
															{
																type: "Attempted",
																value: invitedPieData.attempted,
															},
															{
																type: "Not Attempted",
																value: invitedPieData.notAttempted,
															},
														]}
														title="Invited candidates who attempted/not attempted the test"
													/>
												</Card>
											</Col>
										)}

									{allowedPieData &&
										allowedPieData.totalAllowedUsers >
											0 && (
											<Col xs={24} sm={8}>
												<Card>
													<CreatePieMetrics
														data={[
															{
																type: "Attempted",
																value: allowedPieData.attempted,
															},
															{
																type: "Not Attempted",
																value: allowedPieData.notAttempted,
															},
														]}
														title="Allowed candidates who attempted/not attempted the test"
													/>
												</Card>
											</Col>
										)}
								</Row>
							)}
						</>
					</Space>
				</Flex>
			)}
		</>
	);
};

export default Metrics;
