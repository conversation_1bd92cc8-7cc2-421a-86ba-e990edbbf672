// https://ant-design-charts.antgroup.com/examples
import { Column, ColumnConfig, Line, Pie, PieConfig } from "@ant-design/charts";
import { Flex, Typography } from "antd";
import { memo, useMemo } from "react";
import { BarMetricsDataType, LineMetricsDataType } from "./metrics";

export const CreateLineMetrics = memo(
	({
		data,
		xField,
		yField,
		xTitle,
	}: {
		data: LineMetricsDataType[];
		xField: keyof LineMetricsDataType;
		xTitle: string;
		yField: string;
	}) => {
		const xTicks = useMemo(
			() =>
				[
					...new Set(
						data.map((item: LineMetricsDataType) => item[xField])
					),
				].sort((a, b) => Number(a) - Number(b)),
			[data, xField]
		);
		const config = useMemo(
			() => ({
				data: data.map(d => ({
					...d,
					[xField]: String(d[xField]),
				})),
				height: 320,
				xField,
				yField,
				style: {
					lineWidth: 3,
					stroke: "#de6834",
					gradient: "y",
				},
				point: {
					shape: "circle",
					sizeField: 6,
					style: {
						fill: "#de6834",
						stroke: "#de6834",
					},
				},
				legend: false,
				axis: {
					x: {
						line: { style: { stroke: "#939393" } },
						title: xTitle,
						label: {
							formatter: (value: number) => {
								return xTicks.includes(value)
									? value.toString()
									: "";
							},
							style: { fontSize: 10, fill: "#000" },
						},
					},
					y: {
						line: { style: { stroke: "#939393" } },
					},
				},
			}),
			[data, xField, xTitle, yField, xTicks]
		);

		return <Line {...config} />;
	}
);

export const CreatePieMetrics = memo(
	({
		data,
		title,
	}: {
		data: { type: string; value: number }[];
		title: string;
	}) => {
		const config = useMemo<PieConfig>(
			() => ({
				data,
				angleField: "value",
				colorField: "type",
				height: 250,
				legend: false,
				interactions: [{ type: "element-active" }],
				style: {
					padding: 10,
					fill: ({ type }: { type: string }) => {
						if (type === "Attempted") {
							return "#de6834";
						}
						return "#e6b9a1";
					},
				},
				tooltip({ type, value }: { type: string; value: number }) {
					return {
						value: `<div>
              ${value} users
              ${type === "Attempted" ? "have" : "haven't"}
              attempted the test
            </div>`,
						color: type === "Attempted" ? "#de6834" : "#e6b9a1",
					};
				},
			}),
			[data]
		);

		return (
			<Flex align="center" justify="center" gap={24} vertical>
				<Pie {...config} />
				<Typography.Text style={{ fontSize: "0.8rem" }}>
					{title}
				</Typography.Text>
			</Flex>
		);
	}
);

export const CreateColumnMetrics = memo(
	({
		data,
		xField,
		xTitle,
		yField,
	}: {
		data: BarMetricsDataType[];
		xField: string;
		xTitle: string;
		yField: string;
	}) => {
		// const chartRef = useRef<typeof Column | null>(null);
		// const hasBoundClickRef = useRef(false);

		// const handleChartReady = (chart: Chart) => {
		// 	if (hasBoundClickRef.current) return;

		// 	hasBoundClickRef.current = true;
		// 	chart.on("click", (ev: { data?: { data: BarMetricsDataType } }) => {
		// 		const record = ev?.data?.data;
		// 		if (!record) return;
		// 		console.log("Bar clicked:", record);
		// 	});
		// };

		const maxValue = Math.max(
			...data.map(d => d[yField as keyof BarMetricsDataType] as number),
			3
		);
		const tickMax = Math.ceil(maxValue);
		const ticks = Array.from({ length: tickMax + 1 }, (_, i) => i);
		const config = useMemo<ColumnConfig>(() => {
			return {
				data,
				height: 300,
				xField,
				yField,
				legend: false,
				scale: {
					x: { padding: 0.5 },
					y: { domainMin: 0 },
				},
				style: {
					fill: "#de6834",
					maxWidth: 100,
				},
				axis: {
					x: {
						line: { style: { stroke: "#939393" } },
						title: xTitle,
					},
					y: {
						line: { style: { stroke: "#939393" } },
						tickMethod: () => ticks,
						labelFormatter: (val: number) => val.toString(),
					},
				},

				tooltip: true,
				interactions: [
					{ type: "element-active" },
					{ type: "element-selected" },
				],
			};
		}, [data, ticks, xField, xTitle, yField]);

		// return <Column {...config} ref={chartRef} onEvent={handleChartReady} />;
		return <Column {...config} />;
	}
);
