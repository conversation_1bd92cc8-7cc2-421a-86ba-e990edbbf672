import {
	Layout,
	Select,
	Space,
	But<PERSON>,
	Flex,
	theme,
	Typography,
	Collapse,
	Form,
	DatePicker,
	Slider,
	Checkbox,
	Tooltip,
} from "antd";
import React, { useEffect } from "react";
import { FormInstance, useForm } from "antd/es/form/Form";
import dayjs from "dayjs";
import { CloseOutlined, ReloadOutlined } from "@ant-design/icons";
import Tags from "../tags";

const { Header, Content, Footer } = Layout;
const { Title } = Typography;

export interface Option {
	value: number | string;
	label: string;
}

export type FilterMenuItem = { key: string; title: string; value?: unknown } & (
	| {
			type: "collapse";
			title: string;
			options: Option[];
			defaultValue: string[];
			disabled: boolean;
			onChange?: (_key: string, _items: string[]) => void;
	  }
	| {
			type: "select";
			items: Option[];
			value: number;
			onChange?: (_value: number) => void;
	  }
	| {
			type: "dateRange";
			value: [string, string];
	  }
	| {
			type: "slider";
			minValue: number;
			maxMarks: number;
			value: [number, number];
	  }
	| {
			type: "checkbox";
			checked: boolean;
	  }
	| {
			type: "tags";
			placeholder?: string;
			value: string[];
	  }
);

const FilterMenu: React.FC<{
	onChange?: (_items: object) => void;
	onReset?: () => void;
	children?: React.ReactNode;
	componentsData?: FilterMenuItem[];
	onClose?: () => void;
}> = ({ componentsData, onChange, onClose, onReset }) => {
	const [form] = useForm<Record<string, unknown>>();
	const { RangePicker } = DatePicker;
	const {
		token: { colorBgContainer, colorBorder },
	} = theme.useToken();

	useEffect(() => {
		if (!componentsData) return;

		componentsData.forEach(item => {
			switch (item.type) {
				case "collapse":
					form.setFieldValue(item.key, item.defaultValue ?? []);
					break;
				case "select":
					form.setFieldValue(item.key, item.value ?? 0);
					break;
				case "tags":
					form.setFieldValue(item.key, item.value ?? []);
					break;
				case "dateRange":
					try {
						const dateRange = item.value || ["", ""];
						const startDate = dateRange[0]
							? dayjs(dateRange[0])
							: null;
						const endDate = dateRange[1]
							? dayjs(dateRange[1])
							: null;
						const validStartDate =
							startDate && startDate.isValid() ? startDate : null;
						const validEndDate =
							endDate && endDate.isValid() ? endDate : null;

						form.setFieldValue(item.key, [
							validStartDate,
							validEndDate,
						]);
					} catch (error) {
						console.error("Error setting date range:", error);
						form.setFieldValue(item.key, [null, null]);
					}
					break;
				case "slider":
					form.setFieldValue(
						"slider",
						item.value ?? [item.minValue, item.maxMarks]
					);
					break;
				case "checkbox":
					form.setFieldValue(item.key, item.checked ?? false);
					break;
				default:
					break;
			}
		});
	}, [componentsData, form]);

	return (
		<Form
			form={form}
			onFinish={() => onChange?.(form.getFieldsValue())}
			layout="vertical"
			style={{ height: "100%" }}
		>
			<Layout style={{ height: "100%" }}>
				<Header
					style={{
						background: colorBgContainer,
						padding: 0,
						borderBottom: `1px solid ${colorBorder}`,
					}}
				>
					<Flex
						align="center"
						style={{ height: "100%", padding: "1em" }}
						justify="space-between"
					>
						<Title level={4} style={{ marginBottom: 0 }}>
							Filters
						</Title>
						<Button
							type="text"
							icon={<CloseOutlined />}
							onClick={() => onClose?.()}
						></Button>
					</Flex>
				</Header>
				<Content
					style={{
						padding: "2em 1em 1em 1em",
						background: colorBgContainer,
						height: "100%",
						overflow: "auto",
					}}
				>
					<Flex vertical gap={32}>
						{componentsData?.map(item => {
							switch (item.type) {
								case "collapse":
									return (
										<CollapseItem
											key={item.key}
											form={form}
											name={item.key}
											title={item.title}
											disabled={item.disabled}
											onChange={item.onChange}
											options={item.options}
											defaultValue={item.defaultValue}
										/>
									);
								case "select":
									return (
										<>
											<Form.Item
												name={item.key}
												key={item.key}
												label={
													item.title === "dropdown"
														? ""
														: item.title
												}
											>
												<Select
													style={{ width: "100%" }}
													options={item.items}
													defaultValue={item.value}
													onSelect={value =>
														item.onChange?.(value)
													}
													filterOption={(
														input,
														option
													) =>
														(option?.label ?? "")
															.toLowerCase()
															.includes(
																input
																	.trim()
																	.toLowerCase()
															)
													}
												/>
											</Form.Item>
										</>
									);
								case "dateRange":
									return (
										<>
											<Form.Item
												name={item.key}
												label={
													<span>{item.title}</span>
												}
												key={item.key}
											>
												<RangePicker
													allowEmpty={[false, true]}
													defaultValue={[
														item.value[0]
															? dayjs(
																	item
																		.value[0]
																)
															: null,
														item.value[1]
															? dayjs(
																	item
																		.value[1]
																)
															: null,
													]}
													showTime
													format={"YYYY-MM-DD HH:mm"}
												/>
											</Form.Item>
										</>
									);
								case "slider":
									return (
										<Form.Item
											name={item.key}
											label={item.title}
											key={item.key}
										>
											<Slider
												range
												min={item.minValue}
												max={item.maxMarks}
												value={item.value}
												marks={{
													0: "0",
													[item.maxMarks]: `${item.maxMarks}`,
												}}
												disabled={item.maxMarks <= 0}
											/>
										</Form.Item>
									);
								case "checkbox":
									return (
										<Form.Item
											name={item.key}
											key={item.key}
											valuePropName="checked"
										>
											<Checkbox checked={item.checked}>
												{item.title}
											</Checkbox>
										</Form.Item>
									);
								case "tags":
									return (
										<Form.Item
											name={item.key}
											key={item.key}
											label={item.title}
										>
											<Tags
												separator={/[ ,;]+/}
												placeholder={item?.placeholder}
											/>
										</Form.Item>
									);
								default:
									return null;
							}
						})}
					</Flex>
				</Content>
				<Footer
					style={{
						padding: "1em",
						background: colorBgContainer,
					}}
				>
					<Flex justify="space-between">
						<Tooltip title="Reset all filters">
							<Button
								icon={<ReloadOutlined />}
								onClick={() => {
									// First call onReset to update the component data
									if (onReset) {
										onReset();
									} else {
										setTimeout(() => {
											form.resetFields(undefined);
										}, 0);
									}
									// Then reset the form fields
								}}
							>
								Reset
							</Button>
						</Tooltip>
						<Button type="primary" htmlType="submit">
							Apply
						</Button>
					</Flex>
				</Footer>
			</Layout>
		</Form>
	);
};

function CollapseItem(props: {
	form: FormInstance;
	name: string;
	title: string;
	options: Option[];
	disabled?: boolean;
	defaultValue: string[];
	onChange?: (_key: string, _items: string[]) => void;
}) {
	useEffect(() => {
		if (props.defaultValue.length === 0) {
			props.form.setFieldsValue({ [props.name]: props.defaultValue });
		}
		props.form.setFieldsValue({ [props.name]: props.defaultValue });
	}, [props.defaultValue, props.form, props.name]);
	return (
		<>
			<Collapse
				expandIconPosition="end"
				ghost
				collapsible={props.disabled ? "disabled" : "header"}
				defaultActiveKey={["1"]}
				items={[
					{
						styles: {
							header: {
								padding: "0.5rem 0px",
							},
							body: {
								padding: "0.5rem 0px",
							},
						},
						key: "1",
						label: (
							<Space>
								<span>{props.title}</span>
							</Space>
						),
						children: (
							<Flex
								vertical
								style={{
									maxHeight: "350px",
									overflow: "auto",
								}}
							>
								<Form.Item
									name={props.name as string}
									key={props.name as string}
								>
									<Select
										mode="multiple"
										options={props.options}
										placeholder="Search here..."
										allowClear
										defaultValue={props.defaultValue}
										onChange={value => {
											props.onChange?.(
												props.name as string,
												Array.isArray(value)
													? value
													: [value]
											);
										}}
										disabled={props.disabled}
										filterOption={(input, option) =>
											(option?.label ?? "")
												.toLowerCase()
												.includes(
													input.trim().toLowerCase()
												)
										}
									/>
								</Form.Item>
							</Flex>
						),
					},
				]}
			></Collapse>
		</>
	);
}

export default FilterMenu;
