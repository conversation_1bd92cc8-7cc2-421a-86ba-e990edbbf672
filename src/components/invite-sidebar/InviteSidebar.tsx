import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Drawer, Typography } from "antd";
import dayjs, { Dayjs } from "dayjs";
import { Form, Input } from "antd";
import FormItem from "antd/es/form/FormItem";
import Tags from "../tags";
import Quill from "../quill/quill";
import { useEffect, useMemo, useState } from "react";
import { EndPointRequestBody, PostEndPointsMap } from "@/client/test-add";
import { AppConfig } from "@/config";
import { useParams } from "react-router";
import { PlatformOrganisation } from "@/constants";
import { useAppMessage } from "../../hooks/message";
import { validateEmails } from "@/testList/common/common";
import { useTestStore } from "../../store/index";

type FieldType = {
	emailIds: string[];
	expireTime: Dayjs;
	mailContent: string;
	mailSubject: string;
};
type inviteUserDataType = EndPointRequestBody<
	PostEndPointsMap["test/inviteEmails"]
>;

const mailTemplateVariables: {
	[key: string]: { field: string; templateString: string; label: string };
} = {
	name: { field: "displayname", templateString: "name", label: "User Name" },
	testTitle: {
		field: "title",
		templateString: "testTitle",
		label: "Test Title",
	},
	testInviteLink: {
		field: "testInviteLink",
		templateString: "testInviteLink",
		label: "Invite Link",
	},
	testDurationInMins: {
		field: "testDurationInMins",
		templateString: "testDurationInMins",
		label: "Test Duration (mins)",
	},

	...(AppConfig.platformOrganisation === PlatformOrganisation.CHITKARA
		? {
				rollNumber: {
					field: "enrollmentId",
					templateString: "rollNumber",
					label: "Roll Number",
				},
			}
		: {}),
	senderName: {
		field: "senderName",
		templateString: "senderName",
		label: "Sender Name",
	},
	senderEmail: {
		field: "senderEmail",
		templateString: "senderEmail",
		label: "Sender Email",
	},
};

const disabledDate = (current: Dayjs) => {
	return current && current.isBefore(dayjs(), "day");
};

const disabledTime = (current: Dayjs | null) => {
	if (!current) return {};
	const now = dayjs();
	if (current.isSame(now, "day")) {
		return {
			disabledHours: () => [...Array(now.hour()).keys()],
			disabledMinutes: (hour: number) =>
				hour === now.hour() ? [...Array(now.minute()).keys()] : [],
		};
	}
	return {};
};

function InviteSidebar({
	onClose,
	isInviteSideBarOpen,
	userData,
	startTime,
	endTime,
	entryStopTime,
	testTitle,
}: {
	onClose: () => void;
	isInviteSideBarOpen: boolean;
	userData?: string[];
	startTime?: Dayjs | undefined;
	endTime?: Dayjs;
	entryStopTime?: Dayjs;
	testTitle: string;
}) {
	const testStore = useTestStore();
	const messageInstance = useAppMessage();
	const [form] = Form.useForm();
	const [loading, setLoading] = useState(false);
	const { id } = useParams();
	const [mailData, setMailData] = useState("");

	const getQuillValue = useMemo(
		() =>
			(
				startTime: Dayjs,
				endTime: Dayjs | undefined
				// entryStopTime: Dayjs | undefined
			) =>
				`<h2>Your Test Details</h2><p><span>Dear &lt;name&gt;<span>,<br/>${AppConfig.platformOrganisation === PlatformOrganisation.CHITKARA ? `<span>Roll number: &lt;rollNumber&gt;</span>` : ""}<p><b>Your Test Details</b><br/>Test Title: <b>&lt;testTitle&gt;</b><br>${startTime ? `Test start time: ${dayjs(startTime).format("DD MMM YYYY HH:mm a")} IST<br/>` : ""}${endTime ? `Test end time : ${dayjs(endTime).format("DD MMM YYYY HH:mm a")} IST<br/>` : ""}Test Duration: &lt;testDurationInMins&gt;<br/>Test Link: <b>&lt;testInviteLink&gt;</b></p></p><p><b>Best of luck!</b><br/>This link is confidential. Do not share it with anyone.</p><p>Kind regards,<br/>&lt;senderName&gt;<br/>&lt;senderEmail&gt;</p>`,
		[]
	);

	const handleSendInvite = async (values: FieldType) => {
		setLoading(true);
		try {
			if (!id) {
				messageInstance?.error("Test not found");
				return;
			}
			const requestData: inviteUserDataType = {
				mailContent: values?.mailContent.trim(),
				mailSubject: values?.mailSubject.trim(),
				emailIds: values.emailIds.join(","),
				quizId: id,
			};
			if (values.expireTime) {
				requestData.expireTime = values.expireTime.toISOString();
			}
			const response = await testStore.inviteCandidates(id, requestData);
			if (
				response &&
				response.data?.error &&
				response.data?.inviteesEmailsAdded?.length > 0
			) {
				const invalidEmails = values?.emailIds?.filter(
					email => !response.data?.inviteesEmailsAdded.includes(email)
				);
				form.setFieldValue("emailIds", invalidEmails);
			}

			if (response && (response.error || response.data.error)) {
				messageInstance?.error(response.error || response.data.error);
			} else {
				messageInstance?.success("Invitation Sent");
				onClose();
			}
		} catch (error) {
			messageInstance?.error(error as string);
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		const data = getQuillValue(
			startTime ?? dayjs(),
			endTime ?? undefined
			// entryStopTime ?? undefined
		);
		setMailData(data);
	}, [endTime, entryStopTime, getQuillValue, startTime]);

	useEffect(() => {
		if (mailData) {
			form.setFieldsValue({ mailContent: mailData });
		}
	}, [mailData, form]);

	return (
		<>
			<Drawer
				title="Invite Candidates"
				width={500}
				placement="right"
				onClose={() => {
					onClose();
				}}
				open={isInviteSideBarOpen}
				zIndex={1000}
				maskClosable={false}
			>
				<Form
					form={form}
					name="basic"
					layout="vertical"
					style={{
						display: "flex",
						flexDirection: "column",
						gap: "1rem",
					}}
					onFinish={handleSendInvite}
				>
					<Typography.Paragraph>
						Only invited candidates will be allowed to access the
						test
					</Typography.Paragraph>
					<Form.Item<FieldType>
						label="Email(s)"
						name="emailIds"
						required
						rules={[{ validator: validateEmails }]}
						normalize={value => {
							if (typeof value === "string") {
								return value.toLowerCase();
							}
							if (Array.isArray(value)) {
								const unique = new Set<string>();
								for (const item of value)
									unique.add(item.toLowerCase());
								return [...unique];
							}
							return value;
						}}
						initialValue={userData ? userData : []}
					>
						<Tags separator={/[ ,;]+/} max={500} />
					</Form.Item>
					<Form.Item<FieldType>
						label="Subject"
						name="mailSubject"
						rules={[
							{
								required: true,
								message: "Please input your Subject!",
							},
						]}
						initialValue={`${AppConfig.platformOrganisation === PlatformOrganisation.CHITKARA ? `Test Invite from TestPad | Chitkara University | << ${testTitle} >>` : `Test Invite from CodeQuotient << ${testTitle} >>`}`}
					>
						<Input />
					</Form.Item>
					<Form.Item<FieldType>
						style={{ margin: "10px 0px" }}
						label="Test Link Expiry time"
						name="expireTime"
						rules={[
							{
								validator: (_rule, value) => {
									if (
										value &&
										value.valueOf() <= dayjs().valueOf()
									) {
										return Promise.reject(
											"Expiry time cannot be less than current time"
										);
									}
									return Promise.resolve();
								},
							},
						]}
					>
						<DatePicker
							format={"YYYY-MM-DD HH:mm"}
							showTime
							showNow={false}
							placeholder="Select Time"
							disabledDate={disabledDate}
							disabledTime={disabledTime}
							style={{ width: "100%" }}
						/>
					</Form.Item>
					<Form.Item<FieldType> name="mailContent">
						<Quill
							templateVariable={mailTemplateVariables}
							value={mailData}
						/>
					</Form.Item>
					<FormItem>
						<Button
							type="primary"
							htmlType="submit"
							loading={loading}
						>
							Send Invitation
						</Button>
					</FormItem>
				</Form>
			</Drawer>
		</>
	);
}

export default InviteSidebar;
