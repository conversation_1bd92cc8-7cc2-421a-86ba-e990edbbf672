import { AppConfig } from "@/config";
import { useAppMessage } from "../../hooks/message";
import { PaperClipOutlined } from "@ant-design/icons";
import {
	Button,
	Modal,
	Space,
	TableColumnsType,
	Tooltip,
	Typography,
	Upload,
	UploadProps,
} from "antd";
import { RcFile } from "antd/es/upload";
import { useState } from "react";
import { Link } from "react-router";
import * as XLSX from "xlsx-js-style";
import { AnyObject } from "antd/es/_util/type";
import { BulkRoomReqDataType } from "@/client/test-add";
import { quizClient } from "../../store";
import InValidBulkUpload from "../bulk-error-modal/InValidBulkUpload";
import { roomTitleRegex, userDataType } from "../test-rooms/rooms";

interface roomUploadModalProps {
	testId?: string;
	open: boolean;
	students?: userDataType[];
	invigilators?: userDataType[];
	toggle: (_value: boolean) => void;
	onInit?: () => void;
	onFinish?: (_error?: unknown) => void;
}

interface ParsedRoom {
	roomName: string;
	rollNumber: string;
	email: string;
	isInvigilator: string;
}

const columns: TableColumnsType<AnyObject> = [
	{
		key: "Serial Number",
		width: 50,
		align: "right",
		render: (_value, _record, index) => `${index + 1}.`,
	},
	{
		key: "roomName",
		title: "Room Name",
		dataIndex: "roomName",
		render: value => (
			<Typography.Text ellipsis>
				<Tooltip title={value}>{value}</Tooltip>
			</Typography.Text>
		),
	},
	{
		key: "emails",
		title: "Emails",
		dataIndex: "emails",
		render: value => (
			<Typography.Text ellipsis>
				<Tooltip title={value}>{value}</Tooltip>
			</Typography.Text>
		),
	},
	{
		key: "Error",
		title: "Error",
		dataIndex: "error",
		render: value => (
			<Typography.Text ellipsis>
				<Tooltip title={value}>{value}</Tooltip>
			</Typography.Text>
		),
	},
];

const AddBulkRoomModal = (props: roomUploadModalProps) => {
	const {
		testId,
		students,
		invigilators,
		open,
		toggle: setOpen,
		onInit,
		onFinish,
	} = props;
	const messageInstance = useAppMessage();
	const [fileName, setFileName] = useState<string>("");
	const [loading, setLoading] = useState(false);
	const [dataToUpload, setDataToUpload] = useState<BulkRoomReqDataType[]>([]);
	const [failedToUpload, setFailedDataToUpload] = useState<AnyObject[]>([]);
	const [openFailedModal, setOpenFailedModal] = useState(false);

	const readExcel = async (file: RcFile): Promise<boolean> => {
		const reader = new FileReader();

		return new Promise(resolve => {
			reader.onload = ev => {
				const data = ev.target?.result;
				if (!data) return resolve(false);

				const workbook = XLSX.read(data, { type: "buffer" });
				const sheetName = workbook.SheetNames[0];
				const worksheet = workbook.Sheets[sheetName];
				const jsonData = XLSX.utils.sheet_to_json<{
					[key: string]: unknown;
				}>(worksheet);

				if (!jsonData.length) return resolve(false);
				const validData: BulkRoomReqDataType[] = [];
				const errors: AnyObject[] = [];
				const allEmails: string[] = [
					...(students?.map(student => student.email) ?? []),
					...(invigilators?.map(invigilator => invigilator.email) ??
						[]),
				];
				jsonData.forEach(value => {
					try {
						const parsedObj: ParsedRoom = {
							roomName:
								value["Room Name"]?.toString()?.trim() ?? "",
							email: value["Email Id"]?.toString()?.trim() ?? "",
							rollNumber:
								value["Roll Number"]?.toString()?.trim() ?? "",
							isInvigilator:
								value["Is Invigilator (Yes / No)"]
									?.toString()
									?.trim() ?? "",
						};

						if (!parsedObj.roomName) {
							throw new Error("Room Name is missing");
						}

						if (roomTitleRegex.test(parsedObj.roomName)) {
							throw new Error("Room name is invalid!");
						}
						if (!parsedObj.email) {
							throw new Error("Email Id is missing");
						}
						if (!parsedObj.isInvigilator) {
							throw new Error(
								"Is Invigilator (Yes / No) is missing"
							);
						}

						const roomKey = `${parsedObj.roomName}-${testId}`;
						let roomData = dataToUpload.find(
							room => room.roomName === roomKey
						);

						if (!roomData) {
							roomData = {
								roomName: roomKey,
								rollNumbers: [],
								userEmails: [],
								invigilatorEmails: [],
							};
							dataToUpload.push(roomData);
						}

						if (allEmails.includes(parsedObj.email)) {
							const existingEntry = failedToUpload.find(
								entry =>
									entry["Room Name"] === parsedObj.roomName
							);

							if (existingEntry) {
								if (!existingEntry.emails)
									existingEntry.emails = [];
								existingEntry.emails.push(parsedObj.email);
							} else {
								failedToUpload.push({
									roomName: parsedObj.roomName,
									emails: [parsedObj.email],
									error: "Duplicate email Id",
								});
							}
						} else {
							allEmails.push(parsedObj.email);
							roomData.rollNumbers.push(parsedObj.rollNumber);

							if (parsedObj.isInvigilator === "No") {
								roomData.userEmails.push(parsedObj.email);
							} else if (parsedObj.isInvigilator === "Yes") {
								roomData.invigilatorEmails.push(
									parsedObj.email
								);
							}

							if (!validData.includes(roomData)) {
								validData.push(roomData);
							}
						}
					} catch (ex) {
						errors.push({
							roomName: value["Room Name"],
							emails: value["Email Id"],
							error: (ex as Error).message,
						});
					}
				});

				setDataToUpload(validData);
				setFailedDataToUpload(prev => [...prev, ...errors]);

				resolve(true);
			};

			reader.readAsArrayBuffer(file);
		});
	};

	const handleFileChange = async (file: RcFile) => {
		setFileName("");
		const isExcel =
			file.type === "application/vnd.ms-excel" ||
			file.type ===
				"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";

		if (!isExcel) {
			messageInstance?.error(
				"You can only upload Excel files (.xls or .xlsx)!"
			);
			return Upload.LIST_IGNORE;
		}
		setFileName(file.name);

		const fileSuccessfullyRead = await readExcel(file);

		if (!fileSuccessfullyRead) {
			messageInstance?.error("No data found in the file");
			setFailedDataToUpload([]);
			setFileName("");
			setDataToUpload([]);
			return Upload.LIST_IGNORE;
		}
		messageInstance?.success("File processed successfully");

		return false;
	};

	const uploadProps: UploadProps = {
		showUploadList: {
			showRemoveIcon: true,
		},
		beforeUpload: handleFileChange,
		onRemove: () => {
			setFailedDataToUpload([]);
			setFileName("");
			setDataToUpload([]);
		},
		maxCount: 1,
		accept: ".xls, .xlsx",
	};

	function cancelModal() {
		setOpen(false);
		setFileName("");
		setDataToUpload([]);
	}

	function closeFailedDataModal() {
		cancelModal();
		setFailedDataToUpload([]);
		setOpenFailedModal(false);
	}

	const submitUplaodFile = async () => {
		if (!dataToUpload || !testId) return;
		setLoading(true);
		onInit?.();
		try {
			const response = await quizClient.uploadBulkRoom(
				testId,
				dataToUpload
			);
			if (response.length > 0) {
				const errors: AnyObject[] = [];
				response.forEach(value => {
					value.invalidInvigilatorEmails?.forEach(email => {
						errors.push({
							roomName: value.roomName,
							emails: email,
							error: value.error,
						});
					});
					value.invalidUserEmails?.forEach(email => {
						errors.push({
							roomName: value.roomName,
							emails: email,
							error: value.error,
						});
					});
				});
				setFailedDataToUpload(prev => [...prev, ...errors]);
				setOpenFailedModal(true);
			}
			messageInstance?.success("File uploaded successfully");
			onFinish?.();
			cancelModal();
		} catch (error) {
			console.log(error);
			onFinish?.(error);
		} finally {
			setLoading(false);
			if (failedToUpload.length > 0) {
				setOpenFailedModal(true);
			}
		}
	};

	return (
		<div>
			<Modal
				title="Add Rooms"
				open={open}
				onCancel={() => cancelModal()}
				footer={[
					<Button
						key="submit"
						type="primary"
						loading={loading}
						disabled={!fileName}
						onClick={submitUplaodFile}
					>
						Upload
					</Button>,
				]}
				destroyOnClose
			>
				<Space direction="vertical">
					<Upload {...uploadProps}>
						<Button icon={<PaperClipOutlined />} iconPosition="end">
							Choose File
						</Button>
					</Upload>
					<Typography.Text style={{ fontSize: "0.8rem" }}>
						Choose a file to import rooms. Supported extensions are
						(.xls, .xlsx).
						<br />
						Download{" "}
						<Link
							to={`${AppConfig.quizServerURL}/template_files/bulk-upload-rooms.xlsx`}
							target="_blank"
						>
							"Rooms template"
						</Link>
						.
					</Typography.Text>
				</Space>
			</Modal>
			{openFailedModal && failedToUpload.length > 0 && (
				<InValidBulkUpload
					data={failedToUpload}
					clearFailedData={closeFailedDataModal}
					columns={columns}
					title="Following rooms have not been created"
					description=""
				/>
			)}
		</div>
	);
};

export default AddBulkRoomModal;
