import { useState } from "react";
import {
	Mo<PERSON>,
	Button,
	Upload,
	Typography,
	message,
	Space,
	TableColumnsType,
} from "antd";
import { PaperClipOutlined } from "@ant-design/icons";
import { Link } from "react-router";
import { RcFile, UploadProps } from "antd/es/upload";
import * as XLSX from "xlsx-js-style";
import { AppConfig } from "@/config";
import InValidBulkUpload from "../bulk-error-modal/InValidBulkUpload";
import { AnyObject } from "antd/es/_util/type";
import { quizClient } from "../../store";
import { PlatformOrganisation } from "@/constants";
interface McqUploadModalProps {
	testId?: string;
	sectionIndex?: number;
	open: boolean;
	toggle: (_value: boolean) => void;
	onInit?: () => void;
	onFinish?: (error?: unknown) => void;
}

interface ParsedMCQ {
	Title: string;
	levelOfDifficulty: string;
	Score: string;
	questionDescription: string;
	answerChoice1: string;
	answerChoice2: string;
	answerChoice3: string;
	answerChoice4: string;
	correctAnswerChoice: string;
	courseOutcomes?: string;
	bloomTaxonomy?: string;
	Topic?: string;
	Tags?: string;
}

const questionDifficultyLevel: Record<string, string> = {
	Easy: "0",
	Medium: "1",
	Hard: "2",
};

const validBloomsTaxonomy = {
	"L1 - Remembering": "1",
	"L2 - Understanding": "2",
	"L3 - Applying": "3",
	"L4 - Analyzing": "4",
	"L5 - Evaluating": "5",
	"L6 - Creating": "6",
};

const columns: TableColumnsType<AnyObject> = [
	{
		key: "Serial Number",
		width: 60,
		align: "right",
		render: (_value, _record, index) => `${index + 1}.`,
	},
	{
		key: "Title",
		title: "Title",
		dataIndex: "Title",
		render: value => (
			<Typography.Text ellipsis>
				{value ? value : "EMPTY"}
			</Typography.Text>
		),
	},
	{
		key: "Error",
		title: "Error",
		dataIndex: "Error",
		render: value => (
			<Typography.Text ellipsis>
				{value ? value : "EMPTY"}
			</Typography.Text>
		),
	},
];

function AddBulkQuestionModal(props: McqUploadModalProps) {
	const {
		open,
		testId,
		sectionIndex,
		toggle: setOpen,
		onInit,
		onFinish,
	} = props;

	const [loading, setLoading] = useState(false);
	const [fileName, setFileName] = useState<string>("");
	const [dataToUpload, setDataToUpload] = useState<{ finalData: object }>();
	const [unprocessedData, setUnprocessedData] = useState<unknown[]>([]);
	const [openFailedModal, setOpenFailedModal] = useState(false);
	const [failedData, setFailedData] = useState<AnyObject[]>([]);
	const [messageApi, contextHolder] = message.useMessage({ maxCount: 1 });

	const readExcel = async (file: RcFile): Promise<boolean> => {
		const reader = new FileReader();

		return new Promise(resolve => {
			reader.onload = ev => {
				const data = ev.target?.result;
				if (!data) return resolve(false);

				const workbook = XLSX.read(data, {
					type: "buffer",
					dense: true,
				});
				const sheetName = workbook.SheetNames[0];
				const worksheet = workbook.Sheets[sheetName];
				const jsonData = XLSX.utils.sheet_to_json<{
					[key: string]: unknown;
				}>(worksheet);

				if (!jsonData.length) return resolve(false);

				setUnprocessedData(jsonData);

				const validData: object[] = [];
				const errors: AnyObject[] = [];

				jsonData.forEach((value, index) => {
					try {
						const parsedObj: ParsedMCQ = {
							Title: value["Title"]?.toString()?.trim() || "",
							levelOfDifficulty:
								value["Level of Difficulty"]
									?.toString()
									?.trim() || "",
							Score: value["Score"]?.toString()?.trim() || "",
							questionDescription:
								value["Question Description"]
									?.toString()
									?.trim() || "",
							answerChoice1:
								value["Answer Choice 1"]?.toString()?.trim() ||
								"",
							answerChoice2:
								value["Answer Choice 2"]?.toString()?.trim() ||
								"",
							answerChoice3:
								value["Answer Choice 3"]?.toString()?.trim() ||
								"",
							answerChoice4:
								value["Answer Choice 4"]?.toString()?.trim() ||
								"",
							correctAnswerChoice:
								value["Correct Answer Choice"]
									?.toString()
									?.trim() || "",
							courseOutcomes: value["Course Outcomes (COs)"]
								?.toString()
								?.trim(),
							bloomTaxonomy: value["Bloom's Taxonomy"]
								?.toString()
								?.trim(),
							Topic: value["Topic"]?.toString()?.trim(),
							Tags: value["Tags"]?.toString()?.trim(),
						};

						if (!parsedObj.Title) throw new Error("Title Missing");
						if (!parsedObj.levelOfDifficulty)
							throw new Error("Level Of Difficulty Missing");
						if (isNaN(parseInt(parsedObj.Score)))
							throw new Error("Score is Not Valid");
						if (!parsedObj.questionDescription)
							throw new Error("Question Description Missing");
						if (!parsedObj.answerChoice1)
							throw new Error("Answer Choice 1 Missing");
						if (!parsedObj.answerChoice2)
							throw new Error("Answer Choice 2 Missing");
						if (!parsedObj.answerChoice3)
							throw new Error("Answer Choice 3 Missing");
						if (!parsedObj.answerChoice4)
							throw new Error("Answer Choice 4 Missing");
						if (!parsedObj.correctAnswerChoice)
							throw new Error("Correct Answer Choice Missing");

						if (
							AppConfig.platformOrganisation ===
							PlatformOrganisation.CHITKARA
						) {
							if (!parsedObj.courseOutcomes)
								throw new Error("Course outcome not provided");
							if (!parsedObj.bloomTaxonomy) {
								throw new Error(
									"Bloom's Taxonomy not provided"
								);
							}
						}

						if (parsedObj.bloomTaxonomy) {
							if (
								!Object.keys(validBloomsTaxonomy).includes(
									parsedObj.bloomTaxonomy
								)
							) {
								throw new Error(`Not valid Bloom's Taxonomy`);
							}
						}

						const obj = {
							index,
							difficultyLevel:
								questionDifficultyLevel[
									parsedObj.levelOfDifficulty
								],
							score: parseInt(parsedObj.Score),
							txtQues: parsedObj.questionDescription,
							txtQuesTitle: parsedObj.Title,
							txttag: parsedObj.Tags
								? parsedObj.Tags.split(",")
										.map(tag =>
											tag
												.trim()
												.toLowerCase()
												.replaceAll(" ", "-")
										)
										.join(",")
								: "",
							type: "1",
							negativeScore: "",
							courseOutcomes: parsedObj.courseOutcomes,
							bloomTaxonomy: parsedObj.bloomTaxonomy
								? validBloomsTaxonomy[
										parsedObj.bloomTaxonomy as keyof typeof validBloomsTaxonomy
									]
								: undefined,
							topic: parsedObj.Topic,
							txtOpt1: parsedObj.answerChoice1,
							txtOpt2: parsedObj.answerChoice2,
							txtOpt3: parsedObj.answerChoice3,
							txtOpt4: parsedObj.answerChoice4,
							[`chkOpt${parsedObj.correctAnswerChoice}`]: "on",
						};

						validData.push(obj);
					} catch (error) {
						errors.push({
							...value,
							Error: (error as Error).message,
						});
					}
				});

				setDataToUpload({ finalData: validData });
				setUnprocessedData(jsonData);
				setFailedData(errors);

				if (validData.length) {
					messageApi.success("File processed successfully");
				}

				resolve(true);
			};

			reader.readAsArrayBuffer(file);
		});
	};

	const handleFileChange = async (file: RcFile) => {
		setFileName("");
		const isExcel =
			file.type === "application/vnd.ms-excel" ||
			file.type ===
				"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";

		if (!isExcel) {
			messageApi.error(
				"You can only upload Excel files (.xls or .xlsx)!"
			);
			return Upload.LIST_IGNORE;
		}
		setFileName(file.name);

		const success = await readExcel(file);

		if (!success) {
			messageApi.error("No data found in the file");
			setFailedData([]);
			setUnprocessedData([]);
			setFileName("");
			setDataToUpload({ finalData: {} });
			return Upload.LIST_IGNORE;
		}

		return false;
	};

	const submitUplaodFile = async () => {
		if (!dataToUpload) return;
		setLoading(true);
		onInit?.();
		try {
			const response = await quizClient.bulkUploadMCQ(
				dataToUpload.finalData,
				testId,
				sectionIndex
			);
			if (response.failed.length > 0) {
				response?.failed.forEach(single_question => {
					const dataToPush = (unprocessedData as AnyObject[])[
						single_question.index as number
					];
					dataToPush["Error"] = single_question.error;
					failedData.push(dataToPush);
				});
			}
			messageApi.success("File uploaded successfully");
			onFinish?.();
			setOpen(false);
			setUnprocessedData([]);
			setFileName("");
			setDataToUpload({ finalData: {} });
			if (failedData.length) {
				setOpenFailedModal(true);
			}
		} catch (error) {
			console.log(error);
			messageApi.error(
				error instanceof Error ? error.message : String(error)
			);
			onFinish?.(error);
		} finally {
			setLoading(false);
		}
	};

	function closeFailedDataModal() {
		cancelModal();
	}

	const uploadProps: UploadProps = {
		showUploadList: {
			showRemoveIcon: true,
		},
		beforeUpload: handleFileChange,
		onRemove: () => {
			setFailedData([]);
			setUnprocessedData([]);
			setFileName("");
			setDataToUpload({ finalData: {} });
		},
		maxCount: 1,
		accept: ".xls, .xlsx",
	};

	const cancelModal = () => {
		setOpen(false);
		setOpenFailedModal(false);
		setFailedData([]);
		setUnprocessedData([]);
		setFileName("");
		setDataToUpload({ finalData: {} });
	};
	return (
		<>
			{contextHolder}
			<Modal
				title="Add MCQ Questions"
				open={open}
				onCancel={() => cancelModal()}
				footer={[
					<Button
						key="submit"
						type="primary"
						loading={loading}
						disabled={!fileName}
						onClick={submitUplaodFile}
					>
						Upload
					</Button>,
				]}
				destroyOnClose
			>
				<Space direction="vertical">
					<Upload {...uploadProps}>
						<Button icon={<PaperClipOutlined />} iconPosition="end">
							Choose File
						</Button>
					</Upload>
					<Typography.Text style={{ fontSize: "0.8rem" }}>
						Choose a file to import MCQ questions. Supported
						extensions are (.xls, .xlsx).
						<br />
						<Link
							to={`${AppConfig.quizServerURL}/template_files/${AppConfig.platformOrganisation}/bulk-upload-mcq.xlsx`}
							target="_blank"
						>
							Download Bulk MCQ template
						</Link>
					</Typography.Text>
				</Space>
			</Modal>
			{openFailedModal && failedData.length > 0 && (
				<InValidBulkUpload
					data={failedData}
					clearFailedData={closeFailedDataModal}
					columns={columns}
					title="Following questions have not been created"
					description=""
				/>
			)}
		</>
	);
}

export default AddBulkQuestionModal;
