import { Flex, Statistic, Tooltip } from "antd";
import { InfoCircleFilled } from "@ant-design/icons";
import { Link } from "react-router";

interface InfoCardProps {
	title: string;
	value: number;
	toolTipTitle: string;
	link?: string;
}
const InfoCard = ({ title, value, toolTipTitle, link }: InfoCardProps) => {
	return (
		<Statistic
			title={
				<Flex justify="space-between">
					<span>{title}</span>{" "}
					<Tooltip placement="top" title={toolTipTitle}>
						{" "}
						<InfoCircleFilled />
					</Tooltip>
				</Flex>
			}
			valueStyle={{
				color: "#de6834",
				fontWeight: "600",
				fontSize: "32px",
			}}
			style={{
				background: "#f0f0f0",
				padding: "1rem 0.75rem",
				width: "100%",
				borderRadius: "1rem",
			}}
			valueRender={() =>
				link && value > 0 ? (
					<Link
						to={link}
						style={{
							display: "inline-block",
							width: "100%",
							textDecoration: "none",
						}}
					>
						{value}
					</Link>
				) : (
					value
				)
			}
		/>
	);
};

export default InfoCard;
