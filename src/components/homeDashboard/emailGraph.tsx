import { Line } from "@ant-design/plots";
import { useEffect, useState } from "react";
import { loginClient } from "../../store/index";
import { Flex, Typography } from "antd";

const { Text } = Typography;
interface EmailGraphType {
	_id: string;
	count: number;
}
function transformData(input: EmailGraphType[]) {
	return input.map(item => ({
		date: item._id.split("T")[0],
		count: item.count,
	}));
}
const EmailActivityChart = () => {
	const [emailData, setEmailData] = useState<EmailGraphType[]>([]);

	useEffect(() => {
		getData();
	}, []);

	const getData = async () => {
		try {
			const res = await loginClient.getActiveEmails();
			setEmailData(res || []);
		} catch (error) {
			console.error(error);
		}
	};
	const data = emailData.length > 0 ? transformData(emailData) : [];
	const config = {
		data,
		height: 120,
		xField: "date",
		yField: "count",
		style: {
			lineWidth: 3,
			stroke: "#de6834",
			gradient: "y",
		},
		point: {
			shape: "circle",
			sizeField: 6,
			style: {
				fill: "#de6834",
				stroke: "#de6834",
			},
		},
		legend: false,
		axis: {
			x: null,
			y: null,
		},
		interaction: {
			tooltip: {
				crosshairs: false,
			},
		},
	};

	return (
		<>
			{emailData.length === 0 ? (
				<Flex
					style={{
						minHeight: 120,
						justifyContent: "center",
						alignItems: "center",
					}}
				>
					<Text>No activity yet</Text>
				</Flex>
			) : (
				<Line {...config} />
			)}
		</>
	);
};

export default EmailActivityChart;
