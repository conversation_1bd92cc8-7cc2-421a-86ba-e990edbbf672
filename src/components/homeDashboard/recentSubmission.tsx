import { <PERSON><PERSON>, <PERSON>, <PERSON>lex, Tag, Tooltip, Typography } from "antd";
import { useNavigate } from "react-router";

interface SubmissionType {
	quizId: string;
	quizName: string;
	userId: string;
	userName: string;
	score: number;
	submitTime: number;
	quizCreatedBy: string;
	creatorName: string;
	cutOffMarks?: number;
}

const { Text, Title, Link } = Typography;

const formatTimestamp = (timestamp: number): string => {
	const date = new Date(timestamp);

	const options: Intl.DateTimeFormatOptions = {
		month: "short",
		day: "numeric",
		year: "numeric",
		hour: "2-digit",
		minute: "2-digit",
		hour12: true,
	};

	return date.toLocaleString("en-US", options);
};

const RecentSubmission = ({
	recentSubmission,
}: {
	recentSubmission: SubmissionType[];
}) => {
	const navigate = useNavigate();
	return (
		<>
			{recentSubmission.length > 0 ? (
				<>
					{recentSubmission.map(record => (
						<Card
							style={{
								margin: "8px 0px",
							}}
						>
							<Flex justify="space-between">
								<Flex
									justify="center"
									align="center"
									gap="middle"
								>
									<Tag
										bordered={false}
										color={
											!record?.cutOffMarks ||
											record?.score >= record?.cutOffMarks
												? "green"
												: "red"
										}
										style={{
											padding: "0.6em 1.25em",
											fontSize: 20,
										}}
									>
										{!record?.cutOffMarks ||
										record?.score >= record?.cutOffMarks
											? "Pass"
											: "Fail"}
									</Tag>
									<Flex vertical align="flex-start">
										<Tooltip title="Report">
											<Link
												href={`tests/${record.quizId}/user-report/${record.userId}`}
											>
												<Title
													level={4}
													style={{ margin: 0 }}
												>
													{record.userName}
												</Title>
											</Link>
										</Tooltip>
										<Text style={{ color: "#de6834" }}>
											<Link
												href={`tests/${record.quizId}/report`}
												target="_blank"
											>
												{record?.quizName}
											</Link>
											<Text type="secondary">
												{" "}
												Submitted on{" "}
												{formatTimestamp(
													record?.submitTime
												)}
											</Text>
										</Text>
									</Flex>
								</Flex>
								<Flex align="center" gap="middle">
									<Title
										level={5}
										style={{ margin: 0, color: "#de6834" }}
									>
										Score: {record?.score}
									</Title>

									<Tooltip title="Report">
										<Button
											type="default"
											onClick={() =>
												navigate(
													`tests/${record.quizId}/user-report/${record.userId}`
												)
											}
										>
											View
										</Button>
									</Tooltip>
								</Flex>
							</Flex>
						</Card>
					))}
				</>
			) : (
				<Title level={5} style={{ textAlign: "center" }}>
					No recent submission
				</Title>
			)}
		</>
	);
};

export default RecentSubmission;
