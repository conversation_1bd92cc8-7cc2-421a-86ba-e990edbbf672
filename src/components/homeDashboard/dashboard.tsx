import {
	Flex,
	Input,
	Layout,
	message,
	Tabs,
	TabsProps,
	Tooltip,
	Typography,
} from "antd";
import ActiveTest from "./activeTest";
import RecentSubmission from "./recentSubmission";
import EmailActivityChart from "./emailGraph";
import InfoCard from "./InfoCard";
import RecentSubmissionChart from "./recentSumGraph";
import Sider from "antd/es/layout/Sider";
import { Content, Header } from "antd/es/layout/layout";
import { useEffect, useState } from "react";
import { loginClient, quizClient, useAppStore } from "../../store/index";
import { AppConfig } from "@/config";
import { SearchOutlined } from "@ant-design/icons";
import { RoleAction, RoleResource } from "@/constants/roles";
import PageHelmet from "../page-helmet/PageHelmet";
interface SubmissionType {
	quizId: string;
	quizName: string;
	userId: string;
	userName: string;
	score: number;
	submitTime: number;
	quizCreatedBy: string;
	creatorName: string;
	cutOffMarks?: number;
}

interface ActiveTestDataType {
	_id: string;
	title: string;
	quizTime: number;
	quizCode?: number;
	createdBy: string;
	parentIdOfCreator?: string;
	startTime: string;
	userIds: string[];
	studentsCount: number;
}
const { Title } = Typography;

const AdminDashboard = () => {
	const [messageApi, contextHolder] = message.useMessage({
		maxCount: 1,
	});
	const hasResourcePermission = useAppStore(
		state => state.hasResourcePermission
	);
	const [recentSubmission, setRecentSubmission] = useState<SubmissionType[]>(
		[]
	);
	const [activeTest, setActiveTest] = useState<ActiveTestDataType[]>([]);

	const [emailCount, setEmailCount] = useState<number>(0);
	const [activeUserCount, setActiveUserCount] = useState<number>(0);
	const [searchVal, setSearchVal] = useState<string>("");
	const user = useAppStore(store => store.user);
	const items: TabsProps["items"] = [
		...(recentSubmission.length > 0
			? [
					{
						key: "1",
						label: "Recent submissions",
						children: (
							<RecentSubmission
								recentSubmission={
									searchVal.trim() === ""
										? recentSubmission
										: recentSubmission.filter(data =>
												data.userName
													.trim()
													.toLowerCase()
													.includes(
														searchVal
															.trim()
															.toLowerCase()
													)
											)
								}
							/>
						),
						style: {
							height: "100%",
							overflow: "auto",
							padding: 16,
						},
					},
				]
			: []),
		{
			key: "2",
			label: "Active tests",
			children: (
				<ActiveTest
					activeTest={
						searchVal.trim() === ""
							? activeTest
							: activeTest.filter(data =>
									data.title
										?.trim()
										.toLowerCase()
										.includes(
											searchVal.trim().toLowerCase()
										)
								)
					}
				/>
			),
			style: { height: "100%", overflow: "auto", padding: 16 },
		},
	];
	useEffect(() => {
		getData();
		getEmailCountData();
		getActiveTestData();
	}, []);

	async function getEmailCountData() {
		try {
			const currentCount = await loginClient.getEmailSentCount();
			setEmailCount(currentCount ?? 0);
		} catch (error) {
			console.error(error);
			messageApi.error(String(error));
		}
	}

	const getData = async () => {
		try {
			const res = await quizClient.getRecentSubmissionsList();
			const submissions = res?.submissionArr || [];

			// Only latest 10 unique days are considered.
			// Within that, maximum 100 submissions are included.

			const submissionsByDate = new Map<string, SubmissionType[]>();

			submissions.forEach(submission => {
				const dateKey = new Date(submission.submitTime)
					.toISOString()
					.split("T")[0];
				if (!submissionsByDate.has(dateKey)) {
					submissionsByDate.set(dateKey, []);
				}
				submissionsByDate.get(dateKey)!.push(submission);
			});

			const top10Dates = Array.from(submissionsByDate.keys())
				.sort((a, b) => new Date(b).getTime() - new Date(a).getTime())
				.slice(0, 10);

			const allSubmissions = top10Dates.flatMap(
				date => submissionsByDate.get(date)!
			);

			const limitedSubmissions = allSubmissions.slice(0, 100);

			setRecentSubmission(limitedSubmissions);
		} catch (error) {
			console.error(error);
			messageApi.error(String(error));
		}
	};

	const getActiveTestData = async () => {
		try {
			const res = await quizClient.getActiveTest();
			setActiveTest(res.dashboardData || []);
			setActiveUserCount(res.activeUsersCount);
		} catch (error) {
			console.error(error);
			messageApi.error(String(error));
		}
	};

	const tabsBarExtra = (
		<Input
			type="search"
			value={searchVal}
			placeholder="search"
			suffix={
				<Tooltip title="Extra information">
					<SearchOutlined style={{ color: "rgba(0,0,0,.45)" }} />
				</Tooltip>
			}
			onChange={e => setSearchVal(e.target.value)}
		/>
	);
	return (
		<>
			<PageHelmet title="Dashboard" />
			{contextHolder}
			<Layout style={{ height: "100%", overflow: "hidden" }}>
				<Content
					style={{
						height: "100%",
					}}
				>
					<Layout
						style={{
							height: "100%",
							overflow: "hidden",
							backgroundColor: "#fbfbfb",
						}}
					>
						<Header
							style={{
								backgroundColor: "transparent",
								padding: 20,
							}}
						>
							<Title level={3}>
								Welcome {user?.displayName}😊
							</Title>
						</Header>
						<Content
							style={{
								padding: "0px 32px 24px 20px",
								height: "100%",
							}}
						>
							<Tabs
								style={{
									height: "100%",
									overflow: "hidden",
								}}
								items={items}
								tabBarExtraContent={tabsBarExtra}
								onChange={() => {
									setSearchVal("");
								}}
							/>
						</Content>
					</Layout>
				</Content>
				<Sider
					width="35%"
					theme="light"
					style={{
						height: "100%",
						position: "relative",
						scrollbarWidth: "thin",
						borderLeft: "1px solid #e8e8e8",
						overflow: "auto",
					}}
				>
					<Flex
						style={{
							padding: "2rem 1.5rem 0px",
							fontWeight: "400",
							justifyContent: "left",
						}}
						gap={"middle"}
					>
						<InfoCard
							title="Total email sent"
							value={emailCount}
							toolTipTitle="total email sent today"
							link={
								hasResourcePermission(
									RoleResource.EMAIL,
									RoleAction.LIST
								)
									? `${AppConfig.quizServerURL}/email?endpoint=/email/list`
									: undefined
							}
						/>
						<InfoCard
							title="Active users"
							value={activeUserCount}
							toolTipTitle="current count of users attempting tests"
							link={`${AppConfig.quizServerURL}/test/getactivetest`}
						/>
					</Flex>
					<Flex
						vertical
						style={{ padding: "2rem 1.5rem 0px" }}
						gap={12}
					>
						<Title level={4}>Recent Submission</Title>
						<RecentSubmissionChart
							recentSubmission={recentSubmission}
						/>
					</Flex>
					{hasResourcePermission(
						RoleResource.EMAIL,
						RoleAction.LIST
					) && (
						<Flex
							vertical
							style={{ padding: "2rem 1.5rem 0px" }}
							gap={12}
						>
							<Title level={4}>Emails activity</Title>
							<EmailActivityChart />
						</Flex>
					)}
				</Sider>
			</Layout>
		</>
	);
};

export default AdminDashboard;
