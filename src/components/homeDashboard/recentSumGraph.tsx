import { Line } from "@ant-design/plots";
import { Flex, Typography } from "antd";
const { Text } = Typography;
interface SubmissionType {
	quizId: string;
	quizName: string;
	userId: string;
	userName: string;
	score: number;
	submitTime: number;
	quizCreatedBy: string;
	creatorName: string;
	cutOffMarks?: number;
}
interface TransformedDataType {
	date: string;
	count: number;
}
function transformData(
	finalData: Record<string, number>
): TransformedDataType[] {
	return Object.keys(finalData).map(date => ({
		date,
		count: finalData[date],
	}));
}
const RecentSubmissionChart = ({
	recentSubmission,
}: {
	recentSubmission: SubmissionType[];
}) => {
	const finalData: Record<string, number> = {};
	recentSubmission = [...recentSubmission].reverse();

	recentSubmission?.forEach(userSub => {
		const time = new Date(userSub.submitTime);
		const day = time.getDate();
		const month = time.toLocaleString("default", { month: "short" });
		const year = time.getFullYear();
		const formattedDate = `${day} ${month} ${year}`; // "12 Oct 2024"

		// Increment count for this date
		finalData[formattedDate] = (finalData[formattedDate] || 0) + 1;
	});
	const data = transformData(finalData);
	const config = {
		data,
		height: 120,
		xField: "date",
		yField: "count",
		style: {
			lineWidth: 3,
			stroke: "#de6834",
			gradient: "y",
		},
		point: {
			shape: "circle",
			sizeField: 6,
			style: {
				fill: "#de6834",
				stroke: "#de6834",
			},
		},
		legend: false,
		axis: {
			x: null,
			y: null,
		},
		interaction: {
			tooltip: {
				crosshairs: false,
			},
		},
	};

	return (
		<>
			{recentSubmission.length === 0 ? (
				<Flex
					style={{
						minHeight: 150,
						justifyContent: "center",
						alignItems: "center",
					}}
				>
					<Text>No submissions yet</Text>
				</Flex>
			) : (
				<Line {...config} />
			)}
		</>
	);
};

export default RecentSubmissionChart;
