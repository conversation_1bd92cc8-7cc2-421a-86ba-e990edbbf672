import { <PERSON><PERSON>, <PERSON>, <PERSON>lex, message, Tooltip, Typography } from "antd";
import { CopyOutlined } from "@ant-design/icons";
import { Link } from "react-router";
import { quizClient } from "../../store/index";

interface ActiveTestDataType {
	_id: string;
	title: string;
	quizTime: number;
	quizCode?: number;
	createdBy: string;
	parentIdOfCreator?: string;
	startTime: string;
	userIds: string[];
	studentsCount: number;
}
const { Text, Title } = Typography;
const ActiveTest = ({ activeTest }: { activeTest: ActiveTestDataType[] }) => {
	const [messageApi, contextHolder] = message.useMessage({
		maxCount: 1,
	});
	const copyLink = async (id: string) => {
		if (!id) {
			messageApi.info("id is invalid");
			return;
		}
		try {
			const res = await quizClient.getTestLink(id);
			console.log(res);
			navigator.clipboard.writeText(res.link);
			messageApi.success("Link copied");
		} catch (error) {
			console.log(error);
			messageApi.error(String(error));
		}
	};
	return (
		<>
			{contextHolder}
			{activeTest.length > 0 ? (
				<>
					{activeTest.map(record => (
						<Card
							style={{
								margin: "8px 0px",
							}}
						>
							<Flex justify="space-between">
								<Flex vertical>
									<Tooltip title="Report">
										<Link to={`tests/${record._id}/report`}>
											<Title
												level={4}
												style={{ color: "#de6834" }}
											>
												{record.title}
											</Title>
										</Link>
									</Tooltip>
									<Text type="secondary">
										Test Code: {record?.quizCode}
									</Text>
								</Flex>
								<Flex align="center" gap="middle">
									<Flex vertical align="flex-end">
										<Link to={`tests/${record._id}/report`}>
											<Tooltip title={"Report"}>
												<Title
													level={5}
													style={{
														margin: 0,
														color: "#de6834",
													}}
												>
													{record?.studentsCount}
												</Title>
											</Tooltip>
										</Link>
										<Text type="secondary">
											Test submissions
										</Text>
									</Flex>
									<Tooltip
										placement="top"
										title="copy test link"
									>
										<Button
											icon={<CopyOutlined />}
											onClick={() => copyLink(record._id)}
										/>
									</Tooltip>
								</Flex>
							</Flex>
						</Card>
					))}
				</>
			) : (
				<Title level={5} style={{ textAlign: "center" }}>
					No Active tests
				</Title>
			)}
		</>
	);
};

export default ActiveTest;
