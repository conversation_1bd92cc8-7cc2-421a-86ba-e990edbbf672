export interface ActiveTestDataType {
	_id: string;
	title: string;
	quizTime: number;
	quizCode: number;
	createdBy: string;
	parentIdOfCreator: string;
	startTime: string;
	userIds: string[];
	studentsCount: number;
}

export const data: ActiveTestDataType[] = [
	{
		_id: "67c1477947f450baf2a0a3e2",
		title: "test-invite-Clone",
		quizTime: 123,
		quizCode: 123,
		createdBy: "6761335f03369c9461a16800",
		parentIdOfCreator: "59f9c87bbace049edfca78cf",
		startTime: "2024-12-17T08:17:00.000Z",
		userIds: ["67c1ac452cd79fd548bd758a"],
		studentsCount: 2,
	},
	{
		_id: "67c1477947f450baf2a0a3e3",
		title: "test-invite-Clone",
		quizTime: 123,
		quizCode: 123,
		createdBy: "6761335f03369c9461a16800",
		parentIdOfCreator: "59f9c87bbace049edfca78cf",
		startTime: "2024-12-17T08:17:00.000Z",
		userIds: ["67c1ac452cd79fd548bd758a"],
		studentsCount: 2,
	},
	{
		_id: "67c1477947f450baf2a0a3e4",
		title: "test-invite-Clone",
		quizTime: 123,
		quizCode: 123,
		createdBy: "6761335f03369c9461a16800",
		parentIdOfCreator: "59f9c87bbace049edfca78cf",
		startTime: "2024-12-17T08:17:00.000Z",
		userIds: ["67c1ac452cd79fd548bd758a"],
		studentsCount: 2,
	},
	{
		_id: "67c1477947f450baf2a0a3e5",
		title: "test-invite-Clone",
		quizTime: 123,
		quizCode: 123,
		createdBy: "6761335f03369c9461a16800",
		parentIdOfCreator: "59f9c87bbace049edfca78cf",
		startTime: "2024-12-17T08:17:00.000Z",
		userIds: ["67c1ac452cd79fd548bd758a"],
		studentsCount: 2,
	},
	{
		_id: "67c1477947f450baf2a0a3e6",
		title: "test-invite-Clone",
		quizTime: 123,
		quizCode: 123,
		createdBy: "6761335f03369c9461a16800",
		parentIdOfCreator: "59f9c87bbace049edfca78cf",
		startTime: "2024-12-17T08:17:00.000Z",
		userIds: ["67c1ac452cd79fd548bd758a"],
		studentsCount: 2,
	},
];
