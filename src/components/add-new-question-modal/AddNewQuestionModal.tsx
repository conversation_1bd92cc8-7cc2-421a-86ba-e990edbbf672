import { <PERSON><PERSON>, <PERSON><PERSON>, Mo<PERSON> } from "antd";
import QuesLayout from "../question-layout/ques-layout";
import {
	QuestionDificulty,
	QuestionType,
	TestContentQuestionType,
} from "@/client/test-add";
import { useQuestionStore } from "../../store/index";
import { QuestionAddRoot } from "../question-layout/data";

interface AddNewQuestionModalProps {
	open?: boolean;
	toggle: (_value: boolean) => void;
	onAccept?: (_question: TestContentQuestionType[]) => void;
}

const codingQuestionDefaultSettings: TestContentQuestionType["settings"] = {
	contentHead: true,
	contentTail: true,
	customInput: true,
	result: "0",
};

const AddNewQuestionModal = (props: AddNewQuestionModalProps) => {
	const { open, toggle: setOpen } = props;
	const { initialQuestionData, setQuesData } = useQuestionStore();

	const onAdd = () => {
		if ("ques" in initialQuestionData) {
			const commonProperties = {
				_id: initialQuestionData.ques._id,
				description: "",
				dificulty: QuestionDificulty.EASY,
				score: initialQuestionData.ques.score,
				title: initialQuestionData.ques.title,
			};

			const question = (() => {
				switch (initialQuestionData.ques.type) {
					case QuestionType.MCQ:
						return { ...commonProperties, type: QuestionType.MCQ };

					case QuestionType.SUBJECTIVE:
						return {
							...commonProperties,
							type: QuestionType.SUBJECTIVE,
						};

					case QuestionType.CODING:
						return {
							...commonProperties,
							type: QuestionType.CODING as QuestionType.CODING,
							settings: codingQuestionDefaultSettings,
						};

					case QuestionType.MULTIPLE:
						return {
							...commonProperties,
							type: QuestionType.MULTIPLE,
						};

					case QuestionType.WEB:
						return { ...commonProperties, type: QuestionType.WEB };

					default:
						return null;
				}
			})();

			if (question) {
				props.onAccept?.([question]);
			}
		}
		setQuesData({} as QuestionAddRoot);
	};

	return (
		<Modal
			title="Add Questions"
			onCancel={() => setOpen(false)}
			open={open}
			width={"90vw"}
			centered
			styles={{ body: { height: "75vh", overflow: "auto" } }}
			destroyOnClose
			cancelButtonProps={{
				style: {
					border: "none",
					boxShadow: "none",
					color: "#dd6833",
				},
			}}
			footer={[
				<Flex key="footer" justify="flex-end">
					<Flex gap={10}>
						<Button
							key="submit"
							type="primary"
							disabled={
								"ques" in initialQuestionData ? false : true
							}
							onClick={onAdd}
						>
							Add
						</Button>
					</Flex>
				</Flex>,
			]}
		>
			<QuesLayout isFromTestModal={true} />
		</Modal>
	);
};

export default AddNewQuestionModal;
