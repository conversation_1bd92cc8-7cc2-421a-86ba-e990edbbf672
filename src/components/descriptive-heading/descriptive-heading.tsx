import { Flex } from "antd";
import React from "react";

export default function DescriptiveHeading(props: {
	children: React.ReactNode;
	description: React.ReactNode;
}) {
	return (
		<Flex vertical gap={1} style={{ padding: "1em 0px" }}>
			<span>{props.children}</span>
			<span
				style={{
					fontSize: "0.875em",
					fontWeight: 400,
					color: "rgba(0, 0, 0, 0.45)",
				}}
			>
				{props.description}
			</span>
		</Flex>
	);
}
