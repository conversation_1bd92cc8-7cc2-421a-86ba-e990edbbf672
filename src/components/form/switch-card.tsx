import { Form, FormInstance, GetProps, Segmented, Switch } from "antd";
import CollapseCard from "../collapse-card";
import React, { useCallback } from "react";
import { SwitchChangeEventHandler } from "antd/es/switch";
import { SegmentedOptions } from "antd/es/segmented";
import { useWatch } from "antd/es/form/Form";

interface SwitchCardProps {
	form: FormInstance;
	name: string;
	labelTitle: React.ReactNode;
	labelDescription?: React.ReactNode;
	children?: React.ReactNode;
	defaultExpanded?: boolean;
	switchProps?: GetProps<typeof Switch>;
	disabled?: boolean;
	bordered?: boolean;
	ghost?: boolean;
}

export default function SwitchCard(props: SwitchCardProps) {
	const isChecked: boolean = useWatch(props.name, props.form) ?? false;

	const toggle = useCallback(
		(value: boolean) => {
			props.form.setFieldValue(props.name, value);
		},
		[props.form, props.name]
	);
	// useEffect(() => {
	// 	toggle(props.switchProps?.checked ?? false);
	// }, [props.switchProps, toggle]);

	const handleSwitchChange: SwitchChangeEventHandler = (checked, event) => {
		toggle(checked);
		props.switchProps?.onChange?.(checked, event);
	};

	return (
		<CollapseCard
			labelTitle={props.labelTitle}
			labelDescription={props.labelDescription}
			defaultExpanded={props.defaultExpanded ?? true}
			expanded={isChecked}
			toggle={toggle}
			disabled={props.disabled}
			bordered={props.bordered}
			ghost={props.ghost}
			extra={
				<Form.Item
					name={props.name}
					initialValue={isChecked}
					valuePropName="checked"
					noStyle
				>
					<Switch
						{...props.switchProps}
						onChange={handleSwitchChange}
						disabled={props.disabled ? true : undefined}
					/>
				</Form.Item>
			}
		>
			{props.children}
		</CollapseCard>
	);
}

interface SegmentedCardProps {
	form: FormInstance;
	name: string;
	labelTitle: React.ReactNode;
	labelDescription?: React.ReactNode;
	children?: React.ReactNode;
	defaultExpanded?: boolean;
	segmentedProps?: GetProps<typeof Segmented<boolean>>;
	segmentedOptions: SegmentedOptions<boolean>;
	disabled?: boolean;
	bordered?: boolean;
	ghost?: boolean;
	alignHeaderItems?: "center" | "flex-start" | "flex-end";
}

export function SegmentedCard(props: SegmentedCardProps) {
	const value: boolean =
		useWatch(props.name, props.form) ??
		props.form.getFieldValue(props.name) ??
		false;
	const toggle = useCallback(
		(value: boolean) => {
			props.form.setFieldValue(props.name, value);
		},
		[props.form, props.name]
	);
	// console.log(props.name,{expanded}, props.segmentedOptions.map((option) => option))

	// useEffect(() => {
	// 	toggle(props.segmentedProps?.checked ?? false);
	// }, [props.segmentedProps, toggle]);

	const handleSwitchChange = (checked: boolean) => {
		toggle(checked);
		props.segmentedProps?.onChange?.(checked);
	};

	return (
		<CollapseCard
			labelTitle={props.labelTitle}
			labelDescription={props.labelDescription}
			defaultExpanded={props.defaultExpanded ?? true}
			expanded={value}
			toggle={toggle}
			disabled={props.disabled}
			bordered={props.bordered}
			ghost={props.ghost}
			alignHeaderItems={props.alignHeaderItems}
			extra={
				<Form.Item name={props.name} noStyle>
					<Segmented
						{...props.segmentedProps}
						options={props.segmentedOptions}
						onChange={handleSwitchChange}
						disabled={props.disabled}
					/>
				</Form.Item>
			}
		>
			{props.children}
		</CollapseCard>
	);
}
