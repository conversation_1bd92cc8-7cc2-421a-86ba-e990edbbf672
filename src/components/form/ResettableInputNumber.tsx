import { Button, InputNumber, Space } from "antd";
import { RotateCcwIcon } from "lucide-react";

const ResettableInputNumber = ({
	value,
	onChange,
}: {
	value?: number;
	onChange?: (value: number | null) => void;
}) => {
	return (
		<Space.Compact direction="horizontal" size="middle">
			<InputNumber defaultValue={0} value={value} onChange={onChange} />
			<Button type="default" onClick={() => onChange?.(0)}>
				<RotateCcwIcon size={14} />
			</Button>
		</Space.Compact>
	);
};

export default ResettableInputNumber;
