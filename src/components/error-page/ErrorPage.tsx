import { Divider, Flex, Layout } from "antd";
import React from "react";
import { Typography, Image } from "antd";
import { Link } from "react-router";
import Header from "../header/Header";
import PageHelmet from "../page-helmet/PageHelmet";

const ErrorPage: React.FC = () => {
	return (
		<>
			<PageHelmet title="Page Not Found" />
			<Layout style={{ height: "100%" }}>
				<Layout.Header
					style={{ backgroundColor: "#f8f8f8", padding: 0 }}
				>
					<Header />
				</Layout.Header>
				<Divider style={{ margin: "0px" }} />
				<Layout.Content style={{ height: "100%", overflow: "auto" }}>
					<Flex
						justify="space-between"
						align="center"
						style={{ height: "100%" }}
					>
						<Flex gap={50}>
							<div style={{}}>
								<div
									style={{
										backgroundColor: "#983b12",
										width: "120px",
										height: "120px",
										opacity: "0.4",
										borderRadius: "50%",
										position: "relative",
										right: "15px",
									}}
								></div>
								<div
									style={{
										backgroundColor: "#de6438",
										width: "75px",
										height: "90px",
										opacity: "0.4",
										borderRadius: "50%",
										position: "relative",
										right: "50px",
										top: "-56px",
									}}
								></div>
								<div
									style={{
										backgroundColor: "#e6b9a1",
										width: "161px",
										height: "168px",
										opacity: "0.4",
										borderRadius: "50%",
										position: "relative",
										right: "68px",
										top: "-108px",
									}}
								></div>
							</div>
							<div
								style={{
									display: "flex",
									flexDirection: "column",
									gap: "12px",
								}}
							>
								<Typography.Title
									style={{
										color: "#de6438",
									}}
								>
									Uh oh!
								</Typography.Title>
								<Typography.Text
									style={{
										fontSize: "27px",
									}}
								>
									Sorry, it seems we can't find the page
									you're looking for : (
								</Typography.Text>
								<Typography.Link style={{ fontSize: "20px" }}>
									<Link to="/">Go Back to Home</Link>
								</Typography.Link>
							</div>
							<div style={{ marginLeft: "40px " }}>
								<Image
									src="/icons/error.svg"
									width={500}
									alt="404"
									preview={false}
								/>
							</div>
						</Flex>
					</Flex>
				</Layout.Content>
			</Layout>
		</>
	);
};

export default ErrorPage;
