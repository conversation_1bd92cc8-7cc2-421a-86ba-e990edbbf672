import { AppConfig } from "@/config";
import { useNavigate, useParams } from "react-router";
import PageHelmet from "../page-helmet/PageHelmet";
import { useEffect } from "react";

const QuestionPreview = () => {
	const id = useParams();
	const navigate = useNavigate();

	useEffect(
		function () {
			window.addEventListener("message", event => {
				try {
					switch (event.data.type) {
						case "redirect":
							navigate(event.data.path ?? "/");
							break;
					}
				} catch (ex) {
					console.error(ex);
				}
			});
		},
		[navigate]
	);

	return (
		<>
			<PageHelmet title="Preview" />

			<iframe
				src={`${AppConfig.quizServerURL}/quest/preview-react/${id.id}?redirect-type=iframe`}
				style={{ width: "100%", height: "99%", border: "none" }}
				allowFullScreen={true}
				allow="clipboard-read; clipboard-write"
			></iframe>
		</>
	);
};

export default QuestionPreview;
