import {
	Col,
	Form,
	FormInstance,
	Row,
	Button,
	InputNumber,
	Card,
	Table,
} from "antd";
import { DeleteOutlined, PlusCircleOutlined } from "@ant-design/icons";
import Title from "../common/title";
import QuestionType from "../common/type";
import Keywords from "../common/keywords";
import DifficultyLevel from "../common/difficultyLevel";
import Score from "../common/score";
import Premium from "../common/premium";
import ChitkaraFields from "../common/chitkaraFields";
import QuestionDescription from "../common/questionDescription";
import TextArea from "antd/es/input/TextArea";
import { useQuestionStore } from "../../../store";
import useObjectList from "@/hooks/util";
import { QuestionFormFieldDataType } from "../ques-edit-form";
import { AnyObject } from "antd/es/_util/type";
import { useWatch } from "antd/es/form/Form";
import { useEffect } from "react";

const MQ = ({
	quesId,
	formInstance,
}: {
	quesId?: string;
	formInstance: FormInstance;
}) => {
	const { initialQuestionData } = useQuestionStore();

	const questions = useWatch("questions", formInstance) || [];
	const totalScore = questions.reduce(
		(acc: number, question: { score?: number }) =>
			acc + (Number(question?.score) || 0),
		0
	);

	useEffect(() => {
		if (formInstance.getFieldValue("score") !== totalScore) {
			formInstance.setFieldsValue({ score: totalScore });
		}
	}, [totalScore, formInstance]);

	return (
		<>
			<Title regex={initialQuestionData?.questionTitleRegex || ""} />
			<Row gutter={[32, 32]}>
				<Col span={6}>
					<QuestionType
						quesId={quesId}
						questionTypeOptions={
							initialQuestionData?.questionTypeOptions ?? {}
						}
					/>
				</Col>

				<Col span={6}>
					<DifficultyLevel
						difficultyLevel={
							initialQuestionData?.difficultyLevel || {}
						}
					/>
				</Col>

				<Col span={4}>
					<Score isDisable={true} />
				</Col>

				<Col span={8}>
					<Keywords />
				</Col>
			</Row>

			<Premium formInstance={formInstance} />
			<ChitkaraFields
				questionOutcomes={initialQuestionData?.questionOutcomes || {}}
				questionBloomTaxonomy={
					initialQuestionData?.questionBloomTaxonomy || {}
				}
			/>
			<QuestionDescription />

			<Card title="Questions">
				<Form.Item
					name="questions"
					rules={[
						{
							validator: (
								_,
								questions: QuestionFormFieldDataType[]
							) => {
								for (const {
									question,
									answer,
									score,
								} of questions) {
									if (!question.trim()) {
										return Promise.reject(
											new Error("Question is required")
										);
									}
									if (!answer.trim()) {
										return Promise.reject(
											new Error("Answer is required")
										);
									}
									if (score === undefined) {
										return Promise.reject(
											new Error("Score is required")
										);
									}
									if (score < 0 || score > 9999) {
										return Promise.reject(
											new Error(
												"Score must be between 0 and 9999."
											)
										);
									}
								}
								return Promise.resolve();
							},
						},
					]}
				>
					<QuestionAnswerFormField name="__questions" />
				</Form.Item>
			</Card>
		</>
	);
};

type QuestionAnswerFormField<T extends AnyObject> = {
	name: string;
	value?: T[];
	onChange?: (value: T[]) => void;
};

function QuestionAnswerFormField(
	props: QuestionAnswerFormField<QuestionFormFieldDataType>
) {
	const {
		items: fields,
		add,
		remove,
		update,
	} = useObjectList(props.value ?? [], props.onChange);

	return (
		<>
			<div style={{ width: "100%", marginBottom: "16px" }}>
				<Table
					pagination={false}
					dataSource={fields}
					columns={[
						{
							key: "serialNumber",
							render: (_value, _record, index) => `${index + 1}.`,
							width: 64,
							align: "right",
						},
						{
							title: "Question",
							dataIndex: "question",
							key: "question",
							align: "left",
							render: (value, _, index) => (
								<Form.Item
									name={["questions", index, "question"]}
									rules={[
										{
											required: true,
											validator: (_, value) => {
												if (value.trim() === "") {
													return Promise.reject();
												}
												return Promise.resolve();
											},
										},
									]}
								>
									<TextArea
										rows={2}
										value={value}
										placeholder={`Question`}
										style={{
											maxHeight: "200px",
										}}
										onChange={event => {
											update(index, item => {
												item.question =
													event.target.value;
												return item;
											});
										}}
									/>
								</Form.Item>
							),
						},
						{
							title: "Answer",
							dataIndex: "answer",
							key: "answer",
							align: "left",
							render: (value, _, index) => (
								<Form.Item
									name={["questions", index, "answer"]}
									rules={[
										{
											required: true,
											validator: (_, value) => {
												if (value.trim() === "") {
													return Promise.reject();
												}
												return Promise.resolve();
											},
										},
									]}
								>
									<TextArea
										rows={2}
										value={value}
										placeholder={`Answer`}
										style={{
											maxHeight: "200px",
										}}
										onChange={event => {
											update(index, item => {
												item.answer =
													event.target.value;
												return item;
											});
										}}
									/>
								</Form.Item>
							),
						},
						{
							title: "Score",
							dataIndex: "score",
							key: "score",
							align: "left",
							render: (value, _, index) => (
								<Form.Item
									name={["questions", index, "score"]}
									rules={[
										{
											required: true,
											validator: (_, value) => {
												if (value == undefined) {
													return Promise.reject();
												}
												if (value < 0 || value > 9999) {
													return Promise.reject();
												}
												return Promise.resolve();
											},
										},
									]}
								>
									<InputNumber
										value={value}
										placeholder={`Score`}
										onChange={value => {
											update(index, item => {
												item.score = value;
												return item;
											});
										}}
									/>
								</Form.Item>
							),
						},
						{
							title: "Action",
							key: "actions",
							align: "center",
							width: "15%",
							render: (_, _record, index) =>
								fields.length > 1 ? (
									<Button
										type="primary"
										danger
										icon={<DeleteOutlined />}
										onClick={() => remove(index)}
									>
										Remove
									</Button>
								) : (
									"-"
								),
						},
					]}
				/>
			</div>
			<Button
				type="dashed"
				icon={<PlusCircleOutlined />}
				onClick={() =>
					add({ question: "", answer: "", score: undefined })
				}
			>
				Add More Question
			</Button>
		</>
	);
}
export default MQ;
