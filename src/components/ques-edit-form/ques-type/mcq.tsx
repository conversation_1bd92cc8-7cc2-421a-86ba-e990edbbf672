import {
	Checkbox,
	Col,
	Form,
	FormInstance,
	InputNumber,
	Row,
	Button,
	Card,
	Space,
	Table,
} from "antd";
import { DeleteOutlined, PlusCircleOutlined } from "@ant-design/icons";
import Title from "../common/title";
import QuestionType from "../common/type";
import Keywords from "../common/keywords";
import DifficultyLevel from "../common/difficultyLevel";
import Score from "../common/score";
import Premium from "../common/premium";
import ChitkaraFields from "../common/chitkaraFields";
import QuestionDescription from "../common/questionDescription";
import { useQuestionStore } from "../../../store";
import SwitchCard from "@/components/form/switch-card";
import { ChoicesDataType } from "../ques-edit-form";
import useObjectList from "@/hooks/util";
import { AnyObject } from "antd/es/_util/type";
import TextArea from "antd/es/input/TextArea";

const MCQ = ({
	quesId,
	formInstance,
}: {
	quesId?: string;
	formInstance: FormInstance;
}) => {
	const { initialQuestionData } = useQuestionStore();
	const multiAnswer: boolean =
		Form.useWatch("ismultianswer", formInstance) ?? false;
	const isMultiAnswer = multiAnswer;

	return (
		<>
			<Title regex={initialQuestionData?.questionTitleRegex || ""} />
			<Row gutter={[32, 32]}>
				<Col span={6}>
					<QuestionType
						quesId={quesId}
						questionTypeOptions={
							initialQuestionData?.questionTypeOptions ?? {}
						}
					/>
				</Col>

				<Col span={6}>
					<DifficultyLevel
						difficultyLevel={
							initialQuestionData?.difficultyLevel || {}
						}
					/>
				</Col>

				<Col span={6}>
					<Score formInstance={formInstance} />
				</Col>

				<Col span={6}>
					<Form.Item
						name="negativeScore"
						label="Negative Score"
						style={{ width: "100%" }}
						rules={[
							{
								validator: (_, value) => {
									const formData =
										formInstance.getFieldsValue();
									if (
										formData?.negativeScore >
										formData?.score
									) {
										return Promise.reject(
											new Error(
												"Negative score should be less than or equal to score"
											)
										);
									}
									if (value < 0) {
										return Promise.reject(
											new Error(
												"Negative score should be greater than or equal to 0"
											)
										);
									}
									if (value > 5) {
										return Promise.reject(
											new Error(
												"Negative score should be less than or equal to 5"
											)
										);
									}
									return Promise.resolve();
								},
							},
						]}
					>
						<InputNumber style={{ width: "100%" }} />
					</Form.Item>
				</Col>
			</Row>

			<div style={{ maxWidth: "30%" }}>
				<Keywords />
			</div>

			<Premium formInstance={formInstance} />
			<ChitkaraFields
				questionOutcomes={initialQuestionData?.questionOutcomes || {}}
				questionBloomTaxonomy={
					initialQuestionData?.questionBloomTaxonomy || {}
				}
			/>
			<QuestionDescription />

			<Card title="Answer choices">
				<SwitchCard
					form={formInstance}
					name="ismultianswer"
					labelTitle="Multiple answers"
					labelDescription="Check if the question has multiple correct answers"
				/>
				<Form.Item
					name="choices"
					rules={[
						{
							validator: (_, choices: ChoicesDataType[]) => {
								const trueCount = choices.filter(
									({ chkOpt }) => chkOpt
								).length;
								const txtOpts = choices.map(({ txtOpt }) =>
									txtOpt.trim()
								);
								const hasDuplicates =
									new Set(txtOpts).size !== txtOpts.length;

								if (txtOpts.includes("")) {
									return Promise.reject(
										new Error(
											"Answer choice cannot be empty"
										)
									);
								}

								if (isMultiAnswer) {
									if (trueCount < 2) {
										return Promise.reject(
											new Error(
												"Please select at least 2 choices"
											)
										);
									}
								} else {
									if (trueCount > 1) {
										return Promise.reject(
											new Error(
												"Please select only 1 choice"
											)
										);
									} else if (trueCount < 1) {
										return Promise.reject(
											new Error(
												"Please select at least 1 choice"
											)
										);
									}
								}

								if (hasDuplicates) {
									return Promise.reject(
										new Error(
											"Duplicate answer choices found"
										)
									);
								}

								return Promise.resolve();
							},
						},
					]}
				>
					<AnswerChoicesFormField name="__choices" />
				</Form.Item>
			</Card>
		</>
	);
};
type AnswerChoicesFormField<T extends AnyObject> = {
	name: string;
	value?: T[];
	onChange?: (value: T[]) => void;
};

function AnswerChoicesFormField(
	props: AnswerChoicesFormField<ChoicesDataType>
) {
	const {
		items: fields,
		add,
		remove,
		update,
	} = useObjectList(props.value ?? [], props.onChange);

	return (
		<>
			<div style={{ width: "100%", margin: "24px 0" }}>
				<Table
					pagination={false}
					dataSource={fields}
					columns={[
						{
							key: "serialNumber",
							render: (_value, _record, index) => `${index + 1}.`,
							width: 64,
							align: "right",
						},
						{
							title: "Correct Answer",
							dataIndex: "chkOpt",
							key: "chkOpt",
							align: "center",
							width: "15%",
							render: (value, _, index) => (
								<Form.Item
									name={["choices", index, "chkOpt"]}
									valuePropName="checked"
								>
									<Checkbox
										checked={value}
										onChange={event => {
											update(index, item => {
												item.chkOpt =
													event.target.checked;
												return item;
											});
										}}
									/>
								</Form.Item>
							),
						},
						{
							title: "Answer Choice",
							dataIndex: "txtOpt",
							key: "txtOpt",
							align: "left",
							render: (value, _, index) => (
								<Form.Item
									name={["choices", index, "txtOpt"]}
									rules={[
										{
											required: true,
											validator: (_, value) => {
												if (value.trim() === "") {
													return Promise.reject();
												}
												return Promise.resolve();
											},
										},
									]}
								>
									<TextArea
										rows={2}
										value={value}
										placeholder={`Choice ${index + 1}`}
										style={{ maxHeight: "200px" }}
										onChange={event => {
											update(index, item => {
												item.txtOpt =
													event.target.value;
												return item;
											});
										}}
									/>
								</Form.Item>
							),
						},
						{
							title: "Action",
							key: "actions",
							align: "center",
							width: "15%",
							render: (_, _record, index) =>
								fields.length > 2 ? (
									<Button
										type="primary"
										danger
										icon={<DeleteOutlined />}
										onClick={() => remove(index)}
									>
										Remove
									</Button>
								) : (
									"-"
								),
						},
					]}
				/>
			</div>
			<Space>
				<Button
					type="dashed"
					icon={<PlusCircleOutlined />}
					disabled={fields.length >= 8}
					onClick={() => add({ txtOpt: "", chkOpt: false })}
				>
					Add Choice
				</Button>
			</Space>
		</>
	);
}

export default MCQ;
