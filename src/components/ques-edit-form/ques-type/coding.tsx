import {
	Button,
	Col,
	Form,
	FormInstance,
	message,
	Row,
	Select,
	Space,
	Tabs,
	Tooltip,
	Typography,
	Upload,
} from "antd";
import "react-quill/dist/quill.snow.css";
import {
	Title,
	QuestionType,
	DifficultyLevel,
	Premium,
	Keywords,
	ChitkaraFields,
	QuestionDescription,
	Score,
} from "../common";
import { DeleteOutlined, PaperClipOutlined } from "@ant-design/icons";
import { quizClient, useAppStore, useQuestionStore } from "../../../store";
import {
	QuestionSupportedLanguage,
	QuestionSupportedValueToName,
} from "@/constants/languages";
import AceEditor from "react-ace";
import "ace-builds/src-noconflict/ext-language_tools";
import "ace-builds/src-min-noconflict/ace";
import { useEffect, useState } from "react";
import { RcFile, UploadProps } from "antd/es/upload";
import { AppConfig } from "@/config";

const editorOptions = {
	enableMobileMenu: true,
	showLineNumbers: true,
	tabSize: 4,
	useSoftTabs: true,
	behavioursEnabled: true,
	wrapBehavioursEnabled: true,
};

const Coding = ({
	formInstance,
	quesId,
}: {
	formInstance: FormInstance;
	quesId?: string;
}) => {
	const { session } = useAppStore();
	const { initialQuestionData } = useQuestionStore();
	const [fileList, setFileList] = useState<RcFile[]>([]);
	const [messageInstance, contextHolder] = message.useMessage({
		maxCount: 1,
	});
	const [initialFiles, setInitialFiles] = useState<string[]>([]);

	useEffect(() => {
		if (
			"ques" in initialQuestionData &&
			initialQuestionData.ques.files?.length > 0
		) {
			setInitialFiles(initialQuestionData.ques.files);
		}
	}, [initialQuestionData]);

	const handledUIDs = new Set<string>();

	const handleFileChange: UploadProps["onChange"] = ({
		fileList: newFileList,
	}) => {
		const maxFiles = 15;
		const allowedExtensions = [
			"doc",
			"pdf",
			"docx",
			"txt",
			"jpeg",
			"jpg",
			"png",
			"raw",
			"csv",
		];
		const allowedTypes = [
			"application/msword",
			"application/vnd.openxmlformats-officedocument.wordprocessingml.document",
			"application/pdf",
			"text/plain",
			"image/jpeg",
			"image/png",
			"image/RAW",
			"text/csv",
		];

		let errorMessages: string = "";
		const validatedFiles: RcFile[] = [];

		for (const file of newFileList) {
			if (handledUIDs.has(file.uid)) continue;
			handledUIDs.add(file.uid);
			if (errorMessages) break;
			const fileExtension = file.name.split(".").pop()?.toLowerCase();
			const fileSizeMB = (file.size ?? 0) / 1024 / 1024;

			if (
				!fileExtension ||
				!allowedExtensions.includes(fileExtension) ||
				!allowedTypes.includes(file.type ?? "")
			) {
				errorMessages = `Invalid file type`;
			} else if (fileSizeMB > 2) {
				errorMessages = `File should be less than 2MB.`;
			} else if (file.name.includes(" ")) {
				errorMessages = `Spaces is not allowed in file name`;
			} else {
				validatedFiles.push(file as RcFile);
			}
		}

		if (validatedFiles.length > maxFiles) {
			messageInstance.error(`Can't add more than ${maxFiles} files.`);
			return;
		}

		if (errorMessages) {
			messageInstance.error(errorMessages);
		}

		setFileList(validatedFiles);
		formInstance.setFieldsValue({ upldFile: validatedFiles });
	};

	const uploadProps: UploadProps = {
		fileList,
		showUploadList: { showRemoveIcon: true },
		multiple: true,
		onChange: handleFileChange,
		beforeUpload: () => false,
		onRemove: file => {
			const updatedList = fileList.filter(item => item.uid !== file.uid);
			setFileList(updatedList);
			formInstance.setFieldsValue({ upldFile: updatedList });
		},
		accept: ".doc,.pdf,.docx,.txt,.jpeg,.jpg,.png,.raw,.csv",
	};

	const fileDownload = (file: string) => {
		if (file) {
			window.open(
				`${AppConfig.quizServerURL}/download?file=${quesId}/${file}`,
				"_blank"
			);
		}
	};

	const removeFile = async (fileName: string) => {
		if (!quesId) {
			messageInstance.error("Unable to delete file.");
			return;
		}
		try {
			const response = await quizClient.questRemoveFile(fileName, quesId);
			if ("msg" in response && response.msg === "success") {
				messageInstance.success("File deleted successfully.");
				setInitialFiles(initialFiles.filter(f => f !== fileName));
			} else {
				if ("error" in response) {
					messageInstance.success(response.error);
				}
			}
		} catch (error) {
			console.log(error);
		}
	};

	return (
		<>
			{contextHolder}
			<Title regex={initialQuestionData?.questionTitleRegex || ""} />
			<Row gutter={[32, 32]}>
				<Col span={6}>
					<QuestionType
						quesId={quesId}
						questionTypeOptions={
							initialQuestionData?.questionTypeOptions ?? {}
						}
					/>
				</Col>
				<Col span={6}>
					<DifficultyLevel
						difficultyLevel={
							initialQuestionData?.difficultyLevel || {}
						}
					/>
				</Col>
				<Col span={4}>
					<Score isDisable={true} required={false} />
				</Col>
				<Col span={8}>
					<Keywords />
				</Col>
			</Row>
			<Premium formInstance={formInstance} />
			<ChitkaraFields
				questionOutcomes={initialQuestionData?.questionOutcomes || {}}
				questionBloomTaxonomy={
					initialQuestionData?.questionBloomTaxonomy || {}
				}
			/>

			<QuestionDescription />

			<Form.Item
				label="Languages Allowed"
				name="languages"
				rules={[
					{
						required: true,
					},
				]}
			>
				<Select
					mode="multiple"
					placeholder="Please select Language"
					optionFilterProp="label"
					onChange={langs => {
						const currentCodes =
							formInstance.getFieldValue("code") || [];

						const updatedCode = currentCodes.filter(
							(code: unknown) =>
								langs.includes(
									(code as { language: string }).language
								)
						);

						langs.forEach((lang: string) => {
							if (
								!updatedCode.some(
									(c: unknown) =>
										(c as { language: string }).language ===
										lang
								)
							) {
								if (!session?.languagesAllowed?.includes(lang))
									return;

								updatedCode.push({
									language: lang,
									head: "",
									body:
										initialQuestionData.supportedLanguages.find(
											langObj => langObj.code === lang
										)?.defaultCode || "",
									tail: "",
									solution: "",
								});
							}
						});

						formInstance.setFieldsValue({ code: updatedCode });
					}}
					options={session?.languagesAllowed
						?.map(language => {
							const lang =
								language as keyof typeof QuestionSupportedValueToName;
							if (!QuestionSupportedValueToName[lang])
								return null;
							return {
								label: QuestionSupportedValueToName[
									lang as keyof typeof QuestionSupportedLanguage
								],
								value: lang,
							};
						})
						.filter(o => o !== null)}
					filterOption={(input, option) =>
						(option?.label ?? "")
							?.toString()
							.toLowerCase()
							.includes(input.trim().toLowerCase())
					}
				></Select>
			</Form.Item>

			<Form.List name="code">
				{fields => {
					const tabItems = fields.map(({ key, name }) => {
						const lang = formInstance.getFieldValue([
							"code",
							name,
							"language",
						]);

						return {
							key: key.toString(),
							label: QuestionSupportedValueToName[
								lang as keyof typeof QuestionSupportedValueToName
							],
							children: (
								<Tabs
									defaultActiveKey="body"
									type="card"
									destroyInactiveTabPane={false}
									tabBarStyle={{ marginBottom: 0 }}
									items={["head", "body", "tail"].map(
										section => ({
											key: section,
											label:
												section
													.charAt(0)
													.toUpperCase() +
												section.slice(1),
											children: (
												<Form.Item
													name={[name, section]}
												>
													<AceEditor
														mode={lang
															.toLowerCase()
															.replace(
																/[^a-z0-9]/g,
																""
															)}
														theme="textmate"
														fontSize={16}
														lineHeight={24}
														setOptions={
															editorOptions
														}
														width="100%"
														height="350px"
														showPrintMargin={false}
													/>
												</Form.Item>
											),
										})
									)}
								/>
							),
						};
					});
					return (
						formInstance.getFieldValue("languages")?.length > 0 && (
							<Tabs
								style={{
									backgroundColor: "#fbfbfb",
									padding: "0px 16px",
									borderRadius: 8,
								}}
								items={tabItems.map(item => ({
									...item,
								}))}
							/>
						)
					);
				}}
			</Form.List>

			<Space align="center">
				<Form.Item name="upldFile" style={{ marginTop: "1em" }}>
					<Space direction="vertical">
						<Upload {...uploadProps}>
							<Button
								icon={<PaperClipOutlined />}
								iconPosition="end"
							>
								Choose File
							</Button>
						</Upload>
						<Typography.Text
							style={{
								fontSize: "0.8rem",
								maxWidth: "80%",
								display: "block",
							}}
						>
							Choose file to perform file actions. Supported
							extensions are (.doc, .pdf, .docx, .txt, .jpeg,
							.jpg, .png, .raw, .csv)
						</Typography.Text>
					</Space>
				</Form.Item>
				{initialFiles && initialFiles.length > 0 && (
					<Space direction="vertical">
						<Typography.Text>
							Download File Added for this course
						</Typography.Text>
						{initialFiles.map((file: string, index: number) => (
							<Space key={file}>
								<Tooltip title="download">
									<Typography.Text
										onClick={() => fileDownload(file)}
										style={{
											cursor: "pointer",
										}}
									>
										{index + 1}. {file}
									</Typography.Text>
								</Tooltip>
								<Tooltip title="delete">
									<Button
										size="small"
										type="text"
										icon={<DeleteOutlined />}
										onClick={() => removeFile(file)}
									/>
								</Tooltip>
							</Space>
						))}
					</Space>
				)}
			</Space>
		</>
	);
};

export default Coding;
