import { Col, Form, FormInstance, Row, Typography } from "antd";
import "react-quill/dist/quill.snow.css";
import Title from "../common/title";
import QuestionType from "../common/type";
import DifficultyLevel from "../common/difficultyLevel";
import Premium from "../common/premium";
import Keywords from "../common/keywords";
import ChitkaraFields from "../common/chitkaraFields";
import QuestionDescription from "../common/questionDescription";
import Score from "../common/score";
import ReactQuill from "react-quill";
import { useRef } from "react";
import { useQuestionStore } from "../../../store";
import SwitchCard from "@/components/form/switch-card";

const Subjective = ({
	quesId,
	formInstance,
}: {
	quesId?: string;
	formInstance: FormInstance;
}) => {
	const { initialQuestionData } = useQuestionStore();

	const quillRef = useRef<ReactQuill | null>(null);

	return (
		<>
			<Title regex={initialQuestionData?.questionTitleRegex || ""} />
			<Row gutter={[32, 32]}>
				<Col span={6}>
					<QuestionType
						quesId={quesId}
						questionTypeOptions={
							initialQuestionData?.questionTypeOptions ?? {}
						}
					/>
				</Col>

				<Col span={6}>
					<DifficultyLevel
						difficultyLevel={
							initialQuestionData?.difficultyLevel || {}
						}
					/>
				</Col>

				<Col span={4}>
					<Score required={true} />
				</Col>

				<Col span={8}>
					<Keywords />
				</Col>
			</Row>
			<Premium formInstance={formInstance} />
			<SwitchCard
				form={formInstance}
				name="isFileUpload"
				labelTitle="File upload"
				labelDescription="Allow user to upload file(s)."
			/>
			<ChitkaraFields
				questionOutcomes={initialQuestionData?.questionOutcomes || {}}
				questionBloomTaxonomy={
					initialQuestionData?.questionBloomTaxonomy || {}
				}
			/>
			<QuestionDescription />
			<Form.Item
				label={
					<Typography.Title level={3} style={{ color: "#de6834" }}>
						Answer
					</Typography.Title>
				}
				name="txtSub"
				style={{ marginBottom: "1em" }}
				className="bound"
			>
				<ReactQuill
					ref={quillRef}
					theme="snow"
					bounds=".bound"
					style={{
						height: "250px",
						paddingBottom: 45,
					}}
				/>
			</Form.Item>
		</>
	);
};

export default Subjective;
