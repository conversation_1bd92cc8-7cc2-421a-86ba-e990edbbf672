import { useEffect, useState } from "react";
import {
	Tabs,
	Row,
	Col,
	Button,
	Space,
	Form,
	Checkbox,
	Card,
	InputNumber,
	Table,
} from "antd";
import AceEditor from "react-ace";
import "ace-builds/src-noconflict/mode-html";
import "ace-builds/src-noconflict/mode-css";
import "ace-builds/src-noconflict/mode-javascript";
import "ace-builds/src-noconflict/theme-textmate";
import { FormInstance } from "antd";
import Title from "../common/title";
import QuestionType from "../common/type";
import Keywords from "../common/keywords";
import DifficultyLevel from "../common/difficultyLevel";
import Score from "../common/score";
import Premium from "../common/premium";
import ChitkaraFields from "../common/chitkaraFields";
import QuestionDescription from "../common/questionDescription";
import TextArea from "antd/es/input/TextArea";
import { DeleteOutlined, PlusCircleOutlined } from "@ant-design/icons";
import { useQuestionStore } from "../../../store";
import { useWatch } from "antd/es/form/Form";
import { AnyObject } from "antd/es/_util/type";
import { TestCaseFormFiledDataType } from "../ques-edit-form";
import useObjectList from "@/hooks/util";

const Web = ({
	quesId,
	formInstance,
}: {
	quesId?: string;
	formInstance: FormInstance;
}) => {
	const { initialQuestionData } = useQuestionStore();
	const [output, setOutput] = useState("");

	const testcases = useWatch("testcases", formInstance) || [];
	const totalScore = testcases.reduce(
		(acc: number, testcase: { score?: number }) =>
			acc + (Number(testcase?.score) || 0),
		0
	);

	useEffect(() => {
		if (testcases.length > 0) {
			formInstance.setFieldsValue({ score: undefined });
		}
		if (formInstance.getFieldValue("score") !== totalScore) {
			formInstance.setFieldsValue({ score: totalScore });
		}
	}, [totalScore, formInstance, testcases.length]);

	const editorOptions = {
		enableBasicAutocompletion: false,
		enableLiveAutocompletion: false,
		enableSnippets: false,
		enableMobileMenu: true,
		showLineNumbers: true,
		tabSize: 2,
	};

	const runCode = () => {
		const htmlCode = formInstance.getFieldValue("html");
		const cssCode = formInstance.getFieldValue("css");
		const jsCode = formInstance.getFieldValue("js");
		const iframe = `<html><head><style>${cssCode}</style></head><body>${htmlCode}<script>${jsCode}</script></body></html>`;
		setOutput(iframe);
	};

	return (
		<>
			<Title regex={initialQuestionData?.questionTitleRegex || ""} />
			<Row gutter={[32, 32]}>
				<Col span={6}>
					<QuestionType
						quesId={quesId}
						questionTypeOptions={
							initialQuestionData?.questionTypeOptions ?? {}
						}
					/>
				</Col>

				<Col span={6}>
					<DifficultyLevel
						difficultyLevel={
							initialQuestionData?.difficultyLevel || {}
						}
					/>
				</Col>

				<Col span={4}>
					<Score isDisable={testcases.length > 0} />
				</Col>

				<Col span={8}>
					<Keywords />
				</Col>
			</Row>

			<Premium formInstance={formInstance} />
			<ChitkaraFields
				questionOutcomes={initialQuestionData?.questionOutcomes || {}}
				questionBloomTaxonomy={
					initialQuestionData?.questionBloomTaxonomy || {}
				}
			/>
			<QuestionDescription />
			<Space
				size={20}
				style={{ marginBottom: "-0.5em", marginTop: "2em" }}
			>
				<Form.Item
					name="isHtmlAllowed"
					valuePropName="checked"
					style={{ marginBottom: "1em" }}
				>
					<Checkbox
						onChange={e =>
							formInstance.setFieldsValue({
								isHtmlAllowed: e.target.checked ? 1 : 0,
							})
						}
					>
						HTML
					</Checkbox>
				</Form.Item>
				<Form.Item
					name="isCssAllowed"
					valuePropName="checked"
					style={{ marginBottom: "1em" }}
				>
					<Checkbox
						onChange={e =>
							formInstance.setFieldsValue({
								isCssAllowed: e.target.checked ? 1 : 0,
							})
						}
					>
						CSS
					</Checkbox>
				</Form.Item>
				<Form.Item
					name="isJsAllowed"
					valuePropName="checked"
					style={{ marginBottom: "1em" }}
				>
					<Checkbox
						onChange={e =>
							formInstance.setFieldsValue({
								isJsAllowed: e.target.checked ? 1 : 0,
							})
						}
					>
						JS
					</Checkbox>
				</Form.Item>
			</Space>
			<Card style={{ marginBottom: "1em" }}>
				<Row gutter={[32, 32]}>
					<Col span={12}>
						<Tabs
							defaultActiveKey="html"
							type="card"
							items={["html", "css", "js"].map(section => ({
								key: section,
								label: `Index.${section}`,
								children: (
									<div style={{ margin: 0 }}>
										<Form.Item name={section}>
											<AceEditor
												mode={section
													.toLowerCase()
													.replace(/[^a-z0-9]/g, "")}
												theme="textmate"
												fontSize={16}
												lineHeight={24}
												setOptions={editorOptions}
												width="100%"
												height="350px"
											/>
										</Form.Item>
									</div>
								),
							}))}
						/>
					</Col>
					<Col span={12}>
						<Card styles={{ body: { padding: 0 } }}>
							<iframe
								style={{
									width: "100%",
									height: "350px",
									border: "none",
								}}
								srcDoc={output}
							/>
						</Card>
					</Col>
				</Row>
				<Button
					onClick={runCode}
					type="default"
					style={{ marginTop: 16 }}
				>
					Preview
				</Button>
			</Card>

			<Card title="Test Cases">
				<Form.Item
					name="testcases"
					rules={[
						{
							validator: (
								_,
								testcases: TestCaseFormFiledDataType[]
							) => {
								for (const {
									evaluator,
									description,
									score,
								} of testcases) {
									if (!evaluator.trim()) {
										return Promise.reject(
											new Error("Evaluator is required")
										);
									}
									if (!description.trim()) {
										return Promise.reject(
											new Error("Description is required")
										);
									}
									if (score === undefined) {
										return Promise.reject(
											new Error("Score is required")
										);
									}
									if (score < 0 || score > 9999) {
										return Promise.reject(
											new Error(
												"Score must be between 0 and 9999."
											)
										);
									}
								}
								return Promise.resolve();
							},
						},
					]}
				>
					<WebTestCasesFormField name="testcases" />
				</Form.Item>
			</Card>
		</>
	);
};

type TestCasesFormField<T extends AnyObject> = {
	name: string;
	value?: T[];
	onChange?: (value: T[]) => void;
};

function WebTestCasesFormField(
	props: TestCasesFormField<TestCaseFormFiledDataType>
) {
	const {
		items: fields,
		add,
		remove,
		update,
	} = useObjectList(props.value ?? [], props.onChange);

	return (
		<>
			<div style={{ width: "100%", marginBottom: "16px" }}>
				<Table
					pagination={false}
					dataSource={fields}
					columns={[
						{
							key: "serialNumber",
							render: (_value, _record, index) => `${index + 1}.`,
							width: 64,
							align: "right",
						},
						{
							title: "Evaluator",
							dataIndex: "evaluator",
							key: "evaluator",
							align: "left",

							render: (value, _, index) => (
								<Form.Item
									name={["testcases", index, "evaluator"]}
									rules={[
										{
											required: true,
											validator: (_, value) => {
												if (value.trim() === "") {
													return Promise.reject();
												}
												return Promise.resolve();
											},
										},
									]}
								>
									<TextArea
										rows={2}
										value={value}
										placeholder="Evaluator"
										style={{ maxHeight: "200px" }}
										onChange={event =>
											update(index, item => {
												item.evaluator =
													event.target.value;
												return item;
											})
										}
									/>
								</Form.Item>
							),
						},
						{
							title: "Description",
							dataIndex: "description",
							key: "description",
							align: "left",

							render: (value, _, index) => (
								<Form.Item
									name={["testcases", index, "description"]}
									rules={[
										{
											required: true,
											validator: (_, value) => {
												if (value.trim() === "") {
													return Promise.reject();
												}
												return Promise.resolve();
											},
										},
									]}
								>
									<TextArea
										rows={2}
										value={value}
										placeholder="Description"
										style={{ maxHeight: "200px" }}
										onChange={event =>
											update(index, item => {
												item.description =
													event.target.value;
												return item;
											})
										}
									/>
								</Form.Item>
							),
						},

						{
							title: "Score",
							dataIndex: "score",
							key: "score",
							align: "left",
							render: (value, _, index) => (
								<Form.Item
									name={["testcases", index, "score"]}
									rules={[
										{
											required: true,
											validator: (_, value) => {
												if (value == undefined) {
													return Promise.reject();
												}
												if (value < 0 || value > 9999) {
													return Promise.reject();
												}
												return Promise.resolve();
											},
										},
									]}
								>
									<InputNumber
										value={value}
										placeholder={`Score`}
										onChange={value => {
											update(index, item => {
												item.score = value;
												return item;
											});
										}}
									/>
								</Form.Item>
							),
						},
						{
							title: "Action",
							key: "actions",
							align: "center",
							width: "15%",
							render: (_, _record, index) => (
								<Button
									type="primary"
									danger
									icon={<DeleteOutlined />}
									onClick={() => remove(index)}
								>
									Remove
								</Button>
							),
						},
					]}
				/>
			</div>
			<Button
				type="dashed"
				icon={<PlusCircleOutlined />}
				onClick={() =>
					add({ evaluator: "", description: "", score: undefined })
				}
			>
				add more test case
			</Button>
		</>
	);
}

export default Web;
