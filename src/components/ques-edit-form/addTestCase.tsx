import { DataTable } from "@/components/dataTable/dataTable";
import { TestCase } from "@/testReport/data/data";
import {
	TableColumnsType,
	Switch,
	Space,
	Tooltip,
	Button,
	message,
	Layout,
	Flex,
} from "antd";
import { useCallback, useEffect, useMemo, useState } from "react";
import { EndPointRequestBody, PostEndPointsMap } from "@/client/test-add";
import { quizClient, useQuestionStore } from "../../store";
import { EditOutlined, DeleteOutlined, PlusOutlined } from "@ant-design/icons";
import { Content, Footer } from "antd/es/layout/layout";
import { useForm } from "antd/es/form/Form";
import useModal from "antd/es/modal/useModal";
import { QuestionEditRoot } from "../question-layout/data";
import TestCaseModal from "../testcase-modal/testcaseModal";

type TestCaseRequestData = EndPointRequestBody<
	PostEndPointsMap["/quest/addTestCase"]
>;

type DeleteTestCaseRequestData = EndPointRequestBody<
	PostEndPointsMap["/quest/deleteTestCase"]
>;

const AddTestCase = ({
	quesId: id,
	setActiveTabKey,
}: {
	quesId?: string;
	setActiveTabKey: (_value: string) => void;
}) => {
	const { initialQuestionData, setQuesData } = useQuestionStore();
	const [testCases, setTestCases] = useState<TestCase[]>([]);
	const [editingTestCase, setEditingTestCase] = useState<
		(TestCase & { index: number }) | null
	>(null);
	const [loading, setLoading] = useState<boolean>(false);
	const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>(
		{}
	);
	const [showModal, setShowModal] = useState<boolean>(false);
	const [messageApi, contextHolder] = message.useMessage();
	const [modal, modalContext] = useModal();
	const [testCaseForm] = useForm();
	const [isModelDataLoading, setIsModelDataLoading] =
		useState<boolean>(false);
	useEffect(() => {
		if ("ques" in initialQuestionData) {
			setTestCases(
				initialQuestionData.ques.questionTypeCoding?.testCase || []
			);
		} else {
			setTestCases([]);
		}
	}, [initialQuestionData]);

	const syncWithStore = useCallback(
		(updatedTestCases: TestCase[]) => {
			if ("ques" in initialQuestionData) {
				const totalScore = updatedTestCases.reduce(
					(acc, testCase) => acc + (testCase.scoreip || 0),
					0
				);
				const updatedData: QuestionEditRoot = {
					quesDetail: {
						...initialQuestionData,
						ques: {
							...initialQuestionData.ques,
							score: totalScore,
							questionTypeCoding: {
								...initialQuestionData.ques.questionTypeCoding,
								codeproglang:
									initialQuestionData.ques.questionTypeCoding
										?.codeproglang || [],
								testCase: updatedTestCases,
							},
						},
					},
				};
				setQuesData(updatedData.quesDetail);
			}
		},
		[initialQuestionData, setQuesData]
	);

	const handleModalSubmit = async (data: {
		input: string;
		output: string;
		score: number;
		sampleTest: boolean;
	}) => {
		if (!id) {
			messageApi.error("Id is not present");
			return;
		}
		setLoading(true);
		try {
			if (editingTestCase) {
				const requestBody: TestCaseRequestData = {
					input: data.input,
					output: data.output,
					sampleTest: data.sampleTest,
					quesId: id,
					testId: editingTestCase._id,
					score: data.score,
				};
				const response = await quizClient.addTestCase(requestBody);
				if (response.error) throw response.error;
				setTestCases(prev => {
					const updated = prev.map(tc =>
						tc._id === editingTestCase._id
							? {
									...tc,
									scoreip: data.score,
									sampleTest: data.sampleTest,
									codeproginputparams: data.input,
									codeprogexpectedoutput: data.output,
								}
							: tc
					);
					syncWithStore(updated);
					return updated;
				});

				messageApi.success("Test case updated.");
			} else {
				const requestBody: TestCaseRequestData = {
					input: data.input,
					output: data.output,
					sampleTest: data.sampleTest,
					quesId: id,
					score: data.score,
				};
				const response = await quizClient.addTestCase(requestBody);
				if (response.error) throw response.error;
				if (!response.testId) throw "Unable to add test case";

				const newTestCase: TestCase = {
					sampleTest: data.sampleTest,
					attemptInMultiLine: false,
					codeprogexpectedoutput: data.output,
					codeproginputparams: data.input,
					scoreip: data.score,
					_id: response.testId,
				};
				setTestCases(prev => {
					const newList = [...prev, newTestCase];
					syncWithStore(newList);
					return newList;
				});

				messageApi.success("Test case saved.");
			}
			testCaseForm.resetFields();
			setShowModal(false);
			setEditingTestCase(null);
		} catch (error) {
			messageApi.error(String(error));
			console.error(error);
		} finally {
			setLoading(false);
		}
	};

	const toggleSampleTestCase = useCallback(
		async (checked: boolean, testCaseId: string) => {
			if (!id) {
				messageApi.error("Id is not present");
				return;
			}
			const currentTestCase = testCases.find(
				({ _id }) => _id === testCaseId
			);
			if (!currentTestCase) return;
			setLoading(true);
			setLoadingStates(prev => ({ ...prev, [testCaseId]: true }));

			try {
				const requestBody: TestCaseRequestData = {
					input: currentTestCase.codeproginputparams,
					output: currentTestCase.codeprogexpectedoutput,
					sampleTest: checked,
					quesId: id,
					testId: testCaseId,
					score: currentTestCase.scoreip,
				};

				const response = await quizClient.addTestCase(requestBody);
				if (response.error) {
					throw response.error;
				}
				setTestCases(prev => {
					const updated = prev.map(tc =>
						tc._id === testCaseId
							? { ...tc, sampleTest: checked }
							: tc
					);
					syncWithStore(updated);
					return updated;
				});

				messageApi.success("Test case saved.");
			} catch (error) {
				messageApi.error(String(error));
				console.error(error);
			} finally {
				setLoading(false);
				setLoadingStates(prev => ({ ...prev, [testCaseId]: false }));
			}
		},
		[id, testCases, messageApi, syncWithStore]
	);

	const openEditModal = useCallback(
		async (tId: string) => {
			if (loading) return;
			let index = 0;
			const testCase = testCases.find((tc, idx) => {
				const found = tc._id === tId;
				if (found) {
					index = idx;
				}
				return found;
			});
			if (!testCase) return;
			setShowModal(true);
			if (!testCase.onCloud) {
				setEditingTestCase({
					...testCase,
					index: index,
				});
				return;
			}
			setIsModelDataLoading(true);
			const questionId = id!;
			try {
				const input = await quizClient.getTestCaseFromCloud(
					questionId,
					testCase._id
				);
				setEditingTestCase({
					...testCase,
					index,
					codeproginputparams: input,
				});
			} catch (error) {
				messageApi.error(String(error));
				console.error(error);
				setShowModal(false);
			} finally {
				setIsModelDataLoading(false);
			}
		},
		[loading, testCases, testCaseForm, messageApi]
	);

	const handleDelete = useCallback(
		async (testId: string) => {
			if (!id) {
				messageApi.error("Id is not present");
				return;
			}
			setLoading(true);
			try {
				const requestBody: DeleteTestCaseRequestData = {
					quesId: id,
					testId: testId,
				};
				const response = await quizClient.deleteTestCase(requestBody);
				if (response.error) {
					throw response.error;
				}
				setTestCases(prev => {
					const filtered = prev.filter(tc => tc._id !== testId);
					syncWithStore(filtered);
					return filtered;
				});
				messageApi.success("Test case deleted.");
			} catch (error) {
				messageApi.error(String(error));
				console.error(error);
			} finally {
				setLoading(false);
			}
		},
		[id, messageApi, syncWithStore]
	);

	const testCaseColumns: TableColumnsType<TestCase> = useMemo(
		() => [
			{
				key: "Serial Number",
				render: (_, __, index) => `${index + 1}.`,
				align: "right",
			},
			{
				key: "name",
				title: "Name",
				render: (_, __, index) => `Test case${index + 1}`,
				align: "left",
			},

			{
				key: "scoreip",
				title: "Score",
				dataIndex: "scoreip",
				align: "center",
				render: value => value ?? "NA",
			},
			{
				key: "sampleTest",
				title: "Sample Test Case",
				dataIndex: "sampleTest",
				align: "center",
				render: (value, record) => (
					<Switch
						loading={loadingStates[record._id] || false}
						checked={value}
						onChange={checked =>
							toggleSampleTestCase(checked, record._id)
						}
					/>
				),
			},
			{
				key: "actions",
				title: "Actions",
				align: "center",
				render: (_value, data) => {
					return (
						<Space size={1}>
							<Tooltip title="Edit">
								<Button
									type="text"
									icon={<EditOutlined />}
									onClick={() => openEditModal(data._id)}
								/>
							</Tooltip>
							<Tooltip title="Delete">
								<Button
									type="text"
									icon={<DeleteOutlined />}
									onClick={() => {
										if (loading) return;
										modal.confirm({
											title: "Delete Test case",
											content:
												"Are you sure you want to delete this test case?",
											onOk: () => handleDelete(data._id),
										});
									}}
								/>
							</Tooltip>
						</Space>
					);
				},
			},
		],
		[
			handleDelete,
			loading,
			loadingStates,
			modal,
			openEditModal,
			toggleSampleTestCase,
		]
	);

	return (
		<>
			{contextHolder}
			{modalContext}
			<Layout
				style={{
					width: "100%",
					height: "100%",
					padding: 16,
				}}
			>
				<Content>
					<div
						style={{
							height: "100%",
							overflow: "auto",
							backgroundColor: "#FBFBFB",
						}}
					>
						<DataTable<TestCase>
							columns={testCaseColumns}
							data={testCases}
							pagination={false}
						/>
					</div>
				</Content>

				<Footer
					style={{
						position: "sticky",
						bottom: "0px",
						margin: "auto",
						marginBottom: "-16px",
						padding: "16px 0px 24px",
						width: "100%",
						backgroundColor: "#f0f0f0",
						zIndex: 1,
					}}
				>
					<Flex align="center" gap={16}>
						<Button
							type="dashed"
							icon={<PlusOutlined />}
							onClick={() => {
								if (loading) return;
								setShowModal(true);
							}}
						>
							Add test case
						</Button>
						<Button
							type="primary"
							onClick={() => setActiveTabKey("solution")}
						>
							Next
						</Button>
					</Flex>
				</Footer>
			</Layout>

			{showModal && (
				<TestCaseModal
					title={editingTestCase ? "Edit Test Case" : "Add Test Case"}
					showModal={showModal}
					loading={loading}
					editingTestCase={editingTestCase}
					onCancel={() => {
						if (loading) return;
						setShowModal(false);
						setTimeout(() => {
							testCaseForm.resetFields();
							setEditingTestCase(null);
						}, 300);
					}}
					isModelDataLoading={isModelDataLoading}
					onSubmit={handleModalSubmit}
					testCases={testCases}
				/>
			)}
		</>
	);
};

export default AddTestCase;
