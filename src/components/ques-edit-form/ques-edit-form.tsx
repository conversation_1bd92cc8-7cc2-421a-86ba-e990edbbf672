import { quizClient } from "../../store";
import { <PERSON>ton, Flex, Form, Layout, Spin } from "antd";
import { useEffect, useMemo, useState } from "react";
import {
	CoodeComponent,
	QuestionAddReqBodyDataType,
	QuestionEditRoot,
} from "../question-layout/data";
import { Coding, MCQ, MQ, Subjective, Web } from "./ques-type";
import { QuestionSupportedValueToName } from "@/constants/languages";
import { useNavigate } from "react-router";
import { useQuestionStore } from "../../store";
import { RcFile } from "antd/es/upload";
import { QuestionType, QuestionTypesMap } from "@/client/test-add";
import { useAppMessage } from "../../hooks/message";
import useModal from "antd/es/modal/useModal";

export interface ChoicesDataType {
	txtOpt: string;
	chkOpt: boolean;
}

export interface QuestionFormFieldDataType {
	question: string;
	answer: string;
	score: number | undefined;
}

export interface TestCaseFormFiledDataType {
	description: string;
	evaluator: string;
	score: number | undefined;
}
export interface FormValuesDataType {
	type: string;
	difficultyLevel: string;
	score: number | undefined;
	negativeScore: number;
	txtQuesTitle: string;
	txttag: string[];
	code: {
		language: string;
		head: string;
		body: string;
		tail: string;
		solution?: string | undefined;
	}[];
	courseOutcomes: string | undefined;
	bloomTaxonomy: number | string | undefined;
	topic: string;
	isPremium: number;
	txtQues: string;
	choices: ChoicesDataType[];
	ismultianswer: boolean;
	txtSub: string;
	txtInputParams?: string;
	isFileUpload: number;
	upldFile?: RcFile[];
	mcqExplanation?: string;
	txtExpectedOutput?: string;
	scoreip?: number;
	languages: string[];
	questions: QuestionFormFieldDataType[];
	isHtmlAllowed: number;
	isCssAllowed: number;
	isJsAllowed: number;
	html: string;
	css: string;
	js: string;
	testcases: TestCaseFormFiledDataType[];
}

const QuesEditBaseForm = ({
	isFromTestModal,
	quesId,
	defaultQuesType,
	setDefaultQuesType,
	setActiveTabKey,
	setQuesId,
}: {
	isFromTestModal: boolean;
	quesId?: string;
	defaultQuesType: string;
	setDefaultQuesType: (_value: string) => void;
	setActiveTabKey: (_value: string) => void;
	setQuesId: (_value: string) => void;
}) => {
	const messageInstance = useAppMessage();
	const [formInstance] = Form.useForm<FormValuesDataType>();
	const [formDataValue, setFormDataValues] =
		useState<FormValuesDataType | null>();

	const navigate = useNavigate();
	const { initialQuestionData, setQuesData } = useQuestionStore();
	const [loading, setLoading] = useState<boolean>(false);
	const [modal, modalContextHolder] = useModal();

	const exportQuestionToCourse = async (
		instance: {
			destroy: () => void;
			update: (_config: {
				cancelButtonProps?: { disabled: boolean };
			}) => void;
		},
		id: string
	) => {
		try {
			instance.update({
				cancelButtonProps: { disabled: true },
			});
			const response = await quizClient.exportQuestion(id);
			console.log("Export response", response);
			if ("error" in response) {
				throw new Error(response.error);
			}
		} catch (error) {
			messageInstance?.error(
				error instanceof Error ? error.message : String(error)
			);
			console.log(error);
		} finally {
			instance.destroy();
		}
	};

	const submitForm = async (values: FormValuesDataType) => {
		if (!initialQuestionData) return;
		setLoading(true);
		const codecomponent = values?.code?.reduce<CoodeComponent>(
			(acc, item) => {
				acc[item.language] = {
					lang: QuestionSupportedValueToName[
						item.language as keyof typeof QuestionSupportedValueToName
					].toString(),
					head: item.head,
					body: item.body,
					tail: item.tail,
					solution: item.solution ?? undefined,
				};
				return acc;
			},
			{}
		);
		const baseData: QuestionAddReqBodyDataType = {
			txtQuesTitle: values.txtQuesTitle,
			type: values.type,
			difficultyLevel: values.difficultyLevel,
			score: values.score ?? 0,
			negativeScore: values.negativeScore ?? null,
			txttag: Array.isArray(values.txttag)
				? values.txttag.join(",")
				: values.txttag,
			courseOutcomes: values.courseOutcomes,
			bloomTaxonomy: values.bloomTaxonomy ?? undefined,
			topic: values.topic ?? null,
			referenceLinks: "",
			isPremium: values.isPremium ? 1 : 0,
			txtQues: values.txtQues,
			codecomponent: JSON.stringify(codecomponent ?? {}),
		};

		if ("quesId" in initialQuestionData) {
			baseData.quesId = initialQuestionData.quesId;
		}

		let data: QuestionAddReqBodyDataType = { ...baseData };
		switch (defaultQuesType) {
			case QuestionType.MCQ:
				data = {
					...baseData,
					executionTime:
						"ques" in initialQuestionData
							? (initialQuestionData.ques.executionTime ?? "")
							: "",
					funcType: 0,
					funcName: "",
					upldFile: [],
					mcqExplanation: "<p><br></p>",
					...values.choices?.reduce(
						(acc, choice, index) => {
							const { chkOpt, ...rest } = choice;
							if (chkOpt) {
								return {
									...acc,
									[`txtOpt${index + 1}`]: rest.txtOpt.trim(),
									[`chkOpt${index + 1}`]: "on",
								};
							}
							return {
								...acc,
								[`txtOpt${index + 1}`]: rest.txtOpt,
							};
						},
						{} as Record<string, unknown>
					),
				};
				break;

			case QuestionType.SUBJECTIVE:
				data = {
					...baseData,
					isFileUpload: values.isFileUpload ? 1 : 0,
					txtSub: values.txtSub,
				};
				break;

			case QuestionType.CODING:
				data = {
					...baseData,
					isFileUpload: values.isFileUpload ? 1 : 0,
					tutorialLink: "",
					progLang: Array.isArray(values.languages)
						? values.languages.join(",")
						: values.languages,
					bigTestCase: false,
					api: true,
					txtInputParams: values.txtInputParams ?? "",
					txtExpectedOutput: values.txtExpectedOutput ?? undefined,
					scoreip: values.scoreip ?? undefined,
					upldFile: values.upldFile ?? [],
				};
				break;

			case QuestionType.MULTIPLE:
				data = {
					...baseData,
					executionTime:
						"ques" in initialQuestionData
							? (initialQuestionData.ques.executionTime ?? "")
							: "",
					funcType: 0,
					funcName: "",
					upldFile: [],
					...values?.questions?.reduce(
						(acc, ques, index) => {
							return {
								...acc,
								[`txtInputParams${index || ""}`]: ques.question,
								[`txtExpectedOutput${index || ""}`]:
									ques.answer,
								[`scoreip${index || ""}`]: ques.score,
							};
						},
						{} as Record<string, unknown>
					),
				};
				break;

			case QuestionType.WEB:
				data = {
					...baseData,
					txtOpt1: "",
					txtOpt2: "",
					executionTime:
						"ques" in initialQuestionData
							? (initialQuestionData.ques.executionTime ?? "")
							: "",
					isHtmlAllowed: values.isHtmlAllowed,
					isCssAllowed: values.isCssAllowed,
					isJsAllowed: values.isJsAllowed,
					html:
						values.html ??
						("ques" in initialQuestionData
							? initialQuestionData.ques?.questionTypeWeb?.html
							: "<!--write your HTML code here-->"),
					css:
						values.css ??
						("ques" in initialQuestionData
							? initialQuestionData.ques?.questionTypeWeb?.css
							: "/*write your CSS code here*/"),
					js:
						values.js ??
						("ques" in initialQuestionData
							? initialQuestionData.ques?.questionTypeWeb?.js
							: "/*write your JS code here*/"),
					upldFile: [],
					...values?.testcases?.reduce(
						(acc, testcase, index) => {
							return {
								...acc,
								[`txtExpectedOutput${index || ""}`]:
									testcase.evaluator,
								[`txtInputParams${index || ""}`]:
									testcase.description,
								[`scoreip${index || ""}`]: testcase.score,
							};
						},
						{} as Record<string, unknown>
					),
				};
				break;
		}
		if (defaultQuesType === QuestionType.MCQ) {
			const trueCount = values.choices.reduce(
				(count, choice) => count + (choice?.chkOpt ? 1 : 0),
				0
			);
			if (trueCount === 0) {
				messageInstance?.error("Please select at least 1 choice");
				setLoading(false);
				return;
			}
			if (values.ismultianswer && trueCount <= 1) {
				messageInstance?.error("Please select at least 2 choices");
				setLoading(false);
				return;
			}
		}
		if (defaultQuesType === QuestionType.WEB) {
			if (
				!(
					values.isHtmlAllowed ||
					values.isCssAllowed ||
					values.isJsAllowed
				)
			) {
				messageInstance?.error(
					"Atleast one language required in web question"
				);
				setLoading(false);
				return;
			}
		}
		if (defaultQuesType !== QuestionType.CODING && quesId === undefined) {
			const instance = modal.confirm({
				title: "Want to save on course side as well?",
				okText: "Yes",
				cancelText: "No",
				onOk: () => handleExportSave(instance, data),
				onCancel: () => normalSave(data),
			});
		} else {
			normalSave(data);
		}
		setLoading(false);
	};

	const handleExportSave = async (
		instance: {
			destroy: () => void;
			update: (_config: {
				cancelButtonProps?: { disabled: boolean };
			}) => void;
		},
		data: QuestionAddReqBodyDataType
	) => {
		try {
			const res = await addQuestion(data);
			if (res?.quesDetail?.ques?._id) {
				await exportQuestionToCourse(instance, res.quesDetail.ques._id);
				handleAfterSave(res);
			}
		} catch (error) {
			console.error("Error adding question:", error);
		}
	};

	const normalSave = async (data: QuestionAddReqBodyDataType) => {
		const res = await addQuestion(data);
		if (res) {
			handleAfterSave(res);
		}
	};

	const addQuestion = async (data: QuestionAddReqBodyDataType) => {
		try {
			const response = await quizClient.questAdd(
				data,
				quesId,
				initialQuestionData?.quizId,
				initialQuestionData?.segmentIndex
			);

			if (!response || "error" in response) {
				throw new Error(response?.error || "Unable to add question");
			}

			return response;
		} catch (error) {
			messageInstance?.error(
				error instanceof Error ? error.message : String(error)
			);
			console.error(error);
		}
	};

	const handleAfterSave = (response: QuestionEditRoot) => {
		console.log("Response after save", quesId);
		if (!quesId) {
			messageInstance?.success("Question added successfully");
		} else {
			messageInstance?.success("Question updated successfully");
		}
		if (response.quesDetail.ques._id) {
			const isCoding =
				QuestionTypesMap[
					response.quesDetail.ques
						.type as keyof typeof QuestionTypesMap
				] === "Coding";
			setQuesId(response.quesDetail.ques._id);

			if (isCoding) {
				setActiveTabKey("testcase");
			}

			const navigatePath = isCoding
				? `/questions/add/${response.quesDetail.ques._id}?tabId=testcase`
				: `/questions`;

			if (!isFromTestModal) {
				navigate(navigatePath);
			}
			setFormDataValues(null);
		}
		if (quesId) {
			setQuesData(response.quesDetail);
		}
	};

	const initialQuestionDataValues: FormValuesDataType = useMemo(() => {
		const ques =
			"ques" in initialQuestionData
				? initialQuestionData.ques
				: undefined;
		setDefaultQuesType(ques?.type || defaultQuesType);
		return {
			// Common
			txtQuesTitle: ques?.title || "",
			type: defaultQuesType,
			difficultyLevel: ques?.difficultyLevel || "0",
			score: ques?.score ?? undefined,
			txttag: ques?.tags?.length ? ques.tags.split(",") : [],
			isPremium: ques?.isPremium ? 1 : 0,
			txtQues: ques?.text ?? "",

			// Chitkara
			courseOutcomes: ques?.courseOutcomes
				? ques.courseOutcomes
				: Object.keys(initialQuestionData?.questionOutcomes || {})[0],
			bloomTaxonomy: ques?.bloomTaxonomy
				? Object.keys(initialQuestionData?.questionBloomTaxonomy || {})[
						Number(ques.bloomTaxonomy) - 1
					]
				: Object.keys(
						initialQuestionData?.questionBloomTaxonomy || {}
					)[0],
			topic: ques?.topic ?? "",

			// Coding
			languages:
				ques?.questionTypeCoding?.codeproglang?.map(
					lang => lang.language
				) || [],
			code:
				ques?.questionTypeCoding?.codeproglang?.map(lang => ({
					...lang.codeComponents,
					language: lang.language,
				})) || [],

			// MCQ
			negativeScore: ques?.negativeScore ?? 0,
			ismultianswer:
				(ques?.questionTypeMCQ?.correctAnswers?.length ?? 0) > 1,
			choices: ques?.questionTypeMCQ?.options?.map((option, index) => ({
				txtOpt: option,
				chkOpt:
					ques?.questionTypeMCQ?.correctAnswers?.includes(index) ||
					false,
			})) || [
				{ txtOpt: "", chkOpt: false },
				{ txtOpt: "", chkOpt: false },
			],

			// MQ
			questions: ques?.questionTypeCoding?.testCase?.map(testcase => {
				return {
					question: testcase.codeproginputparams,
					answer: testcase.codeprogexpectedoutput,
					score: Number(testcase.scoreip),
				};
			}) || [{ question: "", answer: "", score: undefined }],

			//  Web
			isHtmlAllowed: ques?.questionTypeWeb?.isHtmlAllowed ? 1 : 0,
			isCssAllowed: ques?.questionTypeWeb?.isCssAllowed ? 1 : 0,
			isJsAllowed: ques?.questionTypeWeb?.isJsAllowed ? 1 : 0,
			html:
				ques?.questionTypeWeb?.html ||
				"<!--write your HTML code here-->",
			css: ques?.questionTypeWeb?.css || "/* write your CSS code here */",
			js: ques?.questionTypeWeb?.js || "// write your JS code here",
			testcases:
				ques?.questionTypeWeb?.testCase?.map(testcase => {
					return {
						evaluator: testcase.evaluator,
						description: testcase.description,
						score: Number(testcase.scoreip),
					};
				}) || [],

			// Subjective
			txtSub: ques?.questionTypeSubjective?.subjectiveanswer || "",
			isFileUpload: ques?.questionTypeSubjective?.isFileUpload ? 1 : 0,
		};
	}, [defaultQuesType, initialQuestionData, setDefaultQuesType]);

	useEffect(() => {
		const values = formDataValue || initialQuestionDataValues;
		if (values) {
			formInstance.setFieldsValue(values);
		}
	}, [formInstance, formDataValue, initialQuestionDataValues]);

	return (
		<>
			{modalContextHolder}
			{loading && (
				<div
					style={{
						position: "absolute",
						top: "50%",
						left: "50%",
						transform: "translate(-50%, -50%)",
						display: "flex",
						justifyContent: "center",
						alignItems: "center",
						zIndex: 9999,
					}}
				>
					<Spin />
				</div>
			)}
			{initialQuestionData && (
				<Form
					form={formInstance}
					layout="vertical"
					size="middle"
					scrollToFirstError
					initialValues={initialQuestionDataValues}
					onFinish={submitForm}
					onValuesChange={(_, allValues) => {
						if (allValues.type !== defaultQuesType) {
							if (
								allValues.type === QuestionType.CODING ||
								allValues.type === QuestionType.MULTIPLE
							) {
								allValues.score = undefined;
							}
							setFormDataValues(allValues);
							setDefaultQuesType(allValues.type);
						}
					}}
					style={{ margin: "auto", padding: 16, maxWidth: "1400px" }}
				>
					<Layout>
						<Layout.Content>
							<Flex vertical gap={16}>
								{defaultQuesType === QuestionType.MCQ && (
									<MCQ
										formInstance={formInstance}
										quesId={quesId}
									/>
								)}
								{defaultQuesType ===
									QuestionType.SUBJECTIVE && (
									<Subjective
										formInstance={formInstance}
										quesId={quesId}
									/>
								)}
								{defaultQuesType === QuestionType.CODING && (
									<Coding
										formInstance={formInstance}
										quesId={quesId}
									/>
								)}
								{defaultQuesType === QuestionType.MULTIPLE && (
									<MQ
										formInstance={formInstance}
										quesId={quesId}
									/>
								)}
								{defaultQuesType === QuestionType.WEB && (
									<Web
										formInstance={formInstance}
										quesId={quesId}
									/>
								)}
							</Flex>
						</Layout.Content>
						<Layout.Footer style={{ padding: "16px 0px" }}>
							<Form.Item>
								<Flex gap={16} align="center">
									<Button
										type="primary"
										htmlType="submit"
										loading={loading}
									>
										{quesId ||
										defaultQuesType !== QuestionType.CODING
											? "Save"
											: "Next"}
									</Button>
									{defaultQuesType === QuestionType.CODING &&
									!isFromTestModal ? (
										<Button
											type="dashed"
											onClick={() => {
												navigate("/questions");
											}}
										>
											Cancel
										</Button>
									) : (
										""
									)}
								</Flex>
							</Form.Item>
						</Layout.Footer>
					</Layout>
				</Form>
			)}
		</>
	);
};

export default QuesEditBaseForm;
