import { validateTitle } from "@/components/question-layout/data";
import { Form, Input } from "antd";

const Title = ({ regex }: { regex: string }) => {
	return (
		<>
			<Form.Item
				label="Question Title"
				name="txtQuesTitle"
				rules={[
					{
						required: true,
						validator: (_, value) => validateTitle(_, value, regex),
					},
				]}
				style={{ marginBottom: "1em" }}
			>
				<Input
					size="large"
					placeholder="Enter question title"
					count={{
						show: false,
						max: 125,
						exceedFormatter: (txt, { max }) => txt.slice(0, max),
					}}
				/>
			</Form.Item>
		</>
	);
};

export default Title;
