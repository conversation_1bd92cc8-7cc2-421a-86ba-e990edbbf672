import { Form, Select } from "antd";
import { QuestionTypeOptions } from "@/components/question-layout/data";

const QuestionType = ({
	quesId,
	questionTypeOptions,
}: {
	quesId?: string;
	questionTypeOptions: QuestionTypeOptions;
}) => {
	return (
		<Form.Item
			label="Question Type"
			name="type"
			rules={[
				{
					required: true,
					message: "Please select question type",
				},
			]}
			style={{ marginBottom: "1em" }}
			tooltip={{
				title: "Select what type of question is this.",
			}}
		>
			<Select
				placeholder="Select question type"
				disabled={quesId ? true : false}
			>
				{Object.keys(questionTypeOptions || {})?.map(key => {
					const typedKey = key as keyof typeof questionTypeOptions;
					return (
						<Select.Option
							key={typedKey}
							value={questionTypeOptions[typedKey]?.value}
						>
							{questionTypeOptions[typedKey]?.label}
						</Select.Option>
					);
				})}
			</Select>
		</Form.Item>
	);
};

export default QuestionType;
