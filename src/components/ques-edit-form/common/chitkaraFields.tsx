import { PlatformOrganisation } from "@/constants";
import { Col, Form, Input, Row, Select } from "antd";
import {
	QuestionBloomTaxonomy,
	QuestionOutcomes,
} from "@/components/question-layout/data";
import { AppConfig } from "@/config";

const ChitkaraFields = ({
	questionOutcomes,
	questionBloomTaxonomy,
}: {
	questionOutcomes: QuestionOutcomes;
	questionBloomTaxonomy: QuestionBloomTaxonomy;
}) => {
	return (
		<>
			{AppConfig.platformOrganisation ===
				PlatformOrganisation.CHITKARA && (
				<Row gutter={[32, 32]}>
					<Col span={8}>
						<Form.Item
							label="Outcome"
							name="courseOutcomes"
							rules={[
								{
									required: true,
									message: "Please select question outcome",
								},
							]}
							tooltip={{
								title: "What should be course outcome.",
							}}
							style={{ marginBottom: "1em" }}
						>
							<Select
								options={Object.entries(
									questionOutcomes || {}
								).map(([key, value]) => ({
									label: key,
									value: value,
								}))}
							/>
						</Form.Item>
					</Col>

					<Col span={8}>
						<Form.Item
							label="Bloom's Taxonomy"
							name="bloomTaxonomy"
							rules={[
								{
									required: true,
								},
							]}
							tooltip={{
								title: "<PERSON>’s Taxonomy is a hierarchical model that categorizes learning objectives into varying levels of complexity, from basic knowledge and comprehension to advanced evaluation and creation.",
							}}
							style={{ marginBottom: "1em" }}
						>
							<Select
								options={Object.entries(
									questionBloomTaxonomy ?? {}
								).map(([key, value]) => {
									return {
										label: value,
										value: key,
									};
								})}
							/>
						</Form.Item>
					</Col>

					<Col span={8}>
						<Form.Item
							label="Topic"
							name="topic"
							tooltip={{
								title: "Which topic this question belongs to.",
							}}
							style={{ marginBottom: "1em" }}
						>
							<Input placeholder="Topic" />
						</Form.Item>
					</Col>
				</Row>
			)}
		</>
	);
};

export default ChitkaraFields;
