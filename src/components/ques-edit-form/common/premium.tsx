import SwitchCard from "@/components/form/switch-card";
import { useAppStore } from "../../../store";

import { FormInstance } from "antd";

const Premium = ({ formInstance }: { formInstance: FormInstance }) => {
	const { session } = useAppStore();

	return (
		<>
			{session && (session.role === "0" || session.role === "4") && (
				<SwitchCard
					form={formInstance}
					name="isPremium"
					labelTitle="Premium"
					labelDescription="Mark question as premium question."
				/>
			)}
		</>
	);
};

export default Premium;
