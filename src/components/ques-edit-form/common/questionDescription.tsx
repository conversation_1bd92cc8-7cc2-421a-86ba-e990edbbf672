import { Form } from "antd";
import { useRef } from "react";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";

const customMenu = [
	[{ header: [1, 2, 3, 4, 5, 6, false] }],
	[{ indent: "-1" }, { indent: "+1" }],
	["bold", "italic", "underline", "strike"],
	["code-block"],
	["link", "image"],
	["clean"],
];

const QuestionDescription = () => {
	const quillRef = useRef<ReactQuill | null>(null);

	return (
		<Form.Item
			label="Question Description"
			rules={[
				{
					required: true,
					message: "Please enter Question Description.",
				},
			]}
			name="txtQues"
			tooltip={{
				title: "Includes any text or info which help candidate to understand the question in detail.",
			}}
			style={{ marginBottom: "1em" }}
			className="bound"
		>
			<ReactQuill
				ref={quillRef}
				theme="snow"
				bounds=".bound"
				style={{
					height: "250px",
					paddingBottom: 45,
				}}
				modules={{
					toolbar: customMenu,
				}}
			/>
		</Form.Item>
	);
};

export default QuestionDescription;
