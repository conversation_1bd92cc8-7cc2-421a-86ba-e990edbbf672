import type { DifficultyLevelDataType } from "@/components/question-layout/data";
import { Form, Select } from "antd";

const DifficultyLevel = ({
	difficultyLevel,
}: {
	difficultyLevel: DifficultyLevelDataType;
}) => {
	return (
		<>
			<Form.Item
				label="Level of Difficulty"
				name="difficultyLevel"
				rules={[
					{
						required: true,
						message: "Please select Difficulty Level",
					},
				]}
				style={{ marginBottom: "1em" }}
			>
				<Select>
					{Object.entries(difficultyLevel || {}).map(
						([key, value]) => {
							const typedKey =
								key as keyof typeof difficultyLevel;
							return (
								<Select.Option key={typedKey} value={value}>
									{key}
								</Select.Option>
							);
						}
					)}
				</Select>
			</Form.Item>
		</>
	);
};

export default DifficultyLevel;
