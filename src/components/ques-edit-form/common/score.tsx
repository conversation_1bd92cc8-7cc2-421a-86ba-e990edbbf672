import { Form, FormInstance, InputNumber } from "antd";

const Score = ({
	isDisable,
	required = true,
	formInstance,
}: {
	isDisable?: boolean;
	required?: boolean;
	formInstance?: FormInstance;
}) => {
	return (
		<Form.Item
			label="Score"
			name="score"
			rules={[
				{
					required,
					validator: (_, value) => {
						if (!required || isDisable) return Promise.resolve();
						if (
							(value === null ||
								value === undefined ||
								value === "") &&
							value != 0
						) {
							return Promise.reject(
								new Error(
									"Please enter the score of the question."
								)
							);
						}
						if (value < 0 || value > 9999) {
							return Promise.reject(
								new Error("Score must be between 0 and 9999.")
							);
						}

						if (!formInstance) return Promise.resolve();
						const formData = formInstance?.getFieldsValue();
						if (value < formData?.negativeScore) {
							return Promise.reject(
								new Error(
									"Score should be greater than or equal to negative score"
								)
							);
						}
						return Promise.resolve();
					},
				},
			]}
		>
			<InputNumber
				placeholder="Score"
				disabled={isDisable}
				controls={false}
				style={{ width: "100%" }}
			/>
		</Form.Item>
	);
};

export default Score;
