import { useAppMessage } from "@/hooks/message";
import { testEmailRegex } from "@/testList/common/common";
import { Button, Flex, Form, Modal, Typography } from "antd";
import { useForm } from "antd/es/form/Form";
import { useState } from "react";
import Tags from "../tags";
import { quizClient } from "../../store/index";

const ShareTest = ({
	onClose,
	isShareTestOpen,
	quizId,
}: {
	onClose: () => void;
	isShareTestOpen: boolean;
	quizId?: string;
}) => {
	const [form] = useForm();
	const messageInstance = useAppMessage();
	const [loading, setLoading] = useState<boolean>(false);

	function validateEmails(_rule: unknown, value: string[]): Promise<void> {
		if (!value || value.length === 0) {
			return Promise.reject(new Error("Please Add Email first"));
		}
		for (const email of value) {
			if (!testEmailRegex.test(email)) {
				return Promise.reject(
					new Error(`"${email}" is not a valid e-mail address!`)
				);
			}
		}
		return Promise.resolve();
	}

	const handleUpload = async (values: { emails: string[] }) => {
		if (!quizId) {
			messageInstance?.error("Test not found");
			return;
		}
		setLoading(true);
		try {
			const response = await quizClient.shareTest(values.emails, quizId);
			if (response.userWhereEmailNotSent?.length) {
				form.setFieldValue("emails", response.userWhereEmailNotSent);
				throw new Error(
					`Remaining emails are not either in same organization or invalid please check and try again.`
				);
			}
			messageInstance?.success("Invite Sent.");
			onClose();
		} catch (ex) {
			messageInstance?.error(
				ex instanceof Error ? ex.message : String(ex)
			);
			console.log(ex);
		} finally {
			setLoading(false);
		}
	};

	return (
		<>
			<Modal
				open={isShareTestOpen}
				title="Share Test"
				onCancel={onClose}
				footer={[
					<Button
						key="submit"
						type="primary"
						loading={loading}
						onClick={form.submit}
					>
						Send
					</Button>,
				]}
				destroyOnClose={true}
			>
				<Flex vertical gap="middle">
					<Form name="basic" form={form} onFinish={handleUpload}>
						<Form.Item
							label="Emails"
							name="emails"
							rules={[{ validator: validateEmails }]}
						>
							<Tags separator={/[ ,;]+/} max={500} />
						</Form.Item>
					</Form>
					<Typography.Text>
						A preview and clone link for test will be emailed to the
						user.
					</Typography.Text>
				</Flex>
			</Modal>
		</>
	);
};

export default ShareTest;
