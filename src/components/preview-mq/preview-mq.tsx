import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Flex, Form, Input, Tag, Typography } from "antd";
import "./attempt-mq.scoped.css";
import { useCallback, useMemo, useState } from "react";
import { QuestionTypeCoding } from "@/testReport/data/data";
import { CheckOutlined, CloseOutlined } from "@ant-design/icons";

interface MQFormValues {
	[key: number]: string;
}

interface AttemptMQProps {
	questionData?: QuestionTypeCoding;
}

export default function PreviewMQ(props: AttemptMQProps) {
	const { questionData } = props;
	const [form] = Form.useForm();

	const [answers, setAnswers] = useState<
		({ correctAnswer: string; isCorrect: boolean } | undefined)[]
	>([]);

	const questions = useMemo(
		() => questionData?.testCase ?? [],
		[questionData]
	);

	const handleMQFormSubmit = useCallback(
		async function (values: MQFormValues) {
			const answers = questions.map((question, index) => {
				const correctAnswer = question.codeprogexpectedoutput;
				return {
					correctAnswer,
					isCorrect: values[index] === correctAnswer,
				};
			});
			setAnswers(answers);
		},
		[questions, setAnswers]
	);

	return (
		<div className="attempt-mq-container">
			<div>
				<span className="info-text">Answer below:</span>
				<Divider
					style={{
						margin: "0.5rem 0 1.5rem 0",
						width: "50%",
						minWidth: "unset",
					}}
				/>
				<div>
					<Form
						form={form}
						layout="vertical"
						size="middle"
						onFinish={handleMQFormSubmit}
						style={{
							display: "flex",
							flexDirection: "column",
							gap: "1.5em",
						}}
					>
						{questionData?.testCase?.map((testCase, index) => (
							<Flex gap={4}>
								<div>
									<Tag
										bordered={false}
										color="orange"
										style={{ borderRadius: "50%" }}
									>
										{index + 1}
									</Tag>
								</div>
								<Form.Item
									key={testCase._id}
									name={index}
									style={{ width: "100%" }}
									label={
										<pre>
											{testCase.codeproginputparams}
										</pre>
									}
								>
									<Flex gap={16}>
										<Input.TextArea
											placeholder="User's answer"
											autoSize={{
												minRows: 3,
												maxRows: 5,
											}}
										/>
										<Input.TextArea
											value={
												answers[index]?.correctAnswer
											}
											placeholder="Correct answer"
											autoSize={{
												minRows: 3,
												maxRows: 5,
											}}
											readOnly
										/>
										{answers[index] && (
											<Typography.Text
												type={
													answers[index].isCorrect
														? "success"
														: "danger"
												}
												style={{ placeSelf: "center" }}
											>
												{answers[index].isCorrect ? (
													<CheckOutlined />
												) : (
													<CloseOutlined />
												)}
											</Typography.Text>
										)}
									</Flex>
								</Form.Item>
							</Flex>
						))}
					</Form>
				</div>
			</div>
			<div className="submit-mq-container">
				<Button
					type="primary"
					onClick={() => form.submit()}
					style={{ marginTop: "1.5em" }}
				>
					Submit
				</Button>
			</div>
		</div>
	);
}
