div.attempt-mq-container {
	flex: auto;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	padding: 1rem 1.5rem;
	overflow: auto;
}

div.attempt-mq-container > div.submit-mq-container {
	display: flex;
	justify-content: flex-end;
}

div.submit-mq-container > button {
	border-radius: 5px;
	font-family: "Hind", sans-serif;
	padding: 0.5rem 2rem;
}

span.info-text {
	font-size: 20px;
	font-family: "Hind", sans-serif;
	font-weight: 500;
}

div.attempt-mq-container /deep/ form > div * {
	font-family: "Hind", sans-serif;
}

div.attempt-mq-container form /deep/ label::before {
	display: none !important;
}

div.attempt-mq-container form /deep/ textarea {
	border: 1px solid #d1d1d1;
}

div.attempt-mq-container form /deep/ div.ant-form-item-control-input {
	max-width: 60%;
	margin-left: 35px;
}

div.attempt-mq-container
	form
	/deep/
	div.ant-form-item-explain.ant-form-item-explain-error {
	margin-left: 35px;
}

div.attempt-mq-container /deep/ form div.mq-details-container {
	display: flex;
}

div.mq-details-container > div.mq-index {
	flex: 0 0 35px;
	display: flex;
}

div.mq-index > span {
	background-color: var(--primary-light);
	color: var(--primary-color);
	padding: 0.25rem;
	border-radius: 20px;
	height: 20px;
	width: 20px;
	display: flex;
	justify-content: center;
	align-items: center;
	font-weight: 600;
}
