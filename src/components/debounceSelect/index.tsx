import { Select, SelectProps, Spin } from "antd";
import { useEffect, useMemo, useRef, useState } from "react";
import debounce from "lodash/debounce";
import { DefaultOptionType } from "antd/es/select";

export interface DebounceSelectProps<V extends ValueType>
	extends Omit<SelectProps<V | V[]>, "options" | "children"> {
	fetchOptions: (_search: string) => Promise<V[]>;
	debounceTimeout?: number;
	defaultOptions?: V[];
}

type ValueType = DefaultOptionType;
export default function DebounceSelect<V extends ValueType>({
	fetchOptions,
	debounceTimeout = 800,
	defaultOptions = [],
	...props
}: DebounceSelectProps<V>) {
	const [fetching, setFetching] = useState(false);
	const [options, setOptions] = useState<ValueType[]>([...defaultOptions]);
	const fetchRef = useRef(0);

	const debounceFetcher = useMemo(() => {
		const loadOptions = (value: string) => {
			fetchRef.current += 1;
			const fetchId = fetchRef.current;
			setOptions([]);
			setFetching(true);

			fetchOptions(value).then(newOptions => {
				if (fetchId !== fetchRef.current) {
					return;
				}
				setOptions(newOptions);
				setFetching(false);
			});
		};

		return debounce(loadOptions, debounceTimeout);
	}, [fetchOptions, debounceTimeout]);

	useEffect(() => {
		setOptions([...defaultOptions]);
	}, [defaultOptions]);

	return (
		<Select
			labelInValue
			filterOption={false}
			onSearch={debounceFetcher}
			notFoundContent={fetching ? <Spin size="small" /> : "No data"}
			{...props}
			options={options}
			style={{ width: "100%" }}
		/>
	);
}
