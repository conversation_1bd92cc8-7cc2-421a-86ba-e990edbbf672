import TestAddClient, {
	EndPointRequestBody,
	EndPointResponseBody,
	GetEndPointsMap,
	PostEndPointsMap,
} from "@/client/test-add";
import { DataTable } from "@/components/dataTable/dataTable";
import Tags from "@/components/tags";
import { AppConfig } from "@/config";
import {
	Button,
	message,
	TableColumnsType,
	Modal,
	Flex,
	Form,
	Tooltip,
	Space,
	Layout,
	Tag,
	Typography,
	Empty,
	Input,
	Popconfirm,
} from "antd";
import { Content, Header } from "antd/es/layout/layout";
import Search from "antd/es/input/Search";
import {
	EditOutlined,
	DeleteOutlined,
	PlusOutlined,
	SearchOutlined,
	LoadingOutlined,
	ExportOutlined,
	UploadOutlined,
} from "@ant-design/icons";
import { memo, useCallback, useEffect, useMemo, useState } from "react";
import { useNavigate, useParams } from "react-router";
import { ValidatorRule } from "rc-field-form/lib/interface";
import AddBulkRoomModal from "../bulk-upload-modal/AddBulkRoomModal";

const testAPI = new TestAddClient(AppConfig.quizServerURL);
type ResponseDataType = EndPointResponseBody<
	GetEndPointsMap["/test/quizRoom/:testId"]
>;

type addAndUpdateReqRoomDataType = EndPointRequestBody<
	PostEndPointsMap["/test/updateQuizRoom"]
>;

type addAndUpdateResRoomDataType = EndPointResponseBody<
	PostEndPointsMap["/test/updateQuizRoom"]
>;

export type userDataType = ResponseDataType["userData"][number];
type roomDataType = ResponseDataType["rooms"][number] & {
	_id: string;
	students: userDataType[];
	invigilators: userDataType[];
};

function roomNameReplacer(roomName: string, quizId: string) {
	return `${roomName.replaceAll(`-${quizId}`, "")}`;
}

export const roomTitleRegex = /[^A-z0-9_-]/;
export const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

const validateEmail: ValidatorRule["validator"] = (_rule, emails: string[]) => {
	if (!emails) return Promise.resolve();
	for (const email of emails) {
		if (!emailRegex.test(email)) {
			return Promise.reject("Please enter a valid email address!");
		}
	}
	return Promise.resolve();
};

const validateRoomName: ValidatorRule["validator"] = (_rule, value: string) => {
	if (!value) return Promise.reject(new Error("Room name is required!"));
	else if (roomTitleRegex.test(value)) {
		return Promise.reject(new Error("Room name is invalid!"));
	}
	return Promise.resolve();
};

const PreviewUsersModal = memo(
	({
		roomName,
		title,
		data,
		roomId,
		quizId,
		searchValue,
		updateQuizRoom,
	}: {
		roomName: string;
		title: string;
		roomId: string;
		quizId: string;
		searchValue: string;
		data: userDataType[];
		updateQuizRoom: (
			_data: addAndUpdateReqRoomDataType,
			_title: string
		) => Promise<boolean>;
	}) => {
		const [form] = Form.useForm();
		const [isModalOpen, setIsModalOpen] = useState(false);
		const [search, setSearch] = useState(searchValue ?? "");
		const [modalData, setModalData] = useState<
			(userDataType & { inEditMode?: boolean; editedEmail?: string })[]
		>([]);
		const [allUserData, setAllUserData] = useState<
			(userDataType & { inEditMode?: boolean; editedEmail?: string })[]
		>([]);

		const roomNameReplaced = roomNameReplacer(roomName, quizId);
		useEffect(() => {
			const fullList = (data ?? []).map(user => ({
				...user,
				inEditMode: false,
				editedEmail: user.email,
			}));
			setAllUserData(fullList);
			setModalData(fullList);
		}, [data]);

		const handleSearch = useCallback(
			(value: string) => {
				if (value === search) return;
				const trimmedValue = value.trim().toLowerCase();
				setModalData(
					trimmedValue === ""
						? allUserData
						: allUserData.filter(user =>
								user.email.toLowerCase().includes(trimmedValue)
							)
				);
				setSearch(value);
			},
			[allUserData, search]
		);

		const removeTag = (email: string) => {
			setAllUserData(prevData =>
				prevData.filter(user => user.email !== email)
			);
			setModalData(prevData =>
				prevData.filter(user => user.email !== email)
			);
		};

		const editTag = (email: string) => {
			setAllUserData(prevData =>
				prevData.map(user =>
					user.email === email
						? { ...user, inEditMode: true, editedEmail: user.email }
						: user
				)
			);
			setModalData(prevData =>
				prevData.map(user =>
					user.email === email
						? { ...user, inEditMode: true, editedEmail: user.email }
						: user
				)
			);
		};

		const saveEditedTag = (email: string) => {
			setAllUserData(prevData =>
				prevData.map(user =>
					user.email === email
						? {
								...user,
								email: user.editedEmail || user.email,
								inEditMode: false,
							}
						: user
				)
			);
			setModalData(prevData =>
				prevData.map(user =>
					user.email === email
						? {
								...user,
								email: user.editedEmail || user.email,
								inEditMode: false,
							}
						: user
				)
			);
		};

		const handleInputChange = (email: string, newValue: string) => {
			setAllUserData(prevData =>
				prevData.map(user =>
					user.email === email
						? { ...user, editedEmail: newValue }
						: user
				)
			);
			setModalData(prevData =>
				prevData.map(user =>
					user.email === email
						? { ...user, editedEmail: newValue }
						: user
				)
			);
		};

		const onFinish = async (values: { [key: string]: string[] }) => {
			const invigilatorEmails: string[] = [];
			const userEmails: string[] = [];

			allUserData.forEach(user => {
				if (title === "Invigilators") {
					invigilatorEmails.push(user.email);
				} else {
					userEmails.push(user.email);
				}
			});

			if (values.Invigilators) {
				invigilatorEmails.push(...values.Invigilators);
			}

			if (values.Students) {
				userEmails.push(...values.Students);
			}

			const data = {
				roomId,
				roomName: roomNameReplaced,
				invigilatorEmails,
				userEmails,
			};
			const success = await updateQuizRoom(data, title);
			if (success) {
				setIsModalOpen(false);
				if (title === "Invigilators") {
					form.resetFields(["Invigilators"]);
				} else {
					form.resetFields(["Students"]);
				}
			}
		};

		useEffect(() => {
			handleSearch(searchValue);
		}, [searchValue]);

		return (
			<>
				<Button type="link" onClick={() => setIsModalOpen(true)}>
					{data?.length}
				</Button>
				<Modal
					title={
						<Flex
							align="center"
							gap="middle"
							style={{ width: "80%" }}
						>
							<Typography.Text
								ellipsis
								style={{ maxWidth: "50%" }}
							>
								{roomNameReplaced} {title}
							</Typography.Text>
							<Search
								style={{ width: "50%" }}
								size="middle"
								value={search}
								placeholder={`Search ${title}...`}
								allowClear
								onChange={e => handleSearch(e.target.value)}
							/>
						</Flex>
					}
					open={isModalOpen}
					onOk={() => form.submit()}
					okText="Save"
					onCancel={() => {
						setIsModalOpen(false);
					}}
					width={{
						sm: "75%",
						md: "60%",
						lg: "50%",
						xl: "40%",
						xxl: "30%",
					}}
					styles={{
						body: {
							maxHeight: "60vh",
							overflow: "auto",
						},
					}}
				>
					<Flex vertical gap="middle" flex="1">
						<Form
							form={form}
							layout="vertical"
							size="middle"
							style={{ width: "100%" }}
							onFinish={values => onFinish(values)}
						>
							<Form.Item
								name={title}
								label={`Add ${title}`}
								rules={[
									{
										validator: validateEmail,
									},
								]}
							>
								<Tags />
							</Form.Item>
						</Form>

						<Flex wrap gap="small" style={{ width: "100%" }}>
							{(modalData ?? []).map(user => (
								<Tag
									key={user._id}
									style={{ padding: "2px 8px" }}
									closable={!user.inEditMode}
									onClose={() => removeTag(user.email)}
									onDoubleClick={() => editTag(user.email)}
								>
									{user.inEditMode ? (
										<>
											<Input
												value={user.editedEmail}
												onChange={e =>
													handleInputChange(
														user.email,
														e.target.value
													)
												}
												onBlur={() =>
													saveEditedTag(user.email)
												}
												onPressEnter={() =>
													saveEditedTag(user.email)
												}
												autoFocus
												style={{
													border: "none",
													outline: "none",
													background: "transparent",
													padding: "2px 8px",
													fontSize: "12px",
												}}
											/>
										</>
									) : (
										<>
											{user.email}
											<Button
												type="text"
												icon={<EditOutlined />}
												onClick={() =>
													editTag(user.email)
												}
												size="small"
												style={{
													fontSize: "0.8em",
													color: "#00000073",
												}}
											/>
										</>
									)}
								</Tag>
							))}
						</Flex>
						{modalData.length === 0 && (
							<Empty description={`No ${title} found`} />
						)}
					</Flex>
				</Modal>
			</>
		);
	}
);

const Rooms = () => {
	const navigate = useNavigate();
	const { id } = useParams<{ id: string }>();
	const [roomsData, setRoomsData] = useState<roomDataType[] | undefined>();
	const [tableState, setTableState] = useState<{
		tableData: roomDataType[] | undefined;
		currentPage: number;
		pageSize: number;
		totalRecords: number;
		loading: boolean;
		roomSearch: string;
		studentSearchValue: string;
		invigilatorSearchValue: string;
		sorting: unknown;
	}>({
		tableData: undefined,
		currentPage: 1,
		totalRecords: 1,
		pageSize: 10,
		loading: true,
		roomSearch: "",
		studentSearchValue: "",
		invigilatorSearchValue: "",
		sorting: {},
	});
	const [addRoomform] = Form.useForm();
	const [addNewRoomModal, setAddNewRoomModal] = useState(false);
	const [bulkUploadModal, setBulkUploadModal] = useState(false);
	const [loading, setLoading] = useState(false);
	const [messageInstance, messageInstanceContext] = message.useMessage();

	const handleSearch = useCallback(
		(value: string, key: keyof roomDataType) => {
			const trimmedValue = value.trim().toLowerCase();
			setTableState(prev => {
				// Reset all search values and set only the active one
				const newSearchState = {
					roomSearch: "",
					studentSearchValue: "",
					invigilatorSearchValue: "",
					[key === "roomName"
						? "roomSearch"
						: key === "students"
							? "studentSearchValue"
							: "invigilatorSearchValue"]: value,
				};

				let filteredData: roomDataType[] = [];

				if (!trimmedValue) {
					filteredData = roomsData ?? [];
				} else {
					filteredData = (roomsData ?? [])?.filter(item => {
						if (key === "students" || key === "invigilators") {
							return item[key]?.some(u =>
								u.email.toLowerCase().includes(trimmedValue)
							);
						}
						return item[key]
							?.toString()
							.toLowerCase()
							.includes(trimmedValue);
					});
				}

				return {
					...prev,
					...newSearchState,
					tableData: filteredData,
					loading: false,
				};
			});
		},
		[roomsData]
	);

	const getColumnSearchProps = useCallback(
		(dataIndex: keyof roomDataType) => ({
			filterDropdown: () => (
				<Search
					size="middle"
					placeholder={`Search ${dataIndex === "roomName" ? "room" : dataIndex}...`}
					allowClear
					onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
						handleSearch(e.target.value, dataIndex)
					}
					value={
						dataIndex === "roomName"
							? tableState.roomSearch
							: dataIndex === "students"
								? tableState.studentSearchValue
								: tableState.invigilatorSearchValue
					}
					loading={tableState.loading}
				/>
			),
			filterIcon: (filtered: boolean) => (
				<SearchOutlined
					style={{
						color: filtered ? "#1677ff" : undefined,
					}}
				/>
			),
		}),
		[
			handleSearch,
			tableState.loading,
			tableState.roomSearch,
			tableState.studentSearchValue,
			tableState.invigilatorSearchValue,
		]
	);

	const handleCreateRoom = async ({
		roomName,
		students,
		invigilators,
	}: {
		roomName: string;
		students: string[];
		invigilators: string[];
	}) => {
		if (!id) return;
		const requestData = {
			roomId: "",
			roomName: roomName + `-${id}`,
			invigilatorEmails: invigilators || [],
			userEmails: students || [],
		};
		const duplicateStudentInOtherRoom = (
			tableState.tableData?.flatMap(room => room.students) ?? []
		).find(student => requestData.userEmails.includes(student.email));

		if (duplicateStudentInOtherRoom) {
			messageInstance.error(
				`The following email is already a student in another room: ${duplicateStudentInOtherRoom.email}`
			);
			return;
		}

		const duplicateInvigilatorInOtherRoom = (
			tableState.tableData?.flatMap(room => room.invigilators) ?? []
		).find(invigilator =>
			requestData.invigilatorEmails.includes(invigilator.email)
		);

		if (duplicateInvigilatorInOtherRoom) {
			messageInstance.error(
				`The following email is already an invigilator in another room: ${duplicateInvigilatorInOtherRoom.email}`
			);
			return;
		}
		setLoading(true);
		try {
			const response = await testAPI.addQuizRoom(requestData, id);
			if ("error" in response) {
				throw new Error(response.error);
			}
			const data: roomDataType = {
				_id: response.id,
				roomName: response.roomName + `-${id}`,
				students: response.users ?? [],
				invigilators: response.invigilators ?? [],
				userIds: (response.users ?? []).map(
					(user: userDataType) => user._id
				),
				invigilatorIds: (response.invigilators ?? []).map(
					(user: userDataType) => user._id
				),
				updatedAt: Date.now().toString(),
				createdAt: Date.now().toString(),
				createdBy: "",
				updatedBy: "",
				orgId: "",
				quizId: id,
				displaystatus: "1",
			};
			setRoomsData(prev => [...(prev || []), data]);
			setTableState(prev => ({
				...prev,
				tableData: [...(prev.tableData ?? []), data],
			}));
			addRoomform.resetFields();
			messageInstance.success("Room created successfully!");
			setAddNewRoomModal(false);
		} catch (error) {
			messageInstance.error(
				error instanceof Error ? error.message : String(error)
			);
			console.error(error);
		} finally {
			setLoading(false);
		}
	};

	const updateQuizRoom = useCallback(
		async (
			updatedData: addAndUpdateReqRoomDataType,
			title: string
		): Promise<boolean> => {
			try {
				const room = roomsData?.find(
					room => room._id === updatedData.roomId
				);

				const allOtherRooms =
					roomsData?.filter(r => r._id !== updatedData.roomId) || [];

				const conflictingStudents = updatedData.userEmails.filter(
					email =>
						allOtherRooms.some(room =>
							room.students.some(
								student => student.email === email
							)
						)
				);

				const conflictingInvigilators =
					updatedData.invigilatorEmails.filter(email =>
						allOtherRooms.some(room =>
							room.invigilators.some(inv => inv.email === email)
						)
					);

				if (
					conflictingStudents.length > 0 ||
					conflictingInvigilators.length > 0
				) {
					if (conflictingStudents.length > 0) {
						messageInstance.error(
							`The following email is already a student in another room: ${conflictingStudents[0]}`
						);
					}

					if (conflictingInvigilators.length > 0) {
						messageInstance.error(
							`The following email is already an invigilator in another room: ${conflictingInvigilators[0]}`
						);
					}
					return false;
				}

				const existingStudentEmails =
					room?.students.map(student => student.email) || [];
				const studentEmails =
					title !== "Students"
						? existingStudentEmails
						: existingStudentEmails.filter(email =>
								updatedData.userEmails.includes(email)
							);

				const existingInvigilatorEmails =
					room?.invigilators.map(invigilator => invigilator.email) ||
					[];
				const invigilatorEmails =
					title !== "Invigilators"
						? existingInvigilatorEmails
						: existingInvigilatorEmails.filter(email =>
								updatedData.invigilatorEmails.includes(email)
							);

				const data: addAndUpdateReqRoomDataType = {
					roomId: updatedData.roomId,
					roomName: updatedData.roomName + `-${id}`,
					userEmails: Array.from(
						new Set([...studentEmails, ...updatedData.userEmails])
					),
					invigilatorEmails: Array.from(
						new Set([
							...invigilatorEmails,
							...updatedData.invigilatorEmails,
						])
					),
				};
				const response: addAndUpdateResRoomDataType =
					await testAPI.updateQuizRoom(data);
				const skippedEmails = new Set([
					...updatedData.userEmails,
					...updatedData.invigilatorEmails,
				]);
				setRoomsData(prevRooms =>
					prevRooms
						? prevRooms.map(room =>
								room._id === response.id
									? {
											...room,
											students: response.users ?? [],
											invigilators:
												response.invigilators ?? [],
										}
									: room
							)
						: []
				);
				setTableState(prev => ({
					...prev,
					tableData: (prev.tableData ?? []).map(
						(room: roomDataType) =>
							room._id === updatedData.roomId
								? {
										...room,
										students: response.users ?? [],
										invigilators:
											response.invigilators ?? [],
									}
								: room
					),
				}));

				for (const user of [
					...(response.users ?? []),
					...(response.invigilators ?? []),
				]) {
					skippedEmails.delete(user.email);
				}
				if (skippedEmails.size === 0) {
					messageInstance.success("Room has been updated");
				} else {
					const error = `Following emails were not found:\n${Array.from(skippedEmails).join("\n")}`;
					messageInstance.error(error);
				}
				return true;
			} catch (error) {
				console.error(error);
				messageInstance.error("Failed to Update Rooms data");
				return false;
			}
		},
		[id, messageInstance, roomsData]
	);

	const handleDelete = useCallback(
		async (id: string) => {
			try {
				await testAPI.deleteQuizRoom(id);
				setRoomsData(
					prevRooms =>
						prevRooms?.filter(room => room._id !== id) ?? []
				);
				setTableState(prev => ({
					...prev,
					tableData: (prev.tableData ?? []).filter(
						room => room._id !== id
					),
				}));
				messageInstance.success("Room deleted successfully!");
			} catch (error) {
				console.error("Error deleting room:", error);
				messageInstance.error(
					"An error occurred while deleting the room."
				);
			}
		},
		[messageInstance]
	);

	const columns: TableColumnsType<roomDataType> = useMemo(
		() => [
			{
				key: "serialNumber",
				render: (_val, _record, index) => index + 1,
			},
			{
				title: "Room Name",
				key: "roomName",
				dataIndex: "roomName",
				...getColumnSearchProps("roomName"),
				render(value, record) {
					return roomNameReplacer(value, record.quizId);
				},
			},
			{
				title: "Students",
				key: "students",
				align: "center",
				dataIndex: "students",
				...getColumnSearchProps("students"),
				render(value, record) {
					return (
						<PreviewUsersModal
							roomName={record.roomName}
							quizId={record.quizId}
							roomId={record._id}
							searchValue={tableState.studentSearchValue}
							title="Students"
							data={value}
							updateQuizRoom={updateQuizRoom}
						/>
					);
				},
			},
			{
				title: "Invigilators",
				key: "invigilators",
				align: "center",
				dataIndex: "invigilators",
				...getColumnSearchProps("invigilators"),
				render(value, record) {
					return (
						<PreviewUsersModal
							roomName={record.roomName}
							quizId={record.quizId}
							roomId={record._id}
							searchValue={tableState.invigilatorSearchValue}
							title="Invigilators"
							data={value}
							updateQuizRoom={updateQuizRoom}
						/>
					);
				},
			},
			{
				title: "Actions",
				key: "actions",
				align: "center",
				render: (_value, data) => {
					return (
						<Space>
							<Tooltip title="Open Room">
								<Button
									type="text"
									onClick={() =>
										navigate(`/rooms/${data._id}`)
									}
									icon={<ExportOutlined />}
								/>
							</Tooltip>
							<Tooltip title="Delete">
								<Popconfirm
									title="Are you sure you want to delete this room?"
									onConfirm={() => handleDelete(data._id)}
									okText="Yes"
									cancelText="No"
								>
									<Button
										type="text"
										icon={<DeleteOutlined />}
									/>
								</Popconfirm>
							</Tooltip>
						</Space>
					);
				},
			},
		],
		[
			getColumnSearchProps,
			tableState,
			updateQuizRoom,
			navigate,
			handleDelete,
		]
	);

	const fetchData = useCallback(
		async (id: string | undefined) => {
			if (!id) return;
			try {
				const response = await testAPI.getTestRooms(id);
				if (!response) {
					messageInstance.error("Failed to load Rooms data");
					return;
				}
				const data: roomDataType[] = (response.rooms ?? []).map(
					room => ({
						...room,
						students: room.userIds
							?.map(
								(id: string) =>
									response.userData[
										id as keyof typeof response.userData
									]
							)
							.filter(
								(user): user is userDataType =>
									user !== undefined
							),
						invigilators: room.invigilatorIds
							?.map(
								(id: string) =>
									response.userData[
										id as keyof typeof response.userData
									]
							)
							.filter(
								(user): user is userDataType =>
									user !== undefined
							),
						_id: room._id,
					})
				);
				setTableState(prev => {
					return { ...prev, loading: false, tableData: data };
				});
				setRoomsData(data);
			} catch (error) {
				console.error(error);
				messageInstance.error("Failed to load Rooms data");
			} finally {
				setTableState(prev => {
					return { ...prev, loading: false };
				});
			}
		},
		[messageInstance]
	);

	useEffect(() => {
		if (!id) return;
		fetchData(id);
	}, [id, fetchData]);

	return (
		<>
			{messageInstanceContext}
			<Layout
				style={{
					margin: "auto",
					maxWidth: "1400px",
					width: "100%",
					padding: "16px",
					height: "100%",
				}}
			>
				<Header
					style={{
						backgroundColor: "transparent",
						padding: "0 0.5em 0 0.5em",
						borderRadius: "8px",
					}}
				>
					<Flex
						align="center"
						justify="flex-end"
						style={{ height: "100%" }}
						gap={20}
					>
						<Modal
							title="Add New Room"
							open={addNewRoomModal}
							onCancel={() => setAddNewRoomModal(false)}
							footer={null}
							centered
							width={{
								sm: "75%",
								md: "60%",
								lg: "50%",
								xl: "40%",
								xxl: "30%",
							}}
						>
							<Form
								layout="vertical"
								requiredMark={false}
								style={{
									display: "flex",
									flexDirection: "column",
									gap: "16px",
								}}
								form={addRoomform}
								onFinish={handleCreateRoom}
							>
								<Form.Item
									label="Room Name *"
									name="roomName"
									rules={[
										{
											validator: validateRoomName,
										},
									]}
								>
									<Input placeholder="Enter room name" />
								</Form.Item>

								<Form.Item
									label="Students"
									name="students"
									rules={[
										{
											validator: validateEmail,
										},
									]}
								>
									<Tags />
								</Form.Item>

								<Form.Item
									label="Invigilators"
									name="invigilators"
									rules={[
										{
											validator: validateEmail,
										},
									]}
								>
									<Tags />
								</Form.Item>

								<Form.Item
									style={{
										display: "flex",
										justifyContent: "flex-end",
									}}
								>
									<Button
										type="primary"
										htmlType="submit"
										loading={loading}
									>
										Create Room
										{loading && <LoadingOutlined />}
									</Button>
								</Form.Item>
							</Form>
						</Modal>
						<Flex gap={10} justify="flex-end">
							<Button
								type="primary"
								onClick={() => setAddNewRoomModal(true)}
								style={{ padding: "16px 12px" }}
								icon={<PlusOutlined />}
							/>
							<Button
								type="primary"
								onClick={() => setBulkUploadModal(true)}
								style={{ padding: "16px 12px" }}
								icon={<UploadOutlined />}
							/>
						</Flex>
					</Flex>
				</Header>
				<Content style={{ height: "100%", overflow: "auto" }}>
					<DataTable<roomDataType>
						data={tableState.tableData ?? []}
						columns={columns}
						loading={tableState.loading}
						totalRecords={tableState.totalRecords}
						pagination={{
							current: tableState.currentPage,
							pageSize: tableState.pageSize,
							onChange: (current, pageSize) =>
								setTableState(prev => {
									return {
										...prev,
										currentPage: current,
										pageSize: pageSize,
									};
								}),
							style: {
								position: "sticky",
								bottom: "0px",
								padding: "0.75em",
								backgroundColor: "white",
								margin: 0,
								zIndex: "1",
								width: "100%",
							},
							position: ["bottomCenter"],
							showSizeChanger: true,
						}}
						pageSize={tableState.pageSize}
						currentPage={1}
					></DataTable>
				</Content>
			</Layout>

			<AddBulkRoomModal
				testId={id}
				students={roomsData?.flatMap(room => room.students)}
				invigilators={roomsData?.flatMap(room => room.invigilators)}
				open={bulkUploadModal}
				toggle={setBulkUploadModal}
				onInit={() => console.log("uploading")}
				onFinish={() => fetchData(id)}
			/>
		</>
	);
};

export default Rooms;
