import { useCallback, useEffect, useState } from "react";
import { useLocation, useParams } from "react-router";
import { quizClient } from "../../store";
import useMessage from "antd/es/message/useMessage";
import useLocalStorageState from "@/localStorageState";
import {
	Quiz,
	QuizReport,
	ReportDataType,
	UserDataArray,
} from "@/testReport/data/data";
import {
	Badge,
	Button,
	Card,
	Checkbox,
	Flex,
	Layout,
	Space,
	TableColumnsType,
	theme,
	Tooltip,
} from "antd";
import FilterMenu, { FilterMenuItem } from "../filterMenu/filterMenu";
import Sider from "antd/es/layout/Sider";
import { Content, Header } from "antd/es/layout/layout";
import {
	CloseOutlined,
	EyeOutlined,
	UsergroupAddOutlined,
} from "@ant-design/icons";
import { FilterIcon } from "lucide-react";
import Search from "antd/es/input/Search";
import { DataTable } from "../dataTable/dataTable";
import ExtraTime from "../Menu-Components/ExtraTime";
import FinishTest from "../Menu-Components/FinishTest";
import LogsModal from "@/testReport/logsModal";
import ExtraTimeLogs from "@/testReport/extraTimeLogs";
import ModalComp from "../userReports/modal";
import UserProctoringCount from "../userReports/proctoringTables/userProctoringCount";
import { aiProctorScore, ProctoringType } from "@/constants";
import LoginCount from "../userReports/activityTables/loginCount";
import FullScreen from "../userReports/activityTables/fullScreen";
import TabSwitch from "../userReports/activityTables/tabSwitch";
import { AppConfig } from "@/config";
import PageHelmet from "../page-helmet/PageHelmet";
export type Room = {
	_id: string;
	updatedAt: string;
	createdAt: string;
	roomName: string;
	quizId: string;
	createdBy: string;
	updatedBy: string;
	orgId: string;
	displaystatus: string;
	invigilatorIds: string[];
	userIds: string[];
	__v: number;
};
const fixedColumns = ["Serial Number", "displayname", "email", "Actions"];
const defaultColumns = [
	"status",
	"timeTaken",
	"score",
	"feedback",
	"startTime",
];

const locale = {
	emptyText: "No user in room has attempted yet",
};

type TestReportFilters = {
	dateRange?: [string, string];
	slider?: [number, number];
	reportTags?: string[];
	invitedTags?: string[];
	invitedAttemptStatus?: number;
	allowedTags?: string[];
	allowedAttemptStatus?: number;
};

const MeetingRoom = () => {
	const { roomId } = useParams();
	const location = useLocation();
	const shouldShowCompleted = !new URLSearchParams(location.search).has(
		"force_hide_completed"
	);
	const [messageInstance, contextHolder] = useMessage({
		maxCount: 1,
	});
	const [reportState, setReportState] = useLocalStorageState<{
		searchValue: string;
		currentPage: number;
		pageSize: number;
		questionWiseMarks: boolean;
		visibleColumns: string[];
		marksRange: [number, number];
		dateRange: [string, string];
		maxMarks: number;
		minMarks: number;
	}>("meetingRoomState", {
		searchValue: "",
		currentPage: 1,
		pageSize: 10,
		questionWiseMarks: false,
		visibleColumns: [...fixedColumns, ...defaultColumns],
		marksRange: [0, 0],
		dateRange: ["", ""],
		maxMarks: 0,
		minMarks: 0,
	});
	const [tableData, setTableData] = useState<ReportDataType[]>([]);
	const [filteredReportData, setFilteredReportData] =
		useState<ReportDataType[]>(tableData);
	const [showFilters, setShowFilters] = useState<boolean>(false);
	const [loading, setLoading] = useState<boolean>(true);
	const [showColumns, setShowColumns] = useState<boolean>(false);
	const [quizData, setQuizData] = useState<Quiz>();
	const [extraTimeModalData, setExtraTimeModalData] = useState<
		ReportDataType[]
	>([]);
	const [finishTestUsers, setFinishTestUsers] = useState<ReportDataType[]>(
		[]
	);
	const [extraTimeLogsModalData, setExtraTimeLogsModalData] =
		useState<ReportDataType>();
	const reportFilters: FilterMenuItem[] = [
		{
			type: "dateRange",
			key: "dateRange",
			title: "Start Time:",
			value: reportState.dateRange,
		},
		{
			type: "slider",
			key: "slider",
			title: "Marks Obtained:",
			minValue: reportState.minMarks,
			maxMarks: reportState.maxMarks,
			value: reportState.marksRange,
		},
	];

	useEffect(() => {
		setLoading(true);
		if (roomId === undefined) {
			setLoading(false);
			return;
		}
		(async function () {
			try {
				const data = await quizClient.getRoomData(roomId);
				console.log(data);
				updateReportData(data.quizReport);
				setQuizData(data.quizReport.quiz);
			} catch (error) {
				messageInstance.error(error as string);
			} finally {
				setLoading(false);
			}
		})();
	}, []);

	function updateReportData(data: QuizReport) {
		const userData = data.userDataArray;
		const questions = data.questions;
		const finalData: ReportDataType[] = [];

		function calculateSuspiciousScore(data: UserDataArray) {
			let score =
				data.illegalObjectDetected * aiProctorScore.illegalObject;
			score += data.userExitCount * aiProctorScore.userExit;
			score += data.userEnterCount * aiProctorScore.userEnter;
			score += data.multipleUserDetected * aiProctorScore.multipleUser;
			return Math.floor(score);
		}
		let minMarks = 0;
		let totalMarks = 0;
		let suspiciousScore = 0;
		let marksObtained = 0;
		let timeTaken = "";
		let completed = false;
		let startTime = "";
		let extraTime = 0;

		if (data.quiz.poolQuestion) {
			const questionIdToScoreMap = new Map();
			questions.forEach(question => {
				questionIdToScoreMap.set(
					question._id.toString(),
					question.score
				);
			});
			userData.forEach(currentUserData => {
				(currentUserData.assignedQuestions ?? []).forEach(question => {
					totalMarks += questionIdToScoreMap.get(question);
				});
				suspiciousScore = calculateSuspiciousScore(currentUserData);
			});
		} else {
			questions.forEach(question => {
				totalMarks += question.score;
			});
			userData.forEach(currentUserData => {
				suspiciousScore = calculateSuspiciousScore(currentUserData);
			});
		}

		for (const currentUserData of userData) {
			currentUserData.questionWiseScore.forEach(val => {
				if (typeof val === "number") {
					marksObtained += val;
				}
			});

			if (currentUserData.endTime) {
				completed = true;
			} else if (
				(currentUserData.startTime &&
					Number(currentUserData.startTime) == 0) ||
				!currentUserData.startTime
			) {
				completed = true;
			}
			if (shouldShowCompleted) {
				if (!completed) {
					if (currentUserData.startTime) {
						startTime = currentUserData.startTime;
						extraTime = currentUserData.extraTime;
					} else if (currentUserData.userSessions.length) {
						startTime =
							currentUserData.userSessions[
								currentUserData.userSessions.length - 1
							].startTime;
						extraTime =
							currentUserData.userSessions[
								currentUserData.userSessions.length - 1
							].extraTime;
					}
					if (startTime) {
						if (new Date(startTime).toString() !== "Invalid Date") {
							startTime = new Date(startTime)
								.getTime()
								.toString();
						}
						if (
							new Date(startTime).getTime() +
								extraTime +
								Number(data.quiz.quizTime) * 1000 * 60 +
								1 * 60 * 1000 <
							new Date().getTime()
						) {
							completed = true;
						}
					}
					if (completed) {
						currentUserData.timeTaken = (
							Number(data.quiz.quizTime) +
							currentUserData.extraTime / 60
						).toString();
						currentUserData.userSessions.forEach(element => {
							currentUserData.timeTaken += element.extraTime ?? 0;
						});
						currentUserData.endTime = new Date(
							startTime +
								Number(currentUserData.timeTaken) * 1000 * 60
						)
							.getTime()
							.toString();
						if (
							currentUserData.userSessions &&
							currentUserData.userSessions.length
						) {
							currentUserData.endTime = new Date(
								startTime +
									Number(
										currentUserData.sessionTime ??
											currentUserData.extraTime
									) *
										1000
							)
								.getTime()
								.toString();
							currentUserData.userSessions.push({
								endTime: currentUserData.endTime,
								startTime: currentUserData.startTime ?? "",
								_id: "",
								sessionTime: 0,
								extraTime: 0,
							});
						} else {
							currentUserData.startTime = startTime;
						}
					}
				}
			}
			if (
				currentUserData.userSessions &&
				currentUserData.userSessions[0] &&
				currentUserData.userSessions[0].startTime
			) {
				startTime = currentUserData.userSessions[0].startTime;
			}
			if (
				currentUserData.endTime &&
				currentUserData.startTime &&
				(!currentUserData.userSessions ||
					currentUserData.userSessions.length == 0)
			) {
				if (!currentUserData.idleTime) currentUserData.idleTime = 0;

				const msDifference =
					new Date(currentUserData.endTime).getTime() -
						new Date(currentUserData.startTime).getTime() || 0;

				const minutes = +(msDifference / 1000 / 60).toFixed(2) || 0;
				timeTaken = minutes.toString();

				currentUserData.endTime = new Date(currentUserData.startTime)
					.getTime()
					.toString();
				currentUserData.endTime = (
					Number(currentUserData.endTime) + Number(timeTaken)
				).toString();
			} else {
				if (
					currentUserData.userSessions &&
					!Array.isArray(currentUserData.userSessions)
				) {
					currentUserData.userSessions = JSON.parse(
						currentUserData.userSessions
					);
				}
				if (
					completed &&
					currentUserData.userSessions &&
					Array.isArray(currentUserData.userSessions) &&
					currentUserData.userSessions.length
				) {
					let totalsMins = 0;
					currentUserData.userSessions.forEach(val => {
						const TimeOne = +val.startTime;
						const Timetwo = +val.endTime;

						const dateOne = isNaN(TimeOne)
							? val.startTime
							: TimeOne;
						const dateTwo = isNaN(Timetwo) ? val.endTime : Timetwo;

						if (dateOne && dateTwo) {
							const msDifference =
								+(
									new Date(dateTwo).getTime() -
									new Date(dateOne).getTime()
								) || 0;
							const minutes =
								+(msDifference / 1000 / 60).toFixed(2) || 0;
							totalsMins += minutes;
						}
					});
					currentUserData.endTime =
						currentUserData.userSessions[
							currentUserData.userSessions.length - 1
						].endTime;
					timeTaken = totalsMins.toString();
				}
			}

			if (marksObtained < minMarks) {
				minMarks = marksObtained;
			}
			finalData.push({
				...currentUserData,
				timeTaken: timeTaken,
				score: marksObtained,
				startTime: startTime,
				extraTime: extraTime,
				sessionTime: "",
				isAttempting: false,
				latestPlatform: {
					browser: "",
					browser_version: "",
					os: "",
				},
				enrollmentId: "",
				totalMarks: totalMarks,
				suspiciousScore: suspiciousScore,
				completed: completed,
				status: completed ? "Completed" : "InProgress",
			});
		}
		setTableData(finalData);
		setFilteredReportData(finalData);
		setReportState(prev => ({
			...prev,
			minMarks: minMarks,
			maxMarks: totalMarks,
			marksRange: [minMarks, totalMarks],
		}));
	}

	useEffect(() => {
		const filteredByMarksAndDate = tableData.filter(item => {
			const score = item.score;
			const [minMarks, maxMarks] = reportState.marksRange;
			const [startDate, endDate] = reportState.dateRange;

			const isWithinMarksRange = score >= minMarks && score <= maxMarks;

			const isWithinDateRange =
				(!startDate ||
					new Date(item.startTime) >= new Date(startDate)) &&
				(!endDate || new Date(item.startTime) <= new Date(endDate));

			return isWithinMarksRange && isWithinDateRange;
		});

		const filteredBySearch = filteredByMarksAndDate.filter(item =>
			(item.displayname || "")
				.toLowerCase()
				.includes(reportState.searchValue.trim().toLowerCase())
		);

		setFilteredReportData(filteredBySearch);
	}, [
		reportState.marksRange,
		reportState.dateRange,
		reportState.searchValue,
		tableData,
	]);

	const handleFilterApply = (data: TestReportFilters) => {
		console.log(data);
		setReportState(prev => ({
			...prev,
			marksRange: data.slider ?? [prev.minMarks, prev.maxMarks],
			dateRange: data.dateRange ?? ["", ""],
		}));
	};

	const handleFilterReset = () => {
		setReportState(prev => ({
			...prev,
			marksRange: [prev.minMarks, prev.maxMarks],
			dateRange: ["", ""],
			searchValue: "",
			currentPage: 1,
			dropDownValue: 1,
		}));
	};

	const handleSearch = (value: string) => {
		setLoading(true);
		setReportState(prev => ({ ...prev, searchValue: value }));
		setLoading(false);
	};

	const onSubmit = (message: string, type: "success" | "error") => {
		if (type === "success") {
			messageInstance.success(message);
		} else {
			messageInstance.error(message);
		}
	};

	const sendOTP = useCallback(async () => {
		try {
			if (!quizData?._id) {
				throw new Error("Something went wrong quizId is not available");
			}
			await quizClient.sendOtp(quizData?._id);
		} catch (error) {
			if (error instanceof Error) {
				messageInstance.error(error.message);
				return;
			}
			if (typeof error === "string") {
				messageInstance.error(error);
				return;
			}
			messageInstance.error("Something went wrong");
		}
	}, [quizData?._id]);

	const reportColumns: TableColumnsType<ReportDataType> = [
		{
			key: "Serial Number",
			render: (_value, _record, index) => index + 1,
			width: 50,
			fixed: "left",
			onCell: () => ({ style: { zIndex: "1" } }),
			onHeaderCell: () => ({ style: { zIndex: "1" } }),
		},
		{
			title: "Name",
			dataIndex: "displayname",
			key: "displayname",
			fixed: "left",
			width: 200,
			sorter: (a, b) => a.displayname.localeCompare(b.displayname),
			showSorterTooltip: false,
			render: value => {
				return <>{value}</>;
			},
			onCell: () => ({ style: { zIndex: "1" } }),
			onHeaderCell: () => ({ style: { zIndex: "1" } }),
		},
		{
			title: "Email",
			dataIndex: "email",
			key: "email",
			fixed: "left",
			width: 250,
			filterSearch: true,
			ellipsis: true,
			onCell: () => ({ style: { zIndex: "1" } }),
			onHeaderCell: () => ({ style: { zIndex: "1" } }),
		},
		{
			title: "Roll No",
			dataIndex: "rollNo",
			key: "rollNo",
			width: 100,
			align: "center",
			render: value => {
				if (Number(value) === 0) return "-";
				return value;
			},
			onHeaderCell: () => ({ style: { zIndex: "0" } }),
		},
		{
			title: "Start Time",
			dataIndex: "startTime",
			key: "startTime",
			width: 150,
			align: "center",
			showSorterTooltip: false,
			sorter: (a, b) =>
				new Date(a.startTime ?? 0).getTime() -
				new Date(b.startTime ?? 0).getTime(),
			render: value => {
				if (!value) return "-NA-";
				let dateObj: Date;

				if (/^\d+$/.test(value)) {
					dateObj = new Date(Number(value));
				} else {
					dateObj = new Date(value);
				}
				if (isNaN(dateObj.getTime())) return "-NA-";

				const date = dateObj.toLocaleDateString();
				const time = dateObj.toLocaleTimeString([], {
					hour: "2-digit",
					minute: "2-digit",
					hour12: false,
				});

				return (
					<Space direction="vertical" align="center" size="small">
						{date}
						{time}
					</Space>
				);
			},
			onHeaderCell: () => ({ style: { zIndex: "0" } }),
		},
		{
			title: "Status",
			dataIndex: "status",
			key: "status",
			width: 120,
			align: "center",
			showSorterTooltip: false,
			sorter: (a, b) => a.status.localeCompare(b.status),
			onHeaderCell: () => ({ style: { zIndex: "0" } }),
		},

		{
			title: "End Time",
			dataIndex: "endTime",
			key: "endTime",
			width: 150,
			align: "center",
			showSorterTooltip: false,
			sorter: (a, b) =>
				new Date(a.endTime ?? 0).getTime() -
				new Date(b.endTime ?? 0).getTime(),
			render: value => {
				if (!value) return "-NA-";
				let dateObj: Date;

				if (/^\d+$/.test(value)) {
					dateObj = new Date(Number(value));
				} else {
					dateObj = new Date(value);
				}
				if (isNaN(dateObj.getTime())) return "-NA-";

				const date = dateObj.toLocaleDateString();
				const time = dateObj.toLocaleTimeString([], {
					hour: "2-digit",
					minute: "2-digit",
					hour12: false,
				});

				return (
					<Space direction="vertical" align="center" size="small">
						{date}
						{time}
					</Space>
				);
			},
			onHeaderCell: () => ({ style: { zIndex: "0" } }),
		},
		{
			title: "Time Taken",
			dataIndex: "timeTaken",
			key: "timeTaken",
			width: 200,
			align: "center",
			sorter: (a, b) => Number(a.timeTaken) - Number(b.timeTaken),
			showSorterTooltip: false,
			render: (value, record) => {
				const minutes = Math.floor(Number(value));
				const seconds = Math.round((Number(value) - minutes) * 60);
				if (!record.completed) {
					return "-NA-";
				}
				return (
					<>
						{minutes} min {seconds > 0 ? `${seconds} sec` : ""}
					</>
				);
			},
			onHeaderCell: () => ({ style: { zIndex: "0" } }),
		},
		{
			title: "Total Marks",
			dataIndex: "totalMarks",
			key: "totalMarks",
			width: 200,
			align: "center",
			showSorterTooltip: false,
			onHeaderCell: () => ({ style: { zIndex: "0" } }),
		},
		{
			title: "Marks Obtained",
			dataIndex: "score",
			key: "score",
			width: 200,
			align: "center",
			sorter: (a, b) => a.score - b.score,
			showSorterTooltip: false,
			onHeaderCell: () => ({ style: { zIndex: "0" } }),
		},
		{
			title: "Attempted Questions",
			dataIndex: "totalAttemptedQuestion",
			key: "totalAttemptedQuestion",
			width: 200,
			align: "center",
			onHeaderCell: () => ({ style: { zIndex: "0" } }),
		},
		{
			title: "Tried Copy-Paste",
			dataIndex: "codePasteCount",
			key: "codePasteCount",
			width: 180,
			align: "center",
			sorter: (a, b) => a.codePasteCount - b.codePasteCount,
			onHeaderCell: () => ({ style: { zIndex: "0" } }),
		},
		{
			title: "Tab Switch Out",
			dataIndex: "tabSwitchCount",
			key: "tabSwitchCount",
			width: 150,
			align: "center",
			sorter: (a, b) => a.tabSwitchCount - b.tabSwitchCount,
			render: (text, data) => (
				<ModalComp
					title="Tab Switch Out Count"
					val={text}
					name={data.displayname}
					email={data.email}
				>
					<TabSwitch
						testId={quizData?._id}
						userId={data._id}
						scroll={{ y: 200 }}
					/>
				</ModalComp>
			),
			onHeaderCell: () => ({ style: { zIndex: "0" } }),
		},
		{
			title: "Tab Switch In",
			dataIndex: "tabSwitchInCount",
			key: "tabSwitchInCount",
			width: 150,
			align: "center",
			sorter: (a, b) => a.tabSwitchInCount - b.tabSwitchInCount,
			render: (text, data) => (
				<ModalComp
					title="Tab Switch In Count"
					val={text}
					name={data.displayname}
					email={data.email}
				>
					<TabSwitch
						testId={quizData?._id}
						userId={data._id}
						scroll={{ y: 200 }}
					/>
				</ModalComp>
			),
			onHeaderCell: () => ({ style: { zIndex: "0" } }),
		},
		{
			title: "Full Screen In",
			dataIndex: "fullScreenInCount",
			key: "fullScreenInCount",
			width: 150,
			align: "center",
			sorter: (a, b) => a.fullScreenInCount - b.fullScreenInCount,
			render: (text, data) => (
				<ModalComp
					title="Full Screen Count In"
					val={text}
					name={data.displayname}
					email={data.email}
				>
					<FullScreen
						testId={quizData?._id}
						userId={data._id}
						scroll={{ y: 200 }}
					/>
				</ModalComp>
			),
			onHeaderCell: () => ({ style: { zIndex: "0" } }),
		},
		{
			title: "Full Screen Out",
			dataIndex: "fullScreenOutCount",
			key: "fullScreenOutCount",
			width: 150,
			align: "center",
			sorter: (a, b) => a.fullScreenOutCount - b.fullScreenOutCount,
			render: (text, data) => (
				<ModalComp
					title="Full Screen Count Out"
					val={text}
					name={data.displayname}
					email={data.email}
				>
					<FullScreen
						testId={quizData?._id}
						userId={data._id}
						scroll={{ y: 200 }}
					/>
				</ModalComp>
			),
			onHeaderCell: () => ({ style: { zIndex: "0" } }),
		},
		{
			title: "Login Count",
			dataIndex: "loginCount",
			key: "loginCount",
			width: 140,
			align: "center",
			sorter: (a, b) => a.loginCount - b.loginCount,
			render: (text, data) => (
				<ModalComp
					title="Login Count"
					val={text}
					name={data.displayname}
					email={data.email}
				>
					<LoginCount
						testId={quizData?._id}
						userId={data._id}
						scroll={{ y: 200 }}
					/>
				</ModalComp>
			),
			onHeaderCell: () => ({ style: { zIndex: "0" } }),
		},
		{
			title: "Cam Block",
			dataIndex: "camBlockCount",
			key: "camBlockCount",
			width: 120,
			align: "center",
			sorter: (a, b) => a.camBlockCount - b.camBlockCount,
			onHeaderCell: () => ({ style: { zIndex: "0" } }),
		},
		{
			title: "Qualified",
			dataIndex: "qualified",
			key: "qualified",
			width: 100,
			align: "center",
			render: text => (text ? "Pass" : "Fail"),
			onHeaderCell: () => ({ style: { zIndex: "0" } }),
		},
		{
			title: "User Exit",
			dataIndex: "userExitCount",
			key: "userExitCount",
			width: 120,
			align: "center",
			sorter: (a, b) => a.userExitCount - b.userExitCount,
			render: (text, data) => (
				<ModalComp
					title="AI Protoring"
					val={text}
					name={data.displayname}
					email={data.email}
				>
					<UserProctoringCount
						testId={quizData?._id}
						userId={data._id}
						defaultSubTypeVal={ProctoringType.USER_EXIT}
					/>
				</ModalComp>
			),
			onHeaderCell: () => ({ style: { zIndex: "0" } }),
		},
		{
			title: "User Enter",
			dataIndex: "userEnterCount",
			key: "userEnterCount",
			width: 120,
			align: "center",
			sorter: (a, b) => a.userEnterCount - b.userEnterCount,
			render: (text, data) => (
				<ModalComp
					title="AI Protoring"
					val={text}
					name={data.displayname}
					email={data.email}
				>
					<UserProctoringCount
						testId={quizData?._id}
						userId={data._id}
						defaultSubTypeVal={ProctoringType.USER_ENTER}
					/>
				</ModalComp>
			),
			onHeaderCell: () => ({ style: { zIndex: "0" } }),
		},
		{
			title: "Multiple Person",
			dataIndex: "multipleUserDetected",
			key: "multipleUserDetected",
			width: 150,
			align: "center",
			sorter: (a, b) => a.multipleUserDetected - b.multipleUserDetected,
			render: (text, data) => (
				<ModalComp
					title="AI Protoring"
					val={text}
					name={data.displayname}
					email={data.email}
				>
					<UserProctoringCount
						testId={quizData?._id}
						userId={data._id}
						defaultSubTypeVal={ProctoringType.MULTIPLE_PERSON}
					/>
				</ModalComp>
			),
			onHeaderCell: () => ({ style: { zIndex: "0" } }),
		},
		{
			title: "Prohibited items",
			dataIndex: "illegalObjectDetected",
			key: "illegalObjectDetected",
			width: 150,
			align: "center",
			sorter: (a, b) => a.illegalObjectDetected - b.illegalObjectDetected,
			render: (text, data) => (
				<ModalComp
					title="AI Protoring"
					val={text}
					name={data.displayname}
					email={data.email}
				>
					<UserProctoringCount
						testId={quizData?._id}
						userId={data._id}
						defaultSubTypeVal={ProctoringType.PROHIBITED_ITEMS}
					/>
				</ModalComp>
			),
			onHeaderCell: () => ({ style: { zIndex: "0" } }),
		},
		{
			title: "Suspicious Score",
			dataIndex: "suspiciousScore",
			key: "suspiciousScore",
			width: 160,
			align: "center",
			sorter: (a, b) => a.suspiciousScore - b.suspiciousScore,
			onHeaderCell: () => ({ style: { zIndex: "0" } }),
		},
		{
			title: "Rating",
			dataIndex: "feedback",
			key: "feedback",
			width: 120,
			render: (_value, record) => {
				return <>{record.feedback?.feedbackText || "-"}</>;
			},
			onHeaderCell: () => ({ style: { zIndex: "0" } }),
		},
		{
			title: "Actions",
			render: (_value, data) => {
				return (
					<Tooltip title="Join Meeting">
						<Button
							type="text"
							icon={
								<UsergroupAddOutlined
									style={{ fontSize: "18px" }}
								/>
							}
							onClick={() =>
								window.open(
									`${AppConfig.quizServerURL}/meeting/${data._id}/${quizData?._id}`
								)
							}
						/>
					</Tooltip>
				);
			},
			fixed: "right",
			width: 80,
			key: "Actions",
			ellipsis: true,
			align: "center",
			onCell: () => ({ style: { zIndex: "1" } }),
			onHeaderCell: () => ({ style: { zIndex: "1" } }),
		},
	];

	const filteredColumns = reportColumns.filter(col =>
		reportState.visibleColumns.includes(col.key as string)
	);

	const handleVisibility = (key: string) => {
		setReportState(prev => ({
			...prev,
			visibleColumns: prev.visibleColumns.includes(key)
				? prev.visibleColumns.filter(item => item !== key)
				: [...prev.visibleColumns, key],
		}));
	};

	const columnVisibilityMenu = reportColumns
		.filter(col => col.key !== "Serial Number")
		.map(col => (
			<div
				key={col.key as string}
				style={{ marginBottom: "0.5em", padding: "0.25em" }}
			>
				<Checkbox
					checked={reportState.visibleColumns.includes(
						col.key as string
					)}
					onChange={() => handleVisibility(col.key as string)}
					disabled={fixedColumns.includes(col.key as string)}
					style={{ width: "100%" }}
				>
					{col.title as string}
				</Checkbox>
			</div>
		));

	const {
		token: { colorBgContainer },
	} = theme.useToken();

	return (
		<>
			{quizData && <PageHelmet title={quizData?.title} />}

			<Flex vertical style={{ height: "100%" }}>
				<Layout style={{ padding: "0.5em", height: "100%" }}>
					{contextHolder}
					<Header
						style={{
							backgroundColor: colorBgContainer,
							padding: "0px 16px",
						}}
					>
						<Flex
							align="center"
							justify="space-between"
							style={{ height: "100%" }}
							gap={20}
						>
							<Flex gap={10}>
								<Badge
									dot
									count={
										reportState.dateRange[0] !== "" ||
										reportState.marksRange[0] !==
											reportState.minMarks
											? 1
											: 0 + reportState.marksRange[1] !==
												  reportState.maxMarks
												? 1
												: 0
									}
								>
									<Button
										type={
											!showFilters ? "default" : "primary"
										}
										icon={
											!showFilters ? (
												<FilterIcon size={14} />
											) : (
												<CloseOutlined size={14} />
											)
										}
										onClick={() =>
											setShowFilters(prev => !prev)
										}
									>
										Filters
									</Button>
								</Badge>
								<Search
									width="400px"
									size="middle"
									placeholder="Search here..."
									value={reportState.searchValue}
									allowClear
									onChange={e => handleSearch(e.target.value)}
									loading={loading}
								/>
							</Flex>
							<Flex gap={10}>
								<Button
									type="default"
									icon={<EyeOutlined />}
									href={`${AppConfig.quizServerURL}/meeting/invigilate/${quizData?._id}/${roomId}`}
								>
									Invigilate
								</Button>
								<Button
									type="default"
									onClick={() => {
										window.open(
											`${AppConfig.quizServerURL}/meeting/joinSuspiciousRoom/${quizData?._id}`,
											"blank"
										);
									}}
								>
									Suspicious Room
								</Button>
								<Button
									onClick={() =>
										setShowColumns(prev => !prev)
									}
									type={!showColumns ? "default" : "primary"}
								>
									Columns
								</Button>
							</Flex>
						</Flex>
					</Header>
					<Layout>
						<Sider
							collapsed={!showFilters}
							collapsedWidth={0}
							theme="light"
							style={{
								overflow: "hidden auto",
								maxHeight: "100vh",
							}}
							width="18%"
						>
							<FilterMenu
								componentsData={reportFilters}
								onChange={handleFilterApply}
								onReset={handleFilterReset}
								onClose={() => setShowFilters(false)}
							></FilterMenu>
						</Sider>
						<Content>
							<div
								style={{
									height: "100%",
									overflow: "auto",
									backgroundColor: "#FBFBFB",
								}}
							>
								<DataTable<ReportDataType>
									data={filteredReportData}
									columns={filteredColumns}
									loading={loading}
									scroll={{
										x: 1600,
									}}
									pagination={{
										current: reportState.currentPage,
										pageSize: reportState.pageSize,
										onChange: (current, pageSize) =>
											setReportState(prev => ({
												...prev,
												currentPage: current,
												pageSize,
											})),
										style: {
											position: "sticky",
											bottom: "0px",
											padding: "0.75em",
											backgroundColor: "white",
											margin: 0,
											zIndex: "1",
											width: "100%",
										},
										position: ["bottomCenter"],
										showSizeChanger: true,
									}}
									locale={locale}
								/>
							</div>
						</Content>
						<Sider
							collapsed={!showColumns}
							collapsedWidth={0}
							theme="light"
							style={{
								overflow: "hidden auto",
								maxHeight: "100vh",
							}}
							width="18%"
						>
							<Card
								title="Columns List"
								styles={{
									header: {
										position: "sticky",
										top: 0,
										zIndex: 1,
										background: "white",
									},
								}}
								extra={
									<Button
										type="text"
										icon={<CloseOutlined />}
										onClick={() => setShowColumns(false)}
									></Button>
								}
							>
								{columnVisibilityMenu}
							</Card>
						</Sider>
					</Layout>
				</Layout>
				{
					<ExtraTime
						onClose={() => setExtraTimeModalData([])}
						users={extraTimeModalData}
						quizId={quizData?._id || ""}
						quizTime={Number(quizData?.quizTime)}
						onSubmit={onSubmit}
					/>
				}
				<FinishTest
					users={finishTestUsers}
					onClose={() => setFinishTestUsers([])}
					quizId={quizData?._id || ""}
					sendOtp={sendOTP}
					onSubmit={onSubmit}
				/>
				{extraTimeLogsModalData && (
					<LogsModal
						title="Extra Time Logs"
						name={extraTimeLogsModalData.displayname}
						email={extraTimeLogsModalData.email}
						onClose={() => setExtraTimeLogsModalData(undefined)}
						extraTimeLogsModalData={extraTimeLogsModalData}
					>
						<ExtraTimeLogs
							testId={quizData?._id}
							userId={extraTimeLogsModalData._id}
							scroll={{ y: 200 }}
						/>
					</LogsModal>
				)}
			</Flex>
		</>
	);
};

export default MeetingRoom;
