import { QuestionSupportedLanguage } from "@/constants/languages";

export const aceEditorModes: Record<number, string> = {
	[QuestionSupportedLanguage.C]: "c_cpp",
	[QuestionSupportedLanguage.C99]: "c_cpp",
	[QuestionSupportedLanguage["C++"]]: "c_cpp",
	[QuestionSupportedLanguage["C++11"]]: "c_cpp",
	[QuestionSupportedLanguage["C++14"]]: "c_cpp",
	[QuestionSupportedLanguage.Java]: "java",
	[QuestionSupportedLanguage.JavaScript]: "javascript",
	[QuestionSupportedLanguage.Bash]: "sh",
	[QuestionSupportedLanguage.SQL]: "sql",
	[QuestionSupportedLanguage.MySql]: "sql",
	[QuestionSupportedLanguage.Oracle]: "sql",
	[QuestionSupportedLanguage["Python 2"]]: "python",
	[QuestionSupportedLanguage["Python 3"]]: "python",
	[QuestionSupportedLanguage.Ruby]: "ruby",
	[QuestionSupportedLanguage["C#"]]: "csharp",
	[QuestionSupportedLanguage.Php]: "php",
	[QuestionSupportedLanguage["React-Jest"]]: "javascript",
};

export const getAceEditorMode = (lang: number) => {
	const value = aceEditorModes[lang] ?? undefined;
	return value;
};
