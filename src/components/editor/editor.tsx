import AceEditor, { IAceOptions } from "react-ace";

import "ace-builds/src-noconflict/mode-java";
import "ace-builds/src-noconflict/mode-c_cpp";
import "ace-builds/src-noconflict/mode-python";
import "ace-builds/src-noconflict/mode-sql";
import "ace-builds/src-noconflict/mode-sh";
import "ace-builds/src-noconflict/mode-ruby";
import "ace-builds/src-noconflict/mode-csharp";
import "ace-builds/src-noconflict/mode-php";
import "ace-builds/src-noconflict/ext-language_tools";
import "ace-builds/src-min-noconflict/ace";
import "ace-builds/src-noconflict/ext-beautify";
import "ace-builds/src-noconflict/ext-simple_tokenizer";
import "ace-builds/src-noconflict/ext-searchbox";
import { useMemo } from "react";
import { getAceEditorMode } from "./utils";
import type { Editor } from "ace-builds";

interface CustomAceOptions extends IAceOptions {
	useWorker: undefined;
	readOnly: undefined;
}

interface EditorProps {
	lang: number;
	theme?: string;
	className?: string;
	readOnly?: boolean;
	enableSyntaxSupport?: boolean;
	value?: string;
	styleConfig?: { lineHeight?: number; fontSize?: number };
	size: {
		width?: string;
		height?: string;
	};
	editorOptions?: CustomAceOptions;
	onLoad?: (editor: Editor) => void;
	onChange?: (value: string, event?: never) => void | undefined;
}

export const CQEditor = (props: EditorProps) => {
	const {
		lang,
		theme,
		className,
		value,
		editorOptions,
		readOnly,
		enableSyntaxSupport,
		size,
		styleConfig,
		onChange,
		onLoad,
	} = props;
	const mode = getAceEditorMode(lang);
	const derivedConfig: Partial<IAceOptions> = useMemo(() => {
		const config: Partial<IAceOptions> = {};
		if (readOnly) {
			config.highlightActiveLine = false;
			config.highlightGutterLine = false;
			config.highlightSelectedWord = true;
			config.showLineNumbers = false;
			config.highlightActiveLine = false;
			config.highlightGutterLine = false;
		}
		if (enableSyntaxSupport) {
			config.enableBasicAutocompletion = true;
			config.enableLiveAutocompletion = true;
			config.enableSnippets = true;
			config.behavioursEnabled = true;
			config.wrapBehavioursEnabled = true;
		}
		return config;
	}, [enableSyntaxSupport, readOnly]);
	return (
		<AceEditor
			mode={mode}
			theme={theme ?? "textmate"}
			value={value}
			height={size.height}
			width={size.width}
			onChange={onChange}
			onLoad={onLoad}
			className={`${className} ${readOnly ? "read-only-ace" : ""}`}
			lineHeight={styleConfig?.lineHeight ?? 24}
			fontSize={styleConfig?.fontSize ?? 16}
			editorProps={{
				$blockScrolling: true,
			}}
			setOptions={{
				...editorOptions,
				...derivedConfig,
				readOnly: readOnly ?? false,
			}}
			showPrintMargin={false}
		/>
	);
};
