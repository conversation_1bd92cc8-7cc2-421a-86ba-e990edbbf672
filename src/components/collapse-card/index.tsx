import { Card, Collapse } from "antd";
import { ItemType } from "rc-collapse/es/interface";
import React, { useCallback, useEffect, useState } from "react";

interface CollapseCardProps {
	labelTitle: React.ReactNode;
	labelDescription?: React.ReactNode;
	extra?: React.ReactNode;
	children?: React.ReactNode;
	defaultExpanded?: boolean;
	expanded?: boolean;
	disabled?: boolean;
	bordered?: boolean;
	ghost?: boolean;
	alignHeaderItems?: "center" | "flex-start" | "flex-end";
	// eslint-disable-next-line no-unused-vars
	toggle?: (value: boolean) => void;
}

export default function CollapseCard(props: CollapseCardProps) {
	const bordered = props.bordered ?? true;
	const hasChildren = props.children !== undefined;
	// const hasExtra = props.extra !== undefined;
	const [activeKey, setActiveKey] = useState<0 | 1>(
		hasChildren && props.defaultExpanded ? 1 : 0
	);

	const toggle = useCallback(
		(value: boolean) => {
			if (hasChildren) {
				return void setActiveKey(Number(value) as 0 | 1);
			}
			setActiveKey(0);
		},
		[setActiveKey, hasChildren]
	);

	useEffect(() => {
		if (props.expanded === undefined) {
			return;
		}
		toggle(Boolean(props.expanded));
	}, [toggle, hasChildren, props.expanded]);

	const items: ItemType[] = [
		{
			key: 1,
			collapsible: "icon",
			showArrow: false,
			label: (
				<Card.Meta
					title={
						<div
							style={{
								marginBottom: "0rem",
								fontSize: bordered ? undefined : "14px",
								fontWeight: bordered ? undefined : "400",
								opacity: props.disabled ? 0.5 : 1,
							}}
						>
							{props.labelTitle}
						</div>
					}
					description={
						props.labelDescription && (
							<div
								style={{
									marginTop: "-0.5em",
									fontSize: bordered ? undefined : "13px",
									maxWidth: "calc(100% - 2rem)",
								}}
							>
								{props.labelDescription}
							</div>
						)
					}
				/>
			),
			children: props.children,
			extra: props.extra,
			styles: {
				header: {
					alignItems: props.alignHeaderItems ?? "center",
					padding: bordered ? "1em 1.5em" : "0.5em 0px",
				},
				body: {
					borderTop: "1px solid #f0f0f0",
					borderColor: "#f0f0f0",
					padding: "1em 1.5em",
					backgroundColor: "Background",
				},
			},
		},
	];

	return (
		<>
			<Card
				bordered={bordered}
				style={{
					marginBottom: "0em",
					boxShadow: "none",
					backgroundColor: props.ghost ? "transparent" : undefined,
					borderColor: "#e4e4e4",
				}}
				styles={{ body: { padding: "0px" } }}
			>
				<Collapse
					bordered={false}
					ghost={!bordered || props.ghost}
					activeKey={activeKey}
					collapsible="disabled"
					items={items}
				/>
			</Card>
		</>
	);
}

interface CollapseCardAltProps {
	labelTitle: React.ReactNode;
	labelDescription?: React.ReactNode;
	children?: React.ReactNode;
	extra: (
		// eslint-disable-next-line no-unused-vars
		value: boolean,
		// eslint-disable-next-line no-unused-vars
		actions: { toggle: (value: boolean) => void }
	) => React.ReactNode;
	defaultExpanded?: boolean;
}

export function CollapseCardAlt(props: CollapseCardAltProps) {
	const hasChildren = props.children !== undefined;
	const [activeKey, setActiveKey] = useState<0 | 1>(
		hasChildren && props.defaultExpanded ? 1 : 0
	);

	const toggle = useCallback(
		(value: boolean) => {
			if (hasChildren) {
				return void setActiveKey(Number(value) as 0 | 1);
			}
			setActiveKey(0);
		},
		[setActiveKey, hasChildren]
	);

	useEffect(() => toggle(hasChildren), [hasChildren, toggle]);

	const items: ItemType[] = [
		{
			key: 1,
			collapsible: "icon",
			showArrow: false,
			label: (
				<Card.Meta
					title={
						<div style={{ marginBottom: "0rem" }}>
							{props.labelTitle}
						</div>
					}
					description={
						props.labelDescription && (
							<div style={{ marginTop: "-0.5em" }}>
								{props.labelDescription}
							</div>
						)
					}
				/>
			),
			children: props.children,
			extra: props.extra(activeKey === 1, { toggle }),
			styles: {
				header: { alignItems: "center", padding: "1em 1.5em" },
				body: { borderTop: "1px solid red", borderColor: "#e8e8e8" },
			},
		},
	];

	return (
		<>
			<Card styles={{ body: { padding: "0px" } }}>
				<Collapse
					ghost
					activeKey={activeKey}
					collapsible="disabled"
					items={items}
				/>
			</Card>
		</>
	);
}
