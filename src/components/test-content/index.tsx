/* eslint-disable no-unused-vars */
import useObjectList, {
	createReducerActionMap,
	ReducerActions,
	ReducerDispatchFunction,
	useMappedReducer,
} from "@/hooks/util";
import DragSortTable from "../drag-sort-table";
import {
	Button,
	Card,
	Checkbox,
	Collapse,
	Divider,
	Dropdown,
	Flex,
	Form,
	GetProps,
	Input,
	InputNumber,
	Layout,
	message,
	Popover,
	Radio,
	Space,
	Spin,
	Table,
	Tag,
	Tooltip,
	Typography,
} from "antd";
import {
	CheckOutlined,
	CloseOutlined,
	DeleteOutlined,
	EditOutlined,
	InfoCircleFilled,
	Loading3QuartersOutlined,
	PlusCircleOutlined,
	PlusOutlined,
	SettingOutlined,
} from "@ant-design/icons";
import { LucideChevronsUpDown } from "lucide-react";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import BetterMenu, { BetterMenuItem } from "../better-menu/better-menu";
import { FormInstance, useForm } from "antd/es/form/Form";
import { QuestionType, TestContentType } from "@/client/test-add";
import { TargetIcon } from "../icons/target";
import { CountIcon } from "../icons/count";
import { RandomIcon } from "../icons/random";
import AddQuestionModal from "../add-question-modal/AddQuestionModal";
import { AnyObject } from "antd/es/_util/type";
import confirm from "antd/es/modal/confirm";

const tagStyle = { marginRight: 0 };

import { quizClient, useAppStore } from "../../store";
import AddBulkQuestionModal from "../bulk-upload-modal/AddBulkQuestionModal";
import { useNavigate, useOutletContext, useParams } from "react-router";
import { TextLayoutContextType } from "../test-layout/test-layout";
import QuestionPreview, {
	QuestionPreviewProps,
} from "../question-preview/QuestionPreview";
import AddNewQuestionModal from "../add-new-question-modal/AddNewQuestionModal";
import { useAppMessage } from "@/hooks/message";
import { hasEditPermission } from "@/utils/editPermission";
import { RoleAction, RoleResource } from "@/constants/roles";
import QuestionTypeCloudIcon from "../question-type-cloud-icon/question-type-cloud-icon";
const collapseStyles: { [K in "header" | "body"]?: React.CSSProperties } = {
	header: {
		alignItems: "center",
		padding: "1em 1.5em",
	},
	body: {
		borderTop: "1px solid #f0f0f0",
		borderColor: "#f0f0f0",
		padding: "1em 1.5em",
		backgroundColor: "Background",
	},
};

function checkAndUpdatePoolCount(section: TestContentType) {
	if (section.isPoolQuestion && section.poolCount !== undefined) {
		if (section.poolCount === 0) {
			section.poolCount = 1;
		} else if (section.poolCount > section.questions.length) {
			section.poolCount = section.questions.length;
		}
	}
}

const sectionsReducerActionMap = createReducerActionMap<TestContentType[]>()({
	setItems(_, newItems: TestContentType[]) {
		return [newItems];
	},

	removeSection(items, index: number, count = 1) {
		const list = Array.from(items);
		const removedItems = list.splice(index, count);
		return [list, removedItems];
	},

	addSection(items, section: TestContentType, beforeIndex: number) {
		const list = Array.from(items);
		list.splice(beforeIndex, 0, section);
		return [list];
	},

	updateSectionId(items, index: number, id: string) {
		const list = Array.from(items);
		list[index] = {
			...list[index],
			key: id,
		};
		return [list];
	},

	moveSection(items, sectionKey: React.Key, toIndex: number) {
		const sections = Array.from(items);
		const fromIndex = sections.findIndex(
			section => section.key === sectionKey
		);
		const [spliceItem] = sections.splice(fromIndex, 1);
		sections.splice(toIndex, 0, spliceItem);
		return [sections];
	},

	updateSection(
		items,
		index: number,
		updateHandler: (item: TestContentType) => TestContentType
	) {
		const list = Array.from(items);
		list[index] = updateHandler(list[index]);
		return [list];
	},

	addQuestions(
		items,
		index: number,
		questions: TestContentType["questions"]
	) {
		const sections = Array.from(items);
		const section = sections[index];
		const existingQuestions = Array.from(section.questions);
		existingQuestions.push(...questions);
		section.questions = existingQuestions;
		checkAndUpdatePoolCount(section);
		return [sections];
	},

	moveQuestion(
		items,
		sectionIndex: number,
		questionId: React.Key,
		toIndex: number
	) {
		const sections = items;
		const section = sections[sectionIndex];
		const questions = Array.from(section.questions);
		const fromIndex = questions.findIndex(
			question => question._id === questionId
		);
		const [spliceItem] = questions.splice(fromIndex, 1);
		questions.splice(toIndex, 0, spliceItem);
		section.questions = questions;
		checkAndUpdatePoolCount(section);
		// console.log({ fromIndex, toIndex });
		// console.table(section.questions.map(({ title }) => title));
		return [sections];
	},

	moveQuestions(
		items,
		fromIndex: number,
		toIndex: number,
		questionIds: React.Key[]
	) {
		const sections = Array.from(items);
		const questionsIdsSet = new Set(questionIds);
		const removedQuestions = [];
		const keptQuestions = [];
		const fromSectionQuestions = sections[fromIndex].questions;
		for (const question of fromSectionQuestions) {
			if (questionsIdsSet.has(question._id)) {
				removedQuestions.push(question);
				continue;
			}
			keptQuestions.push(question);
		}
		sections[fromIndex].questions = keptQuestions;
		sections[toIndex].questions = [
			...sections[toIndex].questions,
			...removedQuestions,
		];
		checkAndUpdatePoolCount(sections[toIndex]);
		checkAndUpdatePoolCount(sections[fromIndex]);
		return [sections];
	},

	removeQuestions(items, index: number, questionIds: React.Key[]) {
		const sections = Array.from(items);
		const questionsIdsSet = new Set(questionIds);
		const keptQuestions = [];
		const fromSectionQuestions = sections[index].questions;
		for (const question of fromSectionQuestions) {
			if (!questionsIdsSet.has(question._id)) {
				keptQuestions.push(question);
			}
		}
		sections[index].questions = keptQuestions;
		checkAndUpdatePoolCount(sections[index]);
		return [sections];
	},

	updateQuestion(
		items,
		sectionIndex: number,
		questionIndex: number,
		updateHandler: (
			item: TestContentType["questions"][number]
		) => TestContentType["questions"][number]
	) {
		const sections = Array.from(items);
		const section = sections[sectionIndex];
		const questions = Array.from(section.questions);
		questions[questionIndex] = updateHandler(questions[questionIndex]);
		section.questions = questions;
		return [sections];
	},
});

type SectionsReducerActionMap = typeof sectionsReducerActionMap;

const enum ChangesState {
	SAVING = -1,
	UNSAVED,
	SAVED,
}

export default function TestContent() {
	const { id: testId = "", token } = useParams();
	const navigate = useNavigate();
	const messageAPI = useAppMessage();
	const { hasResourcePermission } = useAppStore();
	const [changesState, setChangesState] = useState<ChangesState>(
		ChangesState.SAVED
	);
	const hasUnsavedChanges = changesState !== ChangesState.SAVED;

	const { tabsData, testContent, setTestContent, isFrozen, isFromToken } =
		useOutletContext<TextLayoutContextType>();

	const languagesAllowed = tabsData?.content?.languagesAllowed ?? [];

	const updateContentOnServer = useCallback(
		function (sections: TestContentType[]) {
			(async function () {
				setChangesState(ChangesState.SAVING);
				try {
					await quizClient.updateTestContentOrder(testId, sections);
					messageAPI?.success("Changes saved");
				} catch (ex) {
					const error = (ex as AnyObject)?.message ?? ex;
					messageAPI?.error(error);
				}
				setChangesState(ChangesState.SAVED);
			})();
		},
		[messageAPI, testId]
	);

	const addSectionOnServer = useCallback(
		function (
			title: string,
			onSuccess?: (sectionIndex: number, sectionId: string) => void
		) {
			(async function () {
				setChangesState(ChangesState.SAVING);
				try {
					const { index, section } =
						await quizClient.createTestContentSection(
							testId,
							title
						);
					onSuccess?.(index, section._id);
					messageAPI?.success("Added section");
				} catch (ex) {
					const error = (ex as AnyObject)?.message ?? ex;
					messageAPI?.error(error);
				}
				setChangesState(ChangesState.SAVED);
			})();
		},
		[messageAPI, testId]
	);

	const addSectionQuestionsOnServer = useCallback(
		function (
			sections: TestContentType[],
			sectionIndex: number,
			questions: { _id: string; type: QuestionType }[]
		) {
			(async function () {
				setChangesState(ChangesState.SAVING);
				try {
					const section = sections[sectionIndex];
					await quizClient.addTestContentSectionQuestions(
						testId,
						String(sectionIndex),
						questions,
						section.poolCount ?? 0,
						section.isPoolQuestion ?? false
					);
					messageAPI?.success("Content added successfully");
				} catch (ex) {
					const error = (ex as AnyObject)?.message ?? ex;
					messageAPI?.error(error);
				}
				setChangesState(ChangesState.SAVED);
			})();
		},
		[messageAPI, testId]
	);

	const removeSectionQuestionsFromServer = useCallback(
		function (
			sectionIndex: number,
			questionIds: string[],
			poolCount: number,
			isPoolQuestion: boolean,
			questionCount: number
		) {
			(async function () {
				setChangesState(ChangesState.SAVING);
				try {
					if (isPoolQuestion && questionCount <= poolCount) {
						await quizClient.updateTestContentSectionPoolCount(
							testId,
							String(sectionIndex),
							poolCount,
							isPoolQuestion
						);
					}
					await quizClient.removeTestContentSectionQuestions(
						testId,
						sectionIndex,
						questionIds
					);
					messageAPI?.success("Question(s) removed");
				} catch (ex) {
					const error = (ex as AnyObject)?.message ?? ex;
					messageAPI?.error(error);
				}
				setChangesState(ChangesState.SAVED);
			})();
		},
		[messageAPI, testId]
	);

	const onSectionDataChange = useCallback(
		function (
			items: TestContentType[],
			action: ReducerActions<TestContentType[], SectionsReducerActionMap>
		) {
			switch (action.type) {
				case "setItems":
				case "updateSectionId":
				case "addSection":
					break;
				case "moveSection":
				case "moveQuestion":
					setChangesState(ChangesState.UNSAVED);
					break;
				case "addQuestions":
					{
						const [sectionIndex, questions] = action.data;
						addSectionQuestionsOnServer(
							items,
							sectionIndex,
							questions
						);
					}
					break;
				case "removeQuestions":
					{
						const [sectionIndex, questionIds] = action.data;
						removeSectionQuestionsFromServer(
							sectionIndex,
							questionIds.map(id => id.toString()),
							items[sectionIndex].poolCount ?? 0,
							items[sectionIndex].isPoolQuestion ?? false,
							items[sectionIndex].questions?.length
						);
					}
					break;
				default:
					updateContentOnServer(items);
			}
		},
		[
			addSectionQuestionsOnServer,
			removeSectionQuestionsFromServer,
			updateContentOnServer,
		]
	);

	const [sections, sectionsDispatch] = useMappedReducer<
		TestContentType[],
		SectionsReducerActionMap
	>(
		sectionsReducerActionMap,
		testContent?.sections ?? [],
		onSectionDataChange
	);

	const refetchData = useCallback(
		function () {
			(async () => {
				try {
					let content;
					if (token) {
						content = await quizClient.getContentWithToken(token);
					} else {
						content = await quizClient.getTestContent(testId);
					}
					console.log("content---------------------", tabsData);
					setTestContent(content);
					sectionsDispatch("setItems", content.sections);
				} catch (ex) {
					console.error(ex);
					messageAPI?.error(
						ex instanceof Error ? ex.message : String(ex)
					);
					navigate("/tests");
					console.log(ex);
				}
			})();
		},
		[messageAPI, navigate, sectionsDispatch, setTestContent, testId, token]
	);

	useEffect(
		function () {
			if (testContent !== undefined) {
				return;
			}
			refetchData();
		},
		[refetchData, testContent]
	);

	useEffect(
		function () {
			if (changesState !== ChangesState.SAVED) {
				return;
			}
			refetchData();
		},
		[refetchData, changesState]
	);

	function toggleUnsavedChanges(value: boolean) {
		setChangesState(value ? ChangesState.UNSAVED : ChangesState.SAVED);
	}

	async function saveUnsavedChanges() {
		const oldState = changesState;
		try {
			setChangesState(ChangesState.SAVING);
			await quizClient.updateTestContentOrder(testId, sections);
			setChangesState(ChangesState.SAVED);
			messageAPI?.success("Changes saved");
		} catch (ex) {
			messageAPI?.error(ex as string);
			setChangesState(oldState);
		}
	}

	async function clearUnsavedChanges() {
		setChangesState(ChangesState.SAVED);
	}

	async function addSection(beforeIndex = sections.length + 1) {
		const index =
			beforeIndex <= 0
				? 0
				: beforeIndex > sections.length
					? sections.length
					: beforeIndex;
		const title = `Section ${index + 1}`;
		sectionsDispatch(
			"addSection",
			{
				key: "_temp-" + Date.now().toString(36), // generate key for mongodb _id

				questions: [],
				testId,
				title,
				expanded: true,
			},
			index
		);
		addSectionOnServer(title, (index, id) =>
			sectionsDispatch("updateSectionId", index, id)
		);
	}

	const isTestContentLoaded = testContent !== undefined;

	return (
		<>
			<Flex
				align="center"
				justify="center"
				style={{
					display: isTestContentLoaded ? "none" : undefined,
					width: "100%",
					height: "100%",
				}}
			>
				<Spin />
			</Flex>
			<Layout
				style={{
					display: isTestContentLoaded ? undefined : "none",
					padding: 16,
					width: "100%",
					height: "100%",
				}}
			>
				<Layout.Content style={{ overflow: "auto" }}>
					<DragSortTable
						disabled={isFrozen || isFromToken}
						columns={[
							{
								render(_data, _record, index) {
									return (
										<EditableSection
											testId={testId}
											languagesAllowed={languagesAllowed}
											isPool={
												tabsData?.content
													?.randomQuestions ?? false
											}
											records={sections}
											hasUnsavedChanges={
												hasUnsavedChanges
											}
											toggleUnsavedChanges={
												toggleUnsavedChanges
											}
											index={index}
											recordsDispatch={sectionsDispatch}
											isFrozen={isFrozen ?? false}
											isFromToken={isFromToken ?? false}
										/>
									);
								},
							},
						]}
						dataSource={sections}
						moveByKey={true}
						moveData={(key, to) =>
							sectionsDispatch("moveSection", key, to)
						}
						showHeader={false}
						style={{
							margin: "auto",
							width: "100%",
							maxWidth: "1400px",
						}}
					/>
				</Layout.Content>

				<Layout.Footer
					style={{
						position: "sticky",
						bottom: "0px",
						margin: "auto",
						marginBottom: "-16px",
						padding: "16px 0px 24px",
						width: "100%",
						maxWidth: "1400px",
						backgroundColor: "#f0f0f0",
						zIndex: 1,
					}}
				>
					<Flex
						justify="space-between"
						gap={16}
						style={{ width: "100%" }}
					>
						<Space size="middle">
							{hasResourcePermission(
								RoleResource.QUIZ,
								RoleAction.CREATE_SEGMENTS
							) && (
								<Button
									type="dashed"
									icon={<PlusOutlined />}
									disabled={
										hasUnsavedChanges ||
										isFrozen ||
										isFromToken
									}
									onClick={() => addSection()}
								>
									Add section
								</Button>
							)}
							<Dropdown
								menu={{
									defaultOpenKeys: sections
										.filter(section => section.expanded)
										.map(section => section.key),
									items: sections.map(
										(section, sectionIndex) => ({
											key: section.key,
											label:
												sectionIndex +
												1 +
												". " +
												section.title,
											onClick: () =>
												addSection(sectionIndex),
										})
									),
								}}
								trigger={["click"]}
							>
								{/* <Button
									type="dashed"
									icon={<PlusOutlined />}
									disabled={hasUnsavedChanges}
								>
									Add section before
								</Button> */}
							</Dropdown>
						</Space>
						<Space
							size="middle"
							style={{
								visibility: hasUnsavedChanges
									? "visible"
									: "hidden",
							}}
						>
							{hasUnsavedChanges ? (
								changesState === ChangesState.SAVING ? (
									<Typography.Text type="secondary">
										<Space>
											<Loading3QuartersOutlined /> saving
										</Space>
									</Typography.Text>
								) : (
									<>
										<Button
											type="default"
											onClick={clearUnsavedChanges}
										>
											Cancel
										</Button>
										<Button
											type="primary"
											onClick={saveUnsavedChanges}
										>
											Save
										</Button>
									</>
								)
							) : (
								<></>
							)}
						</Space>
					</Flex>
				</Layout.Footer>
			</Layout>
		</>
	);
}

function EditableContent(props: {
	value: string;
	hasUnsavedChanges: boolean;
	toggleUnsavedChanges: (value: boolean) => void;
	onChange?: (value: string) => void;
	defaultEdit?: boolean;
	placeholder?: string;
	isFrozen: boolean;
	isFromToken: boolean;
}) {
	const [isBeingEdited, setEditingState] = useState(
		props.defaultEdit ?? false
	);
	const [value, setValue] = useState(props.value?.trim());
	return (
		<Flex gap="0.5em" style={{ maxWidth: "50%" }}>
			{isBeingEdited ? (
				<>
					<Input
						size="small"
						value={value}
						onChange={event => setValue(event.target.value.trim())}
						autoFocus
						onClick={event => event.stopPropagation()}
						placeholder={props.placeholder}
						style={{ width: "100%" }}
					/>
					<Button
						type="text"
						size="small"
						style={{ color: "inherit" }}
						onClick={event => {
							event.stopPropagation();
							props.onChange?.(value);
							setEditingState(false);
						}}
					>
						<CheckOutlined />
					</Button>
				</>
			) : (
				props.value
			)}
			<Button
				type="text"
				size="small"
				style={{ color: "inherit" }}
				disabled={
					props.hasUnsavedChanges ||
					props.isFrozen ||
					props.isFromToken
				}
				onClick={event => {
					event.stopPropagation();
					if (isBeingEdited && props.value.trim().length === 0) {
						props.onChange?.("");
					}
					setEditingState(!isBeingEdited);
				}}
			>
				{isBeingEdited ? <CloseOutlined /> : <EditOutlined />}
			</Button>
		</Flex>
	);
}

interface EditableSectionProps<T> {
	recordsDispatch: ReducerDispatchFunction<
		TestContentType[],
		SectionsReducerActionMap
	>;
	testId: string;
	isPool: boolean;
	records: T[];
	hasUnsavedChanges: boolean;
	toggleUnsavedChanges: (value: boolean) => void;
	index: number;
	languagesAllowed: string[];
	isFrozen: boolean;
	isFromToken: boolean;
}

const sectionHeaderButtonProps: GetProps<typeof Button> = {
	type: "text",
	size: "middle",
	style: {
		fontSize: "1rem",
	},
	onClick(event) {
		event.stopPropagation();
	},
};

function EditableSection({
	testId,
	records,
	index,
	recordsDispatch,
	hasUnsavedChanges,
	toggleUnsavedChanges,
	isPool,
	languagesAllowed,
	isFrozen,
	isFromToken,
}: EditableSectionProps<TestContentType>) {
	const record = records[index];
	const [messageAPI, messageContextHolder] = message.useMessage({
		maxCount: 1,
	});
	const [selectedRows, setSelectedRows] = useState<React.Key[]>([]);
	const [previewQuestion, setPreviewQuestion] =
		useState<QuestionPreviewProps | null>(null);
	const { user, hasResourcePermission, session } = useAppStore();
	const questionsArray = useMemo(() => {
		return (record.questions ?? []).map(question => ({
			key: question._id,
			...question,
		}));
	}, [record.questions]);

	const { items: questions = [], move: moveQuestions } =
		useObjectList(questionsArray);

	const totalScore = questions?.reduce(
		(result, question) => result + question.score,
		0
	);

	const allQuestions: React.Key[] = useMemo(() => {
		return records.flatMap(record => {
			return (record.questions ?? []).map(ques => ques._id);
		});
	}, [records]);

	const areQuestionsOrScoresMixed = useMemo(
		function () {
			if (!isPool) {
				return false;
			}
			const questionsTypeSet = new Set(
				questions.map(question => question.type)
			);
			if (questionsTypeSet.size > 1) {
				return true;
			}
			const questionsScoreSet = new Set(
				questions.map(question => question.score)
			);
			if (questionsScoreSet.size > 1) {
				return true;
			}
		},
		[isPool, questions]
	);

	useEffect(() => {
		setSelectedRows(selectedRows => {
			// console.log("selectedRows before Update", selectedRows);
			const selectedRowsAfterUpdate = [];
			for (const question of questions) {
				if (selectedRows.includes(question._id)) {
					selectedRowsAfterUpdate.push(question._id);
				}
			}
			// console.log("selectedRowsAfterUpdate", selectedRowsAfterUpdate);
			return selectedRowsAfterUpdate;
		});
	}, [questions, setSelectedRows]);

	const [selectedCoding, setSelectedCoding] = useState<boolean>(false);

	useEffect(() => {
		let allcoding = true;
		for (const question of questions) {
			if (selectedRows.includes(question._id)) {
				if (question.type !== QuestionType.CODING) {
					allcoding = false;
					break;
				}
			}
		}
		setSelectedCoding(allcoding);
	}, [questions, selectedRows]);

	const setQuestionSettings = useCallback(
		function (
			questionIndex: number,
			value: TestContentType["questions"][number]["settings"]
		) {
			recordsDispatch(
				"updateQuestion",
				index,
				questionIndex,
				question => {
					console.log(question.settings, value);
					question.settings = value;
					return { ...question };
				}
			);
		},
		[index, recordsDispatch]
	);

	const setCodingQuestionExecutionType = useCallback(
		function (value: TestContentType["questions"][number]["settings"]) {
			recordsDispatch("updateSection", index, section => {
				const questions = section.questions.map(question => {
					if (
						question.type === QuestionType.CODING &&
						selectedRows.includes(question._id)
					) {
						return { ...question, settings: value };
					}
					return question;
				});
				return { ...section, questions };
			});
		},
		[index, recordsDispatch, selectedRows]
	);

	const moveSelectedQuestions = useCallback(
		function (toIndex: number) {
			recordsDispatch("moveQuestions", index, toIndex, selectedRows);
			setSelectedRows([]);
		},
		[index, recordsDispatch, selectedRows]
	);

	const removeSelectedQuestions = useCallback(
		function (quesId?: string) {
			if (quesId) {
				recordsDispatch("removeQuestions", index, [quesId]);
				return;
			}
			recordsDispatch("removeQuestions", index, selectedRows);
			setSelectedRows([]);
		},
		[index, recordsDispatch, selectedRows]
	);

	const [isQuestionModalOpen, toggleQuestionAddModal] = useState(false);
	const [isNewQuestionModalOpen, toggleNewQuestionAddModal] = useState(false);
	const [isBulkModalOpen, toggleBulkAddModal] = useState(false);

	function openQuestionAddModal() {
		toggleQuestionAddModal(true);
	}

	function openNewQuestionAddModal() {
		toggleNewQuestionAddModal(true);
	}

	async function previewQuestionModalHandler(quesId: string) {
		try {
			const response = await quizClient.getDescription(quesId);
			const { title, type } =
				questions.find(question => question._id === quesId) ?? {};
			setPreviewQuestion({
				title: title ?? "",
				type: type as QuestionType,
				description: response.title,
				count: response.testCase ?? 0,
				id: quesId,
			});
		} catch (ex) {
			const error = (ex as AnyObject)?.message ?? ex;
			messageAPI?.error(error);
		}
	}

	return (
		<>
			{messageContextHolder}
			<Card bordered={true} styles={{ body: { padding: "0px" } }}>
				<Collapse
					ghost
					size="small"
					expandIconPosition="end"
					items={[
						{
							styles: collapseStyles,
							label: (
								<Card.Meta
									title={
										<Flex
											gap={8}
											align="center"
											style={{ width: "100%" }}
										>
											<span
												style={{
													color: "#888",
												}}
											>
												{index + 1 + "."}
											</span>
											<EditableContent
												placeholder="Type section title here"
												value={record.title}
												hasUnsavedChanges={
													hasUnsavedChanges
												}
												toggleUnsavedChanges={
													toggleUnsavedChanges
												}
												onChange={value =>
													recordsDispatch(
														"updateSection",
														index,
														section => {
															section.title =
																value;
															return section;
														}
													)
												}
												isFrozen={isFrozen}
												isFromToken={isFromToken}
											/>
										</Flex>
									}
									description={
										<Space>
											<Tooltip title="No. of questions">
												<Space size="small">
													<CountIcon /> Questions:
													{record.questions?.length}
												</Space>
											</Tooltip>

											<Divider type="vertical" />

											<Tooltip title="Total score">
												<Space size="small">
													<TargetIcon /> Score:
													{totalScore}
												</Space>
											</Tooltip>

											{isPool && (
												<>
													<Divider type="vertical" />
													<Tooltip title="Random questions">
														<Space size="small">
															<RandomIcon /> Pool
															count:
															{record.poolCount ??
																0}
														</Space>
													</Tooltip>
												</>
											)}
										</Space>
									}
								/>
							),
							children: (
								<Flex
									vertical
									gap="1rem"
									style={{ width: "100%" }}
								>
									{selectedRows.length > 0 && (
										<Space
											style={{
												justifyContent: "space-between",
											}}
										>
											<span
												style={{
													color: "GrayText",
												}}
											>
												{selectedRows.length} row(s)
												selected
											</span>

											<Space>
												<Dropdown
													menu={{
														items: records.map(
															(
																section,
																sectionIndex
															) => ({
																key: section.key,
																label:
																	sectionIndex +
																	1 +
																	". " +
																	section.title,
																disabled:
																	section.key ===
																	record.key,
																onClick() {
																	moveSelectedQuestions(
																		sectionIndex
																	);
																},
															})
														),
													}}
													trigger={["click"]}
												>
													<Button
														disabled={
															hasUnsavedChanges
														}
														icon={
															<LucideChevronsUpDown
																size={14}
															/>
														}
													>
														Move to
													</Button>
												</Dropdown>

												<Button
													disabled={hasUnsavedChanges}
													icon={<DeleteOutlined />}
													onClick={() => {
														confirm({
															title: "Are you sure you want to delete these questions?",
															type: "confirm",
															onOk() {
																removeSelectedQuestions();
															},
														});
													}}
												>
													Delete
												</Button>
												{selectedCoding && (
													<QuestionSettingsPopoverForm
														value={{
															contentHead: false,
															contentTail: false,
															customInput: false,
															result: "1",
														}}
														onChangeMultiple={
															setCodingQuestionExecutionType
														}
														hasUnsavedChanges={
															hasUnsavedChanges
														}
														isFrozen={isFrozen}
														isFromToken={
															isFromToken
														}
														isFromMultipleSelection={
															true
														}
													/>
												)}
											</Space>
										</Space>
									)}
									<DragSortTable
										disabled={isFrozen || isFromToken}
										columns={[
											Table.SELECTION_COLUMN,
											{
												width: 64,
												align: "right",
												render(_0, _1, index) {
													return index + 1 + ".";
												},
											},
											{
												dataIndex: "title",
												title: "Title",
												width: 300,
												ellipsis: true,
												render(value, record) {
													return (
														<Tooltip
															title={"Preview"}
															placement="left"
														>
															<Button
																size="small"
																type="link"
																onClick={() =>
																	previewQuestionModalHandler(
																		record._id
																	)
																}
															>
																{value}
															</Button>
														</Tooltip>
													);
												},
											},
											{
												dataIndex: "type",
												title: "Type",
												render(_value, record) {
													return (
														<QuestionTypeCloudIcon
															question={record}
														/>
													);
												},
											},
											{
												dataIndex: "score",
												align: "center",
												title: "Score",
											},
											{
												dataIndex: "setting",
												title: "Settings",
												render(_value, record) {
													return record.type ===
														QuestionType.CODING ? (
														<Space
															size="small"
															wrap
														>
															<Tag
																style={tagStyle}
															>
																{
																	{
																		"1": "Only compile",
																		"2": "Only result",
																		"3": "Result with test cases",
																	}[
																		record
																			?.settings
																			?.result ??
																			"2"
																	]
																}
															</Tag>
															{record.settings
																?.contentHead ? (
																<Tag
																	key="contentHead"
																	style={
																		tagStyle
																	}
																>
																	Show head
																</Tag>
															) : (
																<></>
															)}
															{record.settings
																?.contentTail ? (
																<Tag
																	key="contentTail"
																	style={
																		tagStyle
																	}
																>
																	Show tail
																</Tag>
															) : (
																<></>
															)}
															{record.settings
																?.customInput && (
																<Tag
																	style={
																		tagStyle
																	}
																>
																	Custom input
																</Tag>
															)}
														</Space>
													) : (
														"NA"
													);
												},
											},
											{
												render(_value, record, index) {
													console.log(
														"record",
														record
													);
													return (
														<Space size="small">
															{record.type ===
																QuestionType.CODING && (
																<QuestionSettingsPopoverForm
																	value={
																		record.settings
																	}
																	onChange={
																		setQuestionSettings
																	}
																	index={
																		index
																	}
																	hasUnsavedChanges={
																		hasUnsavedChanges
																	}
																	isFrozen={
																		isFrozen
																	}
																	isFromToken={
																		isFromToken
																	}
																/>
															)}

															{hasEditPermission(
																{
																	createdById:
																		record.createdBy,
																	orgId: record.orgId,
																},
																session,
																user.role?.id,
																hasResourcePermission
															) && (
																<Button
																	{...sectionHeaderButtonProps}
																	disabled={
																		hasUnsavedChanges ||
																		isFrozen ||
																		isFromToken
																	}
																	icon={
																		<EditOutlined />
																	}
																	onClick={event => {
																		event.stopPropagation();
																		window.open(
																			`/questions/add/${record._id}`,
																			"_blank"
																		);
																	}}
																/>
															)}

															<Button
																{...sectionHeaderButtonProps}
																disabled={
																	hasUnsavedChanges ||
																	isFrozen ||
																	isFromToken
																}
																icon={
																	<DeleteOutlined />
																}
																onClick={() => {
																	confirm({
																		title: "Are you sure you want to delete this question?",
																		type: "confirm",

																		onOk() {
																			removeSelectedQuestions(
																				record._id
																			);
																		},
																	});
																}}
															/>
														</Space>
													);
												},
											},
										]}
										dataSource={questions}
										moveByKey={true}
										moveData={(key, to) => {
											const from = questions.findIndex(
												q => q._id === key
											);
											moveQuestions(from, to);
											recordsDispatch(
												"moveQuestion",
												index,
												key,
												to
											);
										}}
										rowSelection={
											hasUnsavedChanges ||
											isFrozen ||
											isFromToken
												? undefined
												: {
														onChange(selectedRows) {
															setSelectedRows(
																selectedRows
															);
														},
														selectedRowKeys:
															selectedRows,
													}
										}
									/>
									<Space>
										<Button
											type="dashed"
											icon={<PlusCircleOutlined />}
											disabled={
												hasUnsavedChanges ||
												isFrozen ||
												isFromToken
											}
											onClick={openQuestionAddModal}
										>
											Add questions
										</Button>
										<Button
											type="dashed"
											icon={<PlusCircleOutlined />}
											disabled={
												hasUnsavedChanges ||
												isFrozen ||
												isFromToken
											}
											onClick={openNewQuestionAddModal}
										>
											Add new question
										</Button>
										<Button
											type="dashed"
											icon={<PlusCircleOutlined />}
											disabled={
												hasUnsavedChanges ||
												isFrozen ||
												isFromToken
											}
											onClick={() =>
												toggleBulkAddModal(true)
											}
										>
											Bulk add MCQs
										</Button>
									</Space>
								</Flex>
							),
							extra: (
								<Space>
									{isPool && (
										<Tooltip title="Mixing different question types and scores in a section can lead to uneven difficulty and unfair grading.">
											<Typography.Text
												type={
													areQuestionsOrScoresMixed
														? "warning"
														: "secondary"
												}
												style={{
													marginRight: 8,
													marginBottom: 0,
													fontSize: 18,
												}}
											>
												<InfoCircleFilled />
											</Typography.Text>
										</Tooltip>
									)}
									<Tooltip
										title="Delete section"
										mouseEnterDelay={0.5}
									>
										<Button
											{...sectionHeaderButtonProps}
											disabled={
												hasUnsavedChanges ||
												isFrozen ||
												isFromToken
											}
											icon={<DeleteOutlined />}
											onClick={event => {
												event.stopPropagation();
												confirm({
													title: "Are you sure you want to delete this Section?",
													type: "confirm",
													onOk() {
														recordsDispatch(
															"removeSection",
															index
														);
													},
												});
											}}
										/>
									</Tooltip>
									{isPool && (
										<SectionSettingPopoverForm
											index={index}
											record={record}
											value={{
												poolCount:
													record.poolCount ?? 1,
											}}
											onChange={(index, value) =>
												recordsDispatch(
													"updateSection",
													index,
													section => {
														section.poolCount =
															value.poolCount;
														return section;
													}
												)
											}
											onClick={event =>
												event.stopPropagation()
											}
										>
											<Button
												{...sectionHeaderButtonProps}
												disabled={
													hasUnsavedChanges ||
													isFrozen ||
													isFromToken
												}
												onClick={event =>
													event.stopPropagation()
												}
												icon={<SettingOutlined />}
											/>
										</SectionSettingPopoverForm>
									)}
								</Space>
							),
						},
					]}
				></Collapse>
			</Card>
			<AddQuestionModal
				open={isQuestionModalOpen}
				toggle={toggleQuestionAddModal}
				ignoredKeys={allQuestions}
				languagesAllowed={languagesAllowed}
				onAccept={(questions, poolCount) => {
					if (isPool) {
						record.poolCount = poolCount;
					}
					recordsDispatch("addQuestions", index, questions);
					toggleQuestionAddModal(false);
				}}
				isPoolQuestion={isPool}
				questionsCount={questions.length}
				poolCount={record.poolCount || 0}
				quizId={testId}
			/>
			<AddNewQuestionModal
				open={isNewQuestionModalOpen}
				toggle={toggleNewQuestionAddModal}
				onAccept={question => {
					recordsDispatch("addQuestions", index, question);
					toggleNewQuestionAddModal(false);
				}}
			/>
			<AddBulkQuestionModal
				testId={testId}
				sectionIndex={index}
				open={isBulkModalOpen}
				toggle={toggleBulkAddModal}
				onInit={() => toggleUnsavedChanges(true)}
				onFinish={() => toggleUnsavedChanges(false)}
			/>
			{previewQuestion && (
				<QuestionPreview
					title={previewQuestion.title}
					count={previewQuestion.count}
					description={previewQuestion.description ?? ""}
					type={previewQuestion.type}
					onClose={() => setPreviewQuestion(null)}
					id={previewQuestion.id}
				/>
			)}
		</>
	);
}

function BasePopoverForm<T extends AnyObject | undefined>(props: {
	initialValues?: Partial<T>;
	items: BetterMenuItem[];
	onChange?: (value: T) => void;
	form?: FormInstance;
	onClick?: React.MouseEventHandler<HTMLFormElement>;
	children?: React.ReactNode;
}) {
	const [form] = useForm<T>(props.form);
	function handleFormSubmission(values: T) {
		props.onChange?.(values);
		toggle(false);
	}
	const [open, toggle] = useState(false);
	return (
		<>
			<Popover
				open={open}
				onOpenChange={toggle}
				placement="left"
				trigger="click"
				style={{
					backgroundColor: "Background",
					padding: 0,
				}}
				styles={{
					body: {
						// padding: 0,
					},
				}}
				content={
					<Form
						form={form}
						onFinish={handleFormSubmission}
						onReset={() => toggle(false)}
						onClick={props.onClick}
						initialValues={props.initialValues}
					>
						<BetterMenu items={props.items} />
					</Form>
				}
				getPopupContainer={triggerNode =>
					triggerNode.parentElement || document.body
				}
			>
				{props.children}
			</Popover>
		</>
	);
}

type QuestionSettings = TestContentType["questions"][number]["settings"];

function QuestionSettingsPopoverForm(props: {
	value: QuestionSettings;
	index?: number;
	hasUnsavedChanges?: boolean;
	isFrozen?: boolean;
	isFromToken?: boolean;
	isFromMultipleSelection?: boolean;
	onChange?: (index: number, value: QuestionSettings) => void;
	onChangeMultiple?: (value: QuestionSettings) => void;
}) {
	const settings = props.value ?? {
		contentHead: true,
		contentTail: true,
		customInput: true,
		result: "0",
	};
	function handleFormSubmission(values: QuestionSettings) {
		if (props.isFromMultipleSelection) {
			props.onChangeMultiple?.(values);
		} else if (props.index !== undefined) {
			props.onChange?.(props.index, values);
		}
	}
	return (
		<BasePopoverForm
			initialValues={settings}
			onChange={handleFormSubmission}
			items={[
				{
					type: "group",
					key: "result",
					label: "Result",
					children: (
						<Form.Item name="result">
							<Radio.Group
								style={{
									display: "flex",
									flexDirection: "column",
									gap: 8,
								}}
							>
								{[
									{
										key: "1",
										label: "Only compile",
										type: "item",
									},
									{
										key: "2",
										label: "Only result",
										type: "item",
									},
									{
										key: "3",
										label: "Result with test cases",
										type: "item",
									},
								].map(item => (
									<Radio key={item.key} value={item.key}>
										{item.label}
									</Radio>
								))}
							</Radio.Group>
						</Form.Item>
					),
				},
				{ type: "divider" },
				{
					type: "group",
					key: "content",
					label: "Code content",
					children: (
						<Flex vertical gap={8}>
							<Form.Item<QuestionSettings>
								noStyle
								name="contentHead"
								valuePropName="checked"
							>
								<Checkbox>Show head</Checkbox>
							</Form.Item>
							<Form.Item<QuestionSettings>
								noStyle
								name="contentTail"
								valuePropName="checked"
							>
								<Checkbox>Show tail</Checkbox>
							</Form.Item>
						</Flex>
					),
				},
				{ type: "divider" },
				{
					type: "group",
					children: (
						<Form.Item<QuestionSettings>
							noStyle
							name="customInput"
							valuePropName="checked"
							initialValue={settings.customInput}
						>
							<Checkbox>Custom input</Checkbox>
						</Form.Item>
					),
				},
				{ type: "divider" },
				{
					type: "group",
					children: (
						<Flex gap={8}>
							<Form.Item<QuestionSettings> noStyle>
								<Button
									type="primary"
									htmlType="submit"
									style={{ width: "100%" }}
								>
									Apply
								</Button>
							</Form.Item>
							<Form.Item<QuestionSettings> noStyle>
								<Button
									type="default"
									htmlType="reset"
									style={{ width: "100%" }}
								>
									Cancel
								</Button>
							</Form.Item>
						</Flex>
					),
				},
			]}
		>
			<Button
				type="text"
				icon={<SettingOutlined />}
				disabled={
					props.hasUnsavedChanges ||
					props.isFrozen ||
					props.isFromToken
				}
			></Button>
		</BasePopoverForm>
	);
}

interface SectionSetting {
	poolCount: number;
}

function SectionSettingPopoverForm(props: {
	value: SectionSetting;
	index: number;
	record: TestContentType;
	onChange?: (index: number, value: SectionSetting) => void;
	children?: React.ReactNode;
	onClick?: React.MouseEventHandler<HTMLFormElement>;
}) {
	const [form] = useForm<SectionSetting>();

	useEffect(() => {
		form.setFieldValue("poolCount", props.value.poolCount);
	}, [form, props.value]);

	return (
		<BasePopoverForm
			initialValues={props.value}
			form={form}
			onChange={value => props.onChange?.(props.index, value)}
			items={[
				{
					type: "group",
					key: "poolCount",
					label: "Pool count",
					children: (
						<Form.Item<SectionSetting>
							name="poolCount"
							// noStyle
							rules={[
								() => ({
									validator(_, value) {
										if (value === null) {
											return Promise.reject(
												new Error(
													"Pool Count is Required"
												)
											);
										}
										if (
											(value < 0 || value > 0) &&
											props.record.questions.length === 0
										) {
											return Promise.reject(
												"Must be zero"
											);
										}
										if (
											value <= 0 &&
											props.record.questions.length !== 0
										) {
											return Promise.reject(
												new Error(
													"Must be greater than 0."
												)
											);
										}
										if (
											value >
											props.record.questions.length
										) {
											return Promise.reject(
												new Error(
													"Must be less than total questions"
												)
											);
										}
										return Promise.resolve();
									},
								}),
							]}
						>
							<InputNumber
								size="small"
								style={{ width: "100%", minWidth: "15rem" }}
							/>
						</Form.Item>
					),
				},
				{ type: "divider" },
				{
					type: "group",
					key: "actions",
					children: (
						<Flex gap={8}>
							<Form.Item noStyle>
								<Button
									type="primary"
									htmlType="submit"
									style={{ width: "100%" }}
								>
									Apply
								</Button>
							</Form.Item>
							<Form.Item noStyle>
								<Button
									type="default"
									htmlType="reset"
									style={{ width: "100%" }}
								>
									Cancel
								</Button>
							</Form.Item>
						</Flex>
					),
				},
			]}
			children={props.children}
			onClick={props.onClick}
		/>
	);
}
