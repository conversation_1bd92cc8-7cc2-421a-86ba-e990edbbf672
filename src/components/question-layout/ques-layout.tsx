import { Flex, Layout, Space, Spin, Tabs, Typography } from "antd";
import { useEffect, useMemo, useState } from "react";
import { useNavigate, useParams, useSearchParams } from "react-router";
import QuesEditBaseForm from "../ques-edit-form/ques-edit-form";
import AddTestCase from "../ques-edit-form/addTestCase";
import { useQuestionStore } from "../../store";
import { QuestionSolution } from "../question-solution";
import { useAppMessage } from "../../hooks/message";
import PageHelmet from "../page-helmet/PageHelmet";

const QuesLayout = ({
	isFromTestModal = false,
	quesIdFromModal,
}: {
	isFromTestModal?: boolean;
	quesIdFromModal?: string;
}) => {
	const messageInstance = useAppMessage();
	const { quesId } = useParams();
	const navigate = useNavigate();
	const { initialQuestionData, getInitialQuest } = useQuestionStore();
	const [id, setQuesId] = useState<string | undefined>(
		quesId ?? quesIdFromModal
	);
	const [isNewQues] = useState<boolean>(id === undefined);

	const isQuesBeingCreated = id === undefined;
	const [searchParams, setSearchParams] = useSearchParams();
	const tabId = searchParams.get("tabId") ?? "question";
	const [activeTabKey, setActiveTabKey] = useState(id ? tabId : "question");
	const [loading, setLoading] = useState(true);
	const [defaultQuesType, setDefaultQuesType] = useState<string>("4");

	const tabsItems = useMemo(() => {
		if ("ques" in initialQuestionData) {
			setDefaultQuesType(initialQuestionData.ques.type);
		}
		const allTabs = [
			{
				key: "question",
				title: "Question Details",
				children: (
					<QuesEditBaseForm
						isFromTestModal={isFromTestModal}
						setQuesId={setQuesId}
						quesId={id}
						defaultQuesType={defaultQuesType}
						setDefaultQuesType={setDefaultQuesType}
						setActiveTabKey={setActiveTabKey}
					/>
				),
			},
			{
				key: "testcase",
				title: "Test Cases",
				disabled: isQuesBeingCreated,
				children: (
					<AddTestCase
						quesId={id}
						setActiveTabKey={setActiveTabKey}
					/>
				),
			},
			{
				key: "solution",
				title: "Solution",
				disabled: isQuesBeingCreated,
				children: (
					<QuestionSolution
						quesId={id}
						isNewQues={isNewQues}
						isFromTestModal={isFromTestModal}
					/>
				),
			},
		];

		return defaultQuesType !== "4"
			? allTabs.filter(tab => tab.key === "question")
			: allTabs;
	}, [
		initialQuestionData,
		isFromTestModal,
		id,
		defaultQuesType,
		isQuesBeingCreated,
		isNewQues,
	]);

	useEffect(() => {
		setQuesId(quesId ?? quesIdFromModal);
	}, [quesId, quesIdFromModal]);

	useEffect(() => {
		(async () => {
			try {
				if (isQuesBeingCreated) {
					await getInitialQuest();
				} else {
					await getInitialQuest(id);
				}
				setLoading(false);
			} catch (error) {
				if (!isFromTestModal) {
					navigate("/questions");
				}
				messageInstance?.error(
					error instanceof Error ? error.message : String(error)
				);
				console.error(error);
			}
		})();
	}, [
		getInitialQuest,
		isQuesBeingCreated,
		id,
		messageInstance,
		navigate,
		isFromTestModal,
	]);

	return loading ? (
		<div
			style={{
				position: "absolute",
				top: "50%",
				left: "50%",
				transform: "translate(-50%, -50%)",
				display: "flex",
				justifyContent: "center",
				alignItems: "center",
				zIndex: 9999,
			}}
		>
			<Spin />
		</div>
	) : (
		<>
			<PageHelmet title="Question" />

			<Layout style={{ height: "100%" }}>
				<Layout.Header
					style={{ backgroundColor: "transparent", height: "auto" }}
				>
					<Flex
						align="flex-end"
						justify="space-between"
						style={{ height: "100%", border: "0px solid #000" }}
					>
						<Space
							direction="vertical"
							style={{ margin: "1rem 0px 0px" }}
						>
							<Flex
								align="center"
								gap="small"
								style={{ marginBottom: 8 }}
							>
								<Typography.Title
									level={2}
									style={{
										color: "#de6834",
										marginBottom: 0,
									}}
								>
									{isQuesBeingCreated
										? "Add a question"
										: "Update a question"}
								</Typography.Title>
							</Flex>
						</Space>
					</Flex>
				</Layout.Header>
				<Layout.Content>
					<Tabs
						style={{ height: "100%", overflow: "auto" }}
						renderTabBar={(props, DefaultTabBar) => (
							<DefaultTabBar
								{...props}
								style={{
									paddingLeft: "48px",
									marginBottom: 0,
									backgroundColor: "#f0f0f0",
								}}
							/>
						)}
						activeKey={activeTabKey}
						items={tabsItems.map(item => ({
							key: item.key,
							label: item.title,
							children: item.children,
							disabled: item.disabled,
							style: {
								height: "100%",
								overflow: "auto",
							},
						}))}
						onChange={key => {
							setActiveTabKey(key);
							setSearchParams({
								tabId: key,
							});
						}}
					/>
				</Layout.Content>
			</Layout>
		</>
	);
};

export default QuesLayout;
