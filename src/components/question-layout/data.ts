import { Question } from "@/testReport/data/data";
import { RcFile } from "antd/es/upload";

export interface QuestionAddReqBodyDataType {
	txtQuesTitle: string;
	type: string;
	difficultyLevel: string;
	isPremium: number;
	isFileUpload?: number;
	score: number;
	tutorialLink?: string;
	txtQues: string;
	codecomponent: string | object;
	negativeScore: number;
	txttag: string;
	progLang?: string;
	referenceLinks: string;
	bigTestCase?: boolean;
	api?: boolean;
	courseOutcomes?: string;
	bloomTaxonomy?: number | string;
	topic: string;
	quesId?: string;
	[key: `txtOpt${number}`]: string | undefined;
	[key: `chkOpt${number}`]: string | undefined;
	executionTime?: unknown;
	funcName?: unknown;
	funcType?: unknown;
	upldFile?: RcFile[];
	mcqExplanation?: string;
	txtSub?: string;
	txtInputParams?: string;
	txtExpectedOutput?: string;
	scoreip?: number;
	[key: `txtInputParams${number}`]: string | undefined;
	[key: `txtExpectedOutput${number}`]: string | undefined;
	[key: `scoreip${number}`]: number | undefined;
	isHtmlAllowed?: number;
	isCssAllowed?: number;
	isJsAllowed?: number;
	testRunnerFileContent?: unknown;
	reactRendererCode?: unknown;
	html?: string;
	css?: string;
	js?: string;
}

export interface CoodeComponent {
	[lang: string]: {
		lang: string;
		head: string;
		body: string;
		tail: string;
		solution: string | undefined;
	};
}
export interface QuestionAddRoot {
	quizId?: string;
	segmentIndex?: string;
	username: string;
	supportedLanguages: SupportedLanguage[];
	difficultyLevel: DifficultyLevelDataType;
	questionSubType: QuestionSubType;
	questionTypeOptions: QuestionTypeOptions;
	quesId: string;
	keywordsRegexp: string;
	questionTitleRegex: string;
	questionOutcomes: QuestionOutcomes;
	questionBloomTaxonomy: QuestionBloomTaxonomy;
}

export interface QuestionEditRoot {
	quesDetail: {
		quizId?: string; // Todo Change when add question in Quiz
		segmentIndex?: string;
		ques: Question;
		stringifyQues: string;
		username: string;
		supportedLanguages: SupportedLanguage[];
		difficultyLevel: DifficultyLevelDataType;
		questionSubType: QuestionSubType;
		questionTypeOptions: QuestionTypeOptions;
		keywordsRegexp: string;
		questionTitleRegex: string;
		questionOutcomes: QuestionOutcomes;
		questionBloomTaxonomy: QuestionBloomTaxonomy;
	};
}

export interface SupportedLanguage {
	code: string;
	name: string;
	isQuestionLanguage: boolean;
	isProjectLanguage: boolean;
	isIdeLanguage: boolean;
	mainFile?: string;
	defaultCode: string;
	isCustomInput: boolean;
	extension: string;
	isInterviewLanguage?: boolean;
}

export interface DifficultyLevelDataType {
	Easy: string;
	Medium: string;
	Hard: string;
}

export interface QuestionSubType {
	Basic: string;
	Practice: string;
	Premium: string;
}

export interface QuestionTypeOptions {
	coding: QuestionType;
	MCQ: QuestionType;
	multipleAnswer: QuestionType;
	web: QuestionType;
	subjective: QuestionType;
}

export interface QuestionType {
	value: string;
	label: string;
}

export interface QuestionOutcomes {
	CO1: string;
	CO2: string;
	CO3: string;
	CO4: string;
	CO5: string;
	CO6: string;
	CO7: string;
	CO8: string;
}

export interface QuestionBloomTaxonomy {
	"1": string;
	"2": string;
	"3": string;
	"4": string;
	"5": string;
	"6": string;
}

export const validateTitle = async (
	_: unknown,
	value: string,
	questionTitleRegex: string
) => {
	if (!value) {
		return Promise.reject(
			new Error("Please enter the title of the question.")
		);
	}

	if (questionTitleRegex) {
		const regex = new RegExp(questionTitleRegex.slice(1, -1));
		if (!regex.test(value)) {
			return Promise.reject(new Error(`Title is not valid`));
		}
	}
	return Promise.resolve();
};
