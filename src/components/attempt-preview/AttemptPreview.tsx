import { Divider, Flex, Typography } from "antd";
const { Title, Text } = Typography;
import "./attempt.css";
import ReactQuill from "react-quill";
import { Radio } from "antd";
import { useEffect, useState } from "react";

type ItemType = {
	sampleTest: boolean;
	attemptInMultiLine: boolean;
	codeprogexpectedoutput: string;
	codeproginputparams: string;
	_id: string;
	scoreip: number | null;
};

type BaseQuestion = {
	_id: string;
	updatedAt: string;
	createdAt: string;
	type: string;
	text: string;
	title: string;
	tags: string;
	askconf: boolean;
	createdBy: string;
	importedby: never[];
	displaystatus: number;
	score: number;
	__v: number;
	updatedBy: string;
	showhint: boolean;
	hint: null;
	explanation: null;
	isPublic: boolean;
	orgId: string;
	referenceLinks?: string[] | null;
	difficultyLevel?: string;
	executionTime?: string;
	parentIdOfCreator?: string;
	tutorialLink?: string | null;
	files?: never[];
	activityIds?: string[];
};

type MCQType = {
	questionTypeMCQ: {
		answer: string;
		options: string[];
		correctAnswers: number[];
	};
	isCqDocument: boolean;
};

type SubjectiveType = {
	questionTypeSubjective: {
		subjectiveanswer: string;
		displayFileUpload: boolean;
		displayInputBox: boolean;
		isProjectType: boolean;
	};
	isCqDocument: boolean;
};

type CodingType = {
	questionTypeCoding: {
		multipleTestCases: boolean;
		testCase: ItemType[];
		codeproglang: {
			_id: string;
			language: string;
			defaultTrimmedCode: string;
			executionTimePerTestCaseInSec: number;
			codeComponents: {
				head: string;
				body: string;
				tail: string;
				solution: string;
			};
			defaultCompileResult: {
				errors: string;
				output: string;
			};
		}[];
	};
	isCqDocument: boolean;
};

type WebType = {
	questionTypeWeb: {
		js: string;
		css: string;
		html: string;
		testCase: {
			evaluator: string;
			description: string;
			_id: string;
			scoreip: number;
		}[];
	};
};

type QuestionData = BaseQuestion &
	(
		| (MCQType & {
				type: "1";
				questionTypeCoding: null;
				questionTypeSubjective: null;
				courseId: string[];
		  })
		| (SubjectiveType & {
				type: "2";
				questionTypeMCQ: null;
				questionTypeProject: null;
				questionTypeWeb: null;
				questionTypeCoding: null;
		  })
		| (CodingType & {
				type: "4";
				minCompletionTimeInSec: number;
				questionTypeMCQ: null;
				questionTypeProject: null;
				questionTypeSubjective: null;
				questionTypeWeb: null;
		  })
		| (CodingType & {
				type: "5";
				funcType: number;
				funcName: string;
				contentUrl: string | null;
				questionTypeProject: null;
				questionTypeSubjective: null;
				questionTypeWeb: null;
				questionTypeMCQ: null;
				questionTypeCoding: {
					encryptedOutput: boolean;
					isTraceTable: boolean;
					testCase: ItemType[];
					codeproglang: [];
				};
		  })
		| (WebType & {
				type: "9";
				questionTypeCoding: {
					testCase: [];
					codeproglang: [];
					multipleTestCases: boolean;
				};
				questionTypeMCQ: { correctAnswers: []; options: [] };
		  })
	);

interface AttemptPreviewDataType {
	html: string;
	questiondata: QuestionData;
	dtFrmt: object;
}

// type:1(Mcq)
// const data1: AttemptPreviewDataType = {
// 	html: '<style>\n    .tital{ font-size:16px; font-weight:500;}\n\t.bot-border{ border-bottom:1px #f8f8f8 solid;  margin:5px 0  5px 0}\n</style>\n<fieldset>\n\n    <div ><p>What will be the output of the following program:</p><p><br></p><pre class="ql-syntax" spellcheck="false"><span class="hljs-meta">#include&lt;stdio.h&gt;</span>\r\n<span class="hljs-function"><span class="hljs-keyword">int</span> <span class="hljs-title">main</span><span class="hljs-params">()</span>\r\n</span>{\r\n <span class="hljs-keyword">int</span> var = <span class="hljs-number">062</span>;\r\n <span class="hljs-built_in">printf</span>(<span class="hljs-string">"%d"</span>, var);\r\n <span class="hljs-keyword">return</span> <span class="hljs-number">0</span>;\r\n}\r\n</pre></div>\n    <div class="clearfix"></div>\n    <div class="bot-border"></div>\n\n\n\n</fieldset>',
// 	questiondata: {
// 		_id: "5a2a870d6dc3de029e48874c",
// 		updatedAt: "2023-03-23T06:30:37.061Z",
// 		createdAt: "2017-12-08T12:35:25.082Z",
// 		type: "1",
// 		text: '<p>What will be the output of the following program:</p><p><br></p><pre class="ql-syntax" spellcheck="false"><span class="hljs-meta">#include&lt;stdio.h&gt;</span>\r\n<span class="hljs-function"><span class="hljs-keyword">int</span> <span class="hljs-title">main</span><span class="hljs-params">()</span>\r\n</span>{\r\n <span class="hljs-keyword">int</span> var = <span class="hljs-number">062</span>;\r\n <span class="hljs-built_in">printf</span>(<span class="hljs-string">"%d"</span>, var);\r\n <span class="hljs-keyword">return</span> <span class="hljs-number">0</span>;\r\n}\r\n</pre>',
// 		title: "Variables and Identifiers - 3",
// 		tags: "c,variables,identifiers",
// 		askconf: false,
// 		createdBy: "5a4de5b805f73615301195b3",
// 		questionTypeCoding: null,
// 		questionTypeMCQ: {
// 			answer: '<p><span style="color: rgb(0, 0, 0); background-color: transparent;">The constant in assignment statement is starting from 0, hence it is treated as octal value. So decimal equivalent of octal 62 (which is 50) is printed.</span></p>',
// 			correctAnswers: [2],
// 			options: ["062", "62", "50", "Does not compile"],
// 		},
// 		courseId: ["5a2fbef3a4bc6655eb24ebbb"],
// 		importedby: [],
// 		displaystatus: 1,
// 		score: 0,
// 		__v: 0,
// 		updatedBy: "5a4de5b805f73615301195b3",
// 		questionTypeSubjective: null,
// 		showhint: false,
// 		hint: null,
// 		explanation: null,
// 		referenceLinks: [""],
// 		isPublic: false,
// 		isCqDocument: true,
// 		orgId: "59f9c87bbace049edfca78cf",
// 	},
// 	dtFrmt: {},
// };

// // type :2 subjective
// const data2: AttemptPreviewDataType = {
// 	html: '<style>\n    .tital{ font-size:16px; font-weight:500;}\n\t.bot-border{ border-bottom:1px #f8f8f8 solid;  margin:5px 0  5px 0}\n</style>\n<fieldset>\n\n    <div ><p><span style="color: rgb(0, 0, 0);">Build a basic app to compile coding question using API\'s with the help of AJAX.</span></p><p><strong>User story 1:</strong> You\'ve to create a text editor to write your code, a output console to show output of your code and an option to select programming languages.</p><p><img src="data:image/png;base64,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"></p><p><strong>User story 2:</strong> There is also a button to compile the code. When you click on that button a POST request with code &amp; language ID ( Python : 0 , JavaScript : 4 , C : 7 , C++ : 77 , Java : 8) send to server.</p><p><strong>PFB the details of API </strong></p><p>URL : <a href="https://codequotient.com/api/executeCode" target="_blank">https://course.codequotient.com/api/executeCode</a></p><p>Method : POST</p><p>DATA to Send : { "code" : "" , <em> </em><span style="color: rgb(38, 50, 56);">langId : ""</span>}</p><p><br></p><p><strong>User story 3:</strong> In response you \'ll get an object with key error or codeId( your code submission ID to fetch result).</p><p>In case of error response is :</p><pre class="ql-syntax" spellcheck="false">{<span class="hljs-attr">"error"</span>:<span class="hljs-string">"Code is null"</span>}\r\n</pre><p>In case of success response is :</p><pre class="ql-syntax" spellcheck="false"> {<span class="hljs-attr">"codeId"</span>:<span class="hljs-string">"fghfghgkhk"</span>}\r\n</pre><p><strong>User story 4:</strong> If your code is submitted successfully you\'ll receive a codeId. This id is used for fetch the result after interval of time. When you get the output, clear the interval</p><p><strong>User story 5:</strong> You need to send a get request with codeId to fetch the result. In result of this request you get an object.</p><p><strong>PFB the details of API </strong></p><p>URL : <a href="https://codequotient.com/api/codeResult/:codeId" target="_blank">https://course.codequotient.com/api/codeResult/${codeId</a>}</p><p>Method : Get</p><p>If object\'s data field is empty it means result is not ready or removed from server.</p><pre class="ql-syntax" spellcheck="false">{<span class="hljs-attr">"data"</span>:{}}\r\n</pre><p>If result is ready you\'ll get output or errors in data\'s object.</p><pre class="ql-syntax" spellcheck="false">{<span class="hljs-string">"data"</span>{output: <span class="hljs-string">""</span>, langid: <span class="hljs-string">"0"</span>, code: <span class="hljs-string">""</span>, errors: <span class="hljs-string">"&nbsp;"</span>}}\r\n</pre><p><strong>User story 6:</strong> Show this in output console, and clear the interval.</p><p><strong>In case you get the response in output</strong></p><p><img src="data:image/png;base64,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"></p><p><strong>In case you get the response in errors</strong></p><p><img src="data:image/png;base64,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"></p><p><br></p><p><strong style="color: rgb(0, 0, 0);">Create this assignment in the Projects module and once you are done, submit the URL of your project link on the right side textbox.</strong></p></div>\n    <div class="clearfix"></div>\n    <div class="bot-border"></div>\n\n\n\n</fieldset>',
// 	questiondata: {
// 		_id: "5fae3656d1128d74898e7b02",
// 		updatedAt: "2024-07-01T11:26:56.734Z",
// 		createdAt: "2020-11-13T07:31:34.497Z",
// 		type: "2",
// 		text: '<p><span style="color: rgb(0, 0, 0);">Build a basic app to compile coding question using API\'s with the help of AJAX.</span></p><p><strong>User story 1:</strong> You\'ve to create a text editor to write your code, a output console to show output of your code and an option to select programming languages.</p><p><img src="data:image/png;base64,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"></p><p><strong>User story 2:</strong> There is also a button to compile the code. When you click on that button a POST request with code &amp; language ID ( Python : 0 , JavaScript : 4 , C : 7 , C++ : 77 , Java : 8) send to server.</p><p><strong>PFB the details of API </strong></p><p>URL : <a href="https://codequotient.com/api/executeCode" target="_blank">https://course.codequotient.com/api/executeCode</a></p><p>Method : POST</p><p>DATA to Send : { "code" : "" , <em> </em><span style="color: rgb(38, 50, 56);">langId : ""</span>}</p><p><br></p><p><strong>User story 3:</strong> In response you \'ll get an object with key error or codeId( your code submission ID to fetch result).</p><p>In case of error response is :</p><pre class="ql-syntax" spellcheck="false">{<span class="hljs-attr">"error"</span>:<span class="hljs-string">"Code is null"</span>}\r\n</pre><p>In case of success response is :</p><pre class="ql-syntax" spellcheck="false"> {<span class="hljs-attr">"codeId"</span>:<span class="hljs-string">"fghfghgkhk"</span>}\r\n</pre><p><strong>User story 4:</strong> If your code is submitted successfully you\'ll receive a codeId. This id is used for fetch the result after interval of time. When you get the output, clear the interval</p><p><strong>User story 5:</strong> You need to send a get request with codeId to fetch the result. In result of this request you get an object.</p><p><strong>PFB the details of API </strong></p><p>URL : <a href="https://codequotient.com/api/codeResult/:codeId" target="_blank">https://course.codequotient.com/api/codeResult/${codeId</a>}</p><p>Method : Get</p><p>If object\'s data field is empty it means result is not ready or removed from server.</p><pre class="ql-syntax" spellcheck="false">{<span class="hljs-attr">"data"</span>:{}}\r\n</pre><p>If result is ready you\'ll get output or errors in data\'s object.</p><pre class="ql-syntax" spellcheck="false">{<span class="hljs-string">"data"</span>{output: <span class="hljs-string">""</span>, langid: <span class="hljs-string">"0"</span>, code: <span class="hljs-string">""</span>, errors: <span class="hljs-string">"&nbsp;"</span>}}\r\n</pre><p><strong>User story 6:</strong> Show this in output console, and clear the interval.</p><p><strong>In case you get the response in output</strong></p><p><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAx8AAALrCAYAAACf0DoeAAAgAElEQVR4Aezdz4sUd+I//v5XPAjxpCdvWfYiOUQWMkHYJbzBsAfDHhKWN3524b0kH3QMRHLwkIODbzYgtCB42EnCsgTyiUIYc4gIJvhlCQNLVAYS2MVAcA7C60tVd1VXvbqqf8x0tdXVD0Gme7q7fjzq2TX17PrRveAfAQIECBAgQIAAAQIEliDQW8I4jIIAAQIECBAgQIAAAQJB+RACAgQIECBAgAABAgSWIqB8LIXZSAgQIECAAAECBAgQUD5kgAABAgQIECBAgACBpQgoH0thNhICBAgQIECAAAECBJQPGSBAgAABAgQIECBAYCkCysdSmI2EAAECBAgQIECAAAHlQwYIECBAgAABAgQIEFiKgPKxFGYjIUCAAAECBAgQIEBA+ZABAgQIECBAgAABAgSWIqB8LIXZSAgQIECAAAECBAgQUD5kgAABAgQIECBAgACBpQgoH0thNhICBAgQIECAAAECBJQPGSBAgAABAgQIECBAYCkCysdSmI2EAAECBAgQIECAAAHlQwYIECBAgAABAgQIEFiKgPKxFGYjIUCAAAECBAgQIEBA+ZABAgQIECBAgAABAgSWIqB8LIXZSAgQIECAAAECBAgQUD5kgAABAgQIECBAgACBpQgoH0thNhICBAgQIECAAAECBJQPGSBAgAABAgQIECBAYCkCysdSmI2EAAECBAgQIECAAAHlQwYIECBAgAABAgQIEFiKgPKxFGYjIUCAAAECBAgQIEBA+ZABAgQIECBAgAABAgSWIrAe5eP5fth/vhRPIyFAYJkCz/fDD199HN45+2o4fvRYOJL8P/lq+NPffxpNRfL+3x/dbcOtex8MpzWd5svh3gImav/x1+Ha+bPh1Mls2CfDqf/5PPy4gGGv2yD2f2kwMN9cHuR0mNdL37RId5b3U4smd90npYn1yLqbmv/lCMxVPn68da600jx9fXf6VO7dCm9mGwXJz9f64ftpr/rmcnip8Jo3bxU2JKa9Nnr8h7+9PdwoORneuPYwNPgnJRqzuwQINCrwy264/vbJ0jopLR9Hj4VsnbH/zZVw+niyMX4inP6fz8MPLfkQYrEbDfvh+362nsuKx/Dn728pH/OE8Jf74cPfnEgz9dJv/hL+8XieF8/43LaWjxneTzPOoactSWCx65ElTbTREAghzFU+wm4/nC6UgiPnPpv6h+3p389HGwfnws29yfbfX98ovGYjzNJxKof49PPwTnF6j54L2z4GrKTySwKrJbAfvnxvsJGYFY7k50svb6Sf/A/Kx8Pw0a+LG+Mnwof32zGXi9xo2L9zsfRhTepx/OVw+pWT4YjyMdcC/+7qqcLfnmPhpSsNBKaV5WOW99NclJ68BIFFrkeWMLlGQSAXmK98hPiP+cXw5cRdCckKrfjHf3D73TuTXvRT2D5XeM2vr4bv8smd88ZY+Tgf/vG0ehj7/3oYtvsXw1uvXF3IIRDVY/FbAisk8Hw//Lj7dbh55Xx4/Y/TP2hY6pz9qx9eL3yw8NK5fvhu7L0dr69OhY8eLnUqa0e2uI2GR+H6mcL68vi5cP3hz7XjfdEPtH09G5ePX11tIDBtLB8zvZ9edHqMPxZY3HokHrL7BJoVmLN8hHDvg+KnjVM+SXx+P1xKD3ko/HFMPp38YMKnSft3wrvFjYpJz53B5oe/XwyvJ8dAn/xdePfvjypf4Q1cyeKXay3wU7j5+8L7tmWfoJf3qJ4KH31bvbD2v/04vPnyiXDk+MvhzRYddrmwdU70Acuv2tKuKhbHwua5YtgL+9UvD8O1cy+Hl46eCCfPfRy++2VhQx4NqIXlY9b302gm3GqDwEq8p9oAZRpaJzB3+Uh28RcPc5h43sfDq+FXhSKRv27SeR/Ra975e/Of4nkDty6XJuiFC7S7fJTPP5t+KOcL54wmYGHrnOicuuxcl2h0rbi7sHluxdwcYiJaWD5W/f10iKWx0i/1nlrpxbfWEz93+QjRJ21H/vh5GDvaYUhaPHfjzT+eLxSR+o2FH26eLZSb+uctcql5Ay9S07C6IaB8NLkcF7bOUT6aXEzNDFv5aMZ1DYe6sPXIGtqZ5RcrMH/5CNFGydHLYafyCjLF550Il/7+Wenk7+rzPn4O//hj4VCPSXtIFujmDbxATIPqiEDx/XusdScur/ontQtb5ygfq/d+Uz5Wb5m1dIoXth5p6fyZrO4KHKB8hFDco3HkaM1JnKU9JMmJ6eWNmcrzPqJzRKpO9qt8sz3/Kex89Pbw+vYnwsmr2Tkl5XGWrvwS/QHIDwmLDxOrO9b9+c/h+y/64d0/DK6uM3j9yXDqtbfDu/2vww+TzqmfNU//3g1f9i+Hd84WxzG4os8b5z8OX+4e5pC0epsfvxrM18nh+TrJFYTeeq8fdiZcpWy+5RIB7A1P9n+t8F0NyTHXvzkb3rlyK9x7PCPm85/Dd59eKX3nQzrtH3w2PBm5fp6zKTrQfCTXxv/mVrj23tvh9G+S48WzAj3Iw6Vb98OPlQU9hFC38bh3P9z84O1wOjlfIRneyVfDW/l8ZFObvD76boeTr4YkG5OWVeHVYzfLG/XZfFT8rDsX6+lu2LmVLIONkOUnmf7jrwwydKjMHuA9W7k8i3Nd5/90N3x57Xx4PfM/ejKcOns+XPtqtst+x++hZPml79l/DbI8dbqK0zh2+364lGesYtnkj1XsOV7UOmX/Ubg3aTkP5zMcYJmVZjdZz351K3x4/uzovTB8P5z+w8Vw/Yvd8LTuvZUMqGb5Pn14K7z72+F79eT58I/sKojR9NZ+B0c6/x+n6//8PZpP1+Vw85sJOZl1HCWIyXd+/PazcD1Z/yRXOMuWf3LFs7Pnw4e37lf/PYqmI39d9vrsZ93fwEmTdJh14qThZo9F054vp+F6M/+um8TgD5fDdu1FGKL3Ut16LRlv3TizaYo/mM3cnv8U7t26HN7K/zacDKeqpinZjrl2PryRL8Pp65y69cjT3Tvl7/w5+eoUh3wmBjfSaY7W41mePn1Y/56rMfrxq6vhreF8vfSyi/pE2mt590DlI0TnZVQdZ1w6N2R4Sd5Saanaq1G6lO+JkK9QCotm7M32fDdc/23xJPhj4Ui+ApmwsRm9SeZa8f7rs/Cn4bXga193/Hfho29n3GguzN/g5qOw/ceNwkZs3QbGifD61YN+d0mFzfNH4R9/fnX0xyv745P/PBne+tuMJ+1PXC7DGX7+U/jy/dnmc+p3NDz+PPzplTqnY+FIujx2p55EPV++Qnj6zdXwRv6lbhPG/0rNdwZUbByNvpuiYngn3w7bw+8eGH2HTcXzjr4aLn0zf/4OXD6S4lf3XRN5fgbTefzsAU/kPcB7dmx5xu+1Kv9/9sObFRfKGLzXT4TTV+7Xf19QcsLy2cIGYDTvR44mX4D4KOwc6ksGow2msXFkeSiWj0WtU/bD97f+MvzulGw8FT+zdfABllm2iJ4+7Ie3ZnlvnTwbrtWtayuW79M7F8OpklnBKZre8b9BP4d7V8+OvtCyNJyyw6k/13yvzNRxZAIz/Ny7Ey5N+1uUTOPxjTR3pSFG01H7tyzbiC69uP7OodeJ9YMePRJNe7KcfhxbrsXlUfe3K3ovZbkdjWl0q2KcoweTWxV/UwvfGzPuW5imx59NzPqpD6rXOePrt/3w/fVzE7Ydpqy/QghP7388/W9a3d+zCqPv+7+LpmcxX6patndv1QQOVj6iK1JVnfdx78qoEOQnpZe+PLCwwh+q/fi34pcYVl/GN36zXb/+u/GN5XwFUrEyyJbQ/Y/D6dc20v/5pyTpH5Lk0+rB79PH428Ifnwr2jAZfDpx6crF8Fbpk/tkhX8u3DzQl1SVV4jJp/fZtJY+2Uqn99SBNjLHV5Qfh5sfFIrHyVcL35RcXIlXb9TOt1xCCM8fhZvnRhkZrZhPhlP5J0TF8R4LL527Vf0lcf/+PLxTtbF4/OVwKv/k+lg48utz4c3XCsOs+KM673yUN9bL2Sl+8p/OX8X44k9m33j/8ihfJ18u7T3IjX5/K3z/zeXRBtTxl8PJqo20X18O9+bsHz/+/S951krTn3zqVXxfXMv2LiZvqP1wr5idfIMs2XtV3JtVsH/lcrg375WECu/Z0qfN6V6ywnuk8J6Nl+fYN4nHG6cfXR1lKZnnyiy+Wn3J3kmZzj/NTAzOhTeLVxI7Ou8f4/vhWrYsoukrrSte+8voE/2wiHVK3XIe7JkrLZNsHVxYZvOsZ/eL+c7zNPoel/y9kD9WvV6K319v/rUf3h1bVxT+FlVsPGV/MgY/o78pySfKNcsimcaqD+amf4JeHmPtvbG/RcP3VzJNxfVebnQivHmr8OFRYdmUn5+8b6vfT7XTUnjg0OvEwrBqb8bL6erV0fowmf/S+23ocvx8+Me/4yGW3xejDy7j5x1gz8d/XQ6X8r9xJ8PJymVyLtz85/1wKf/g7EQ4+XLVhxfVf+fj9dvNvxWKR7L+qnI4GuWgMKtj77tkGH+4GD58/3x4I1rXHKlah8fL5Xr5kuiD9+2867vCBLrZGYGDlY8QnZtx/HK4V9r1vRuu5xt5hcOyotJSPu8j+k6Qqg21kFzqt7ABc/REeOn4sfDSby6H7ewQpP1H4cuvsm9ej/5QzDTMCW+M5w/DR/lKItkYrvhugb074d3Cc468/VntCfn1KUpWiMk3MvcrDzka+4Rnwkn/9eOIbI6fSD+dOPXnW+G74go68Yz3TrxyNXxXWt7zLpf9cO9Koegkh+WcvRp2osOrnn57K9qbcSK8+bf4cIbxL8cam4dkF/K1im+ArsjDfPkKIf1De/Js+Kjq8I/kG4PzPz5JbgvvhWzBRBu/6cr5lb+E7d1Ra0g+AS5/Ej/IffJp5qUvCh5x9o4eC+X3WDbSWX5G+aiwyoaSfGgwOtQseT/+Jdz8tnxI4P7jr8OH0R7KU5P2IGQDr/lZ3sApbDxGzy8vz4r3dpV/8l0ZxelP9tBdKOf1yHt3xvZ+fB99EPLSb6+UM50civLF5Yq9BhXTFc1H7d1o+is3dtMXH36d8sOt8nJO9iZ+GOc+PdymH/7012I5HUz91GWRzeTeZ+W8J5/a34oO9dh/FHauRJ+ovnJlvGxHPi8dTz7wOBneujY6FOnpt3fCvZkPuxq8L5L1VdUhhPvxXrOq76mKN9C+yWZ8jp/798OHxb8zR0+GNz6KDvdN9kbe+stoozwpIcfPhe2Kw2dnfT/NMoWHXifOMpLIcLDevBi+LP4NefowWv8eC/kHofk4Giwfael7Nfzp092wn/29rJimQSZPhNPv3xkdnjvjOqf8nhpuD42tdyr2SlcVsehDvFMXCtMz9Ir/Fo1d2jtaLum8JX+nPh0eHpmuA78O3+f+bqyrwAHLx3CjK/9EJfoW8uIKv1RMyqWlfN5H+QvBxlcSg0VUfrMln2ZfDju1n6DOtgFVHmb9hsDTT98e7WWZMN79r4rfOFy/YVQfukfhu/vljbfyc6OiNvcnp8nQIpujx8Kv3v96bINqMN54A398I7psOGW5jH2hVc0ejWTkv3wdLhW/pTr5Y56tyJPH5xjWDzeLe9aqT6Keaz6SXdQP71fvjRnAhVA6lLDik9DieyV9P/0uXM+6czaMEL/fkiJTVcRCCN+WL29ddd5UYbATbkb5qCsf+1+XP0mu+jQsG0uyZ6D0if/bYbtYdLPnzfBz1o2l8vKseG+P+dft1Sivn44cv1L+MtI5HMY+XTzQ+3eIFE1/ffk45Dolnr9fn88P/5thcaVPmbos0mfth50LxT2iNXs0hiON39NvfRqtNyOfdG/EzcKn//HERxtPVYddfXd/wutDfE5kxfp/6jjiiRq//0O/uMe//pPs5JX7dy8XrjR5LIxtMI6tXyqmeXwSan9z6HVi7ZALD0SGR6o2ppOn//uz8Fa+nXIsHBkeAj4aUrPl4/WqlXlFJl8am67k6IBonVNRZMvvqWPhyJl++L7493E0oyH+cCT+GoPvPjo12r75ff3f5FL2Stt343uHkg/cLt0dfZBWmBw311zgwOVjbKOq8Il06QuLok8ISxsNxfM+Sm/I8Y3bbDnFb7axPzbZE9Ofs21AlYdZsYFSMay6cpQ+tXSy/bEQv8lLk3jAO+VLEtddcWzSwCObo1M2BKON6DeiP+Blw2Nh0nIpZeDoRrj2z0nTGUJpZRftPZhrWNEFDUoXIBhOwjzzMXmqh48+/7p0cnDsFh8WUrun7PGt8Ebxj2hcwrKJicY38TCC7DWVP6N81JWP0qGUx8Kfvpj8h6ZczA/+3igv9/qNpfLyrHhvl9Y7x0LVIaQZz877xb2u58M/itcYn8shsl1K+cjmov7npHVKaZ1+9MSB9qhNXRbJpMXv0T+P72EqzUFciuK9wPHyrXvfZAONNmrHy0f2xAk/714ebcQdPTt+6O2hxxHl57WPazc4B1P5KFw/U8huxUbsrO+nCXM9+0PROmpsnTjLkCLD+g9ZHoWb/zVp3pssH3V/U6NpSv6m1XxJanmdM77+Kr+nToR3v5qw/o2OPDny/tcj6eh9N3GPeemc32hbLVoutX/PRmN2a00FDl4+QtTK85JR/lR+7JO4UnBHGw2lE9TjNl1YOOU3W8WKvfDcsU/3azagysMcf4Ong4zfuMUNwSm3xwxK0zjlzi8/hx++/TrsfNoPH14ZXPlq/DjOkeOUoRUejv6A/det8EPh0fGbk1fSZcNJy6WcjyMVfwjHxh0Vn2KZK62cpw4rmueKPMw+H9FUPt8PT/d2w72v7oSb166ED5Mrz7xWvuJTemhAdix89vJo46i+1Eb+xT8c2bDSn8VDHidvTJdeNnZnulXyktJFJI5GG+Rjw0w+hr0T3i28X+o3GqpePPrdrBtL5eVZ8d6O/CdtCE0a57wOU6drNKuTb0XTP9O65gDrlNL7bML6edLEzjTPE97r1cOesj6JfEobXFUDjDaeJpaP/Z/Dj7v3w84Xt8K1K1cGV76Kj4s/emz8winzjKNqGg/wHpqWz0nZrpqEmX530HXiLAOPDe/Wv2hy7qL1arx+Lg42HufY4XLROrP44WpxOGOHj9d/eDhtuZXnrfo82dGoo+kr/v2L3nfp36rCenrS/dJ7JDKatD4dTZdb6yhwiPKRHOdf2D2eHYZQatDR4ViJcOnx0THpxRPUq46nzhZO+c1WsTGRPTH9OeHNVnjeTMOM/4jN+MZMd/PfKhyXXxjvpJvppSDPVJ14VvgUJ5+GBZSP4oqocsImr6RnMkyHO9syKU1CZD/awJp3WNOfP/t8DKcwvczzueoTw/PlU1hm8R+32nkrCYQQnTBcv0dj+jzGQ66+P9tw5vaaeT6qpyr77awbS1Onb2b/+NC38ntu6niyCR/+nPf50ctHd+eY/oOvU2bLwmiiqm/NNM/Rxktpw6Z6sNF5gNHfhDl80sHPMv69r8NH54qX1C68vyve82PzMMs4auY1/fW88zTDYVWzvp8mTVb+2GHXifmAJtyYw3By7ib/XStNwdRxzv4+mTxNo7FOWy6zDmcwxAnTF83bpLIRP1bKdzSc0mOj2XKLQDhU+SjtrTg6LBrFPRuVn0SXP6kanPdR/rR2/KTi0ZJa2JttNMjJf7yy50Ur/F+duxg+vHJlpv/Xv4mOQ86GWfPzx8+jkwSPDq7ycjr5DpFknP3Pwvb7G4Vd++UNoZrBRr+esCKKnjm4O3klPftymXe89dfqn3XP1mh2po979vlIPsV/GK5FJ1EnJ7MmV0t74/zl8OGVj8PNLz4uH3OsfJQOQ6svUaOlVnVr2h/l7DVTl2f0vh4V22wIo5+Txjl1PKPBpLfmfX708tHdGaf/cOuU6e+b0QTV35ppng+w8TJxuDP65FM9Zfz7Dz8Or8dXy0qveHU2vPP+lfDhtVvhy78Wzg1sYs/HvPO0zPKxiHVivjAm3JiynIqvnJiPeT4MmTrO2d8nk6dpNPWT1jnJs2YdzmCIE6YvmrfXz8+2bZNsA/3jX6PpXdiV3AqDdLObAocqH6Hi3IbibsLyCeUjwNLxw8muydJwKvaWjF66uDfbvMMsTeOx0NjuxOgY5rErNw2ne9pKqTB7NTcnrIiqXhHNf3x40OwrwfJFB45M2DWdT0a0S3h0PGo0D1MPHSuX3OnnfESfouYTNLhROhclufLPV49GVzXJnzu5tMXnfNRv/E4ZTj6+yGTqHq38hdGN2Ybz3dXCSYpHp+32Hz/sKs5RNBG1d2fN/9RczrEhN2mc8Xh2ak76zGaodBhT0+d8HHqd8lPYPlf4dH+W92w2o4WfsdHYZY+T5xY/vJrpam3lD7PG1idzLN90UqONsPInt+VzJ8auZpbN68RhjJ+UWx5HNpAJP6esi6teWfy7fKTifTop21XDq/vdQtaJdQMv/n6aceG5k3M363p1luU22zozmbTJ0zSa+GnLZdbhDIZYd6j8+Pvu0oTD2EZTV3FrjuVS8Wq/WiOBw5WP+IpJH3xW+iM12kiMREsnz54LN28VTtCr3Fsyev18b7bZVgazDTN64x7oErqj+ai9VTpZsf4Y+vJG3wL2fEw5jru8l2t0uFw2H7MZDp5d/kP4u3C9+MlJNsDCz0kr4PJ4607wGw4svvJJxYZ5eXiTykf5pMHacxeiDYWxT/pn3jia9Y/kbJkv8NbcnG045VxMOeExGVPpxOzqLxKtmaDSrydlovjEqctzZv/Jh12Vv6Oo/gTSdNriq9g0XT4WsE4pr2+qr8hWdK+6PXVZJC+Kzmd46ULdFfiGY4gO4x37wGuO5ZsOcdLGU+nvVnSibWGGSx+uNbHnI0QfopzpTzlfb/p7edb3U2E2K24uaJ1YMeSxX01aTtGTJ+euPM1HJnyAVf67VXEuT7w9VPH3JZu0ydOUPWvyOid5Vnk4kz+4Df/8OJwuHBZY+uAn+jtVdUW00VRNuDXHcpkwFA+tgcAhy0d0wunvz4U383BP2iAub8i/+fvRJVDH/nhEC6H8Zpu0cZi8cPpKN3lWeZj1n96WLkXX0CXkyn8Eak5Eiy8/e3SSdQSY341sJnzxUEi+rbx4tZTjF8NOdFGNsuGU5fLPj0vXnq/98sBkWuN5jf7Qxn/oJ10ueOf94qf0s1xqd9J8lMtA3Z6w+FKgK1s+6j7tfvp5+FPxMJR5LrVbkaM8nlNulN8n9fmfmss5Nk4njjPaOzcp02OZaLh8lKf7gOuU6BLOR37fn3yJ6YrlV14WdevZn8M//lw4l/DoPJfarSi/cyzfdJInbTyVHqu5qMbY5aQrNlJLw6l4vMIu/tX314rfOzPfpXZf749fKrickfr3Uzwd5fsLWieWB1p9bw7Dcu7idXq0J77ub2n8d6iqVM64vZHM0ORpGs3ytOVSHs6Ey+WPfQFq/KHfz2H77cLezcThIF+QPMdyGc2lW+socOjyEe8mz09GqttYGSqXTlbPC8v4J+rxQim/2eIVSfzsaAO75pOI8icaJ8Kb0WVk86FG3ylx5Oir419+NXzy/o+74cuPzoWPxr9rKx9c1Y34k+Q3r++Wv3vj3/fDR2PnGRzkj0Vkky6Divn598Nw/e3yie+nrj4cm/T5lktyqERxA2PClwz+pvi8V8OH96PWEx1Sknz/RfJlTT8Un/bvh+Hmn4t/rIcr2Yo8zD4f0aePr1wMX2ZfVJboPN8P3386fu7O6pSP6HCWZCPwbvW5S/H14+u+ZPCjs8Uc1XxPyViyqn8x7Y9y9qqpy3OOjdPJ4ywfkpOsB8cOmaz7ssuGy8di1inJRlrxvZh9meRP5UMNky8R++rj8E7FlwzOvJ7djb4Vue5LBj86G44X/nZUflfCHMs3zcykjaeoYI59Cdv+btiuWM+MHVY1aRxZcKf9/OVO+ft1JnzJ4OnShwMVX8Q4wzkh0yZn8PiC1omzjGwOw2nrgPL7+lh46bdXw73i9w/tjX9BavL+HluuLSgfs/79eym+JHWy07H0/WTJF1LWHUocwv6/7ofr710c/8LKOZbLLIvZc7orcPjyEV2zOysftYehDC3LfxCzxn0ubBc34Crcp61Iyi+JNrArNjbT55cOBRlOy/GXw+nXXg3Ho9eMfctv+sdvcJJxcmnV+PKq4yuo8hSO3Yu+ZTTxfOnlwXBPZ5dxPH4uXHr/7GJPOH/tXHgz+8bcdN43wvglfY+FI1XfIjzHJzn5/P5yP1zKxlfYgDiSuRd/l96u/3Qv2ftR/IbtLIOJ26mTWbaOhVMf3ArXil9yFy3bZNrmydd3V+NCM8pBNt5TH1wOfyrOy8qccB7v8h86JifXJjkszsfYp2qZ+cDjZHHjZ2hx6oP75VKdB2O2G+UNhvryPXV5zrFxOnWcD6+W9uhlOTySmL0yKl7JXpGbpe8MmfYhygSTWaZ/UeuUuvfs0WPh+Csb4fTLhXJSzEc2+Ydez1aMJ3tv1e1xm8Unm77k56SNp+RwuXidlZ5snqyfXx0WoVfDpQ/+Ulg3V2ykThpHcVqm3B7/ssrB+y79e1HIW57D4/WfZk/N9pRpyR5eyDoxG9ikn3MYTl0HVLw/8r+7eaZPhHffm7JcX3j5OBfePDd6D6Y5yKc/WyfXf8t9CPvh3gfx37Tk+cm20HAbJM95MryK9e4cy2XS4vVY9wUOXz5CvNsyCeUMx3JHxximK8gpe0uSxTF1RVJaZjOWj9qNp+pDc36o+kQ7+yNY+nly7j0fyeT/8PeKT8yz4R7fCB9+sx8O/8di3OaHx5+Ftwob6/kfreG4X/rNlbBT/GK1gvV8y2X4wl92x/aqxONM7yeffH46fqhAYfThh1tvlz4FjYdz/O3Pwg/Px+c57rpzzccvDyv2Qo1W8oNxlg9FWJ09H8khbzUFMclDvHH5/Kfw5fsblSWwvCxOhjeu3Q9Pp5yQXVy2Vbdnzf/U5TnHxuks43x690oofdKcvW8L76F7v8y7HqsSGP5uxulf2Drl6f1wrbQHa5T30nKO85FM7mAtSPgAACAASURBVJzr2R/vXJ5omY3v+NmPw72a9dLsF3QYek7ZeNr/9ur41a7yZXwyvPW3R5MLTDKaKeMYTslMP/b/2Z+4zs6Mkr2R2xPOrZsl2zNN0CLWibOMaA7DqeuA5FP/by5Xf3CQLtsT4fWrD9PnZJ7Jz/EPFqf/fclmbZZpSp47bbmMDeeX++HD0tEC0fvz5Nvh5oQchOc/h3tXfzfDejwpJW+Pf1g8x3LJLPxcT4EFlI/4DZKEvf5E6RFztIv26LEwbW9J8tqxN9togBW3Zl8ZhOTa5NfOhzcKnxglnxy8dfV+qPy79nQ3fNm/GN4qfRIw/GTuDxfDtVv3y4f+VEzdpF893b0Trp0/O/rk/uSr4Y3zH4edvcGrpq2UJg178FiNzdOHYfuDt0efYiafepw9H659sTtxg3G+5VKeumRerydfylewTz9tOXs+fDiP497Xg+Fkn/Yk0/6Hi+H6V9n3rEQnF1Z8Ud/c85EcSnPrcngr2yt19EQ4+Zu3w6VPHw69Vrh8JIvpl91yHtJPuc+Gd2rK4P7j++HmlfPhjdwjWR8ke0DeDu/274Tvi4czlGMw171Z8z91ec648Z5M3KzjDNl7KM9znIl512MTaOaY/kWuUyrfs8legOT99unD8GNduZx3Pbv/KNy7dSW8c7b8hZ3Jnpa33uuHL3erDwXMxebwSV8zy8bT3v1wM15H/uFy2H44nJZpw5j2eD7xM954/nP4/ot++iWH2R7XZOM4+fv1xvkr4eY3VVfhKw975myXX1Z977DrxOqhln87h+HUdUA25L2v07+5+R689O/HHMv1he/5GO5BHXuPDda/l27dr39fZgbZzyTjY+vxZD02uIz89brtgTmWSzYqP9dTYCHlYz3pVn2ua8rHqs/WpOmPr6RzZc4TciYN22MECBAgQIAAAQJTBZSPqURdfcL6lY/kPKPiuSG1l4Lu6iI3XwQIECBAgACBFyygfLzgBfDiRr9m5ePp19EJ7rMcGvjilo4xEyBAgAABAgS6KKB8dHGpzjRP3SkfP356Mbxz5Va496+fy5f9TBz2fwrff3E1vBGdSF91ueCZ2DyJAAECBAgQIEDgwALKx4HpVv2FHSoft0ZfUpmdaJlcGrB48mXxKiWTvgBu1Zeq6SdAgAABAgQItFlA+Wjz0ml02rpbPopFo3z7RDj9P8kldxuFNXACBAgQIECAAIEaAeWjBqb7v+5O+UiuTf79V7fCteRyvdGlj9MveDt7Plxa4GVeu58Nc0iAAAECBAgQaEZA+WjG1VAJECBAgAABAgQIEIgElI8IxF0CBAgQIECAAAECBJoRUD6acTVUAgQIECBAgAABAgQiAeUjAnGXAAECBAgQIECAAIFmBJSPZlwNlQABAgQIECBAgACBSED5iEDcJUCAAAECBAgQIECgGQHloxlXQyVAgAABAgQIECBAIBJQPiIQdwkQIECAAAECBAgQaEZA+WjG1VAJECBAgAABAgQIEIgElI8IxF0CBAgQIECAAAECBJoRUD6acTVUAgQIECBAgAABAgQiAeUjAnGXAAECBAgQIECAAIFmBJSPZlwNlQABAgQIECBAgACBSED5iEDcJUCAAAECBAgQIECgGQHloxlXQyVAgAABAgQIECBAIBJQPiIQdwkQIECAAAECBAgQaEZA+WjG1VAJECBAgAABAgQIEIgElI8IxF0CBAgQIECAAAECBJoRUD6acTVUAgQIECBAgAABAgQiAeUjAnGXAAECBAgQIECAAIFmBJSPZlwNlQABAgQIECBAgACBSED5iEDcJUCAAAECBAgQIECgGQHloxlXQyVAgAABAgQIECBAIBJQPiIQdwkQIECAAAECBAgQaEZA+WjG1VAJECBAgAABAgQIEIgElI8IxF0CBAgQIECAAAECBJoRUD6acTVUAgQIECBAgAABAgQiAeUjAnGXAAECBAgQIECAAIFmBJSPZlwNlQABAgQIECBAgACBSED5iEDcJUCAAAECBAgQIECgGQHloxlXQyVAgAABAgQIECBAIBJQPiIQdwkQIECAAAECBGYT+D//5/8E/9tlMNuSe3HPUj5enL0xEyBAgAABAgRWViApHf61T6Dty0X5aF9mTBEBAgQIECBAoPUCbd/IbT1gQxPY9uWifDS04A2WAAECBAgQINBlgbZv5HbZftK8tX25KB+Tlp7HCBAgQIAAAQIEKgXavpFbOdFr8Mu2LxflYw1CaBYJECBAgAABAosWaPtG7qLnd1WG1/blonysSpJMJwECBAgQIECgRQJt38htEdVSJ6Xty0X5WGocjIwAAQIECBAg0A2Btm/kdkN5/rlo+3JRPuZfpl5BgAABAgQIEFh7gbZv5K7rAmr7clE+1jWZc873vQ+OhSNHj4U3b/005ys9nQABAgQIEOiiQNs3crtoPss8tX25zFU+sg3QZCN04v8P7s9is/znfHs1/Cqd9lPh0jf7FeP/Kdz8/WDeXr++W/F4e361//h+uPnB2+H0KyfzZfHSyxvhjfNXwpd7i5/ObNkrH4u3NUQCBAgQILCKAm3fyF1F00VMc9uXy3qVj/Bz2H57WJze/iw8jZdwVk6OXwxf/hI/2JL7z38O967+Lrw0oQBe+mbx06p8LN7UEAkQIECAwCoLtH0jd5VtDzPtbV8uc5WPGCLbID3S1j0d8QQn97OCcfR34fq/ik8YFZP27vXYD/c+eHW4p+NEOP0//bCz+1PYfz6Yj/293bDT/0u41sCOp2xZ2/NRzIzbBAgQIEBgfQXasZG7F/pnemHjRgOHfazoom3HcqnHW7/yUdj78asP7of84Kvdfng92Ztw/GLYyX9ZD/ciHtm/c3G4x+NEePPmo6VOgvKxVG4jI0CAAAECrRdox0au8hEHpR3LJZ6q0f0Gysd+2LlwYvDp/Ht3Rhv32TgfDs+7+PXV8F3yif3erfBmegjRuXBzbz98/+nl8ObLg9e/9PLvwru3Hoanw0/2s0EkP3+83w/vnn01HE9feyKc/O3FcPPhz8Wn1N/O9368Hbb/nTxtNM2v98sb9U8ffhYu/WEjnDw+PFzr5KvhjfMfh52oYNdvnN8Pl/L5G01S9vxkr9H+P/vhnXSez4XtH0fPKd8anY/y0h8/Hz9krPzk0b3nP4fvPr0c3vrNy/mhWsdfORveufZ1+LHCNfyyG7Y/eDucOjmY3+NnLoab/0z2uAzuj+35eP5TuNe/GN7Izj05/nJ4/b1b4buxY9pGk+QWAQIECBAgsPoCy9/I3QmbvV7YvFu063b5eO+998J//vOf4gynt5Pf/d//+3/Hfp/8YvnLpXIyan/ZQPkIYf+r7BP6i+HLaC/Cdx+dSovJrz56OJiovHycDZc+OBdeOnoynHptI5weFpDkxPZTVwp7KEIIP9xKnpfspXg5vH7+cnj3D1kJebXmRPJ4/guHWCVl41/DvR6/vhzuFaY3H8/RYyE5mfv0a4UScvx34drD0ZNrN87DlPLx54vh3azYHE0KWDytw/tPPw/vpCXmWLhUetPVPD/59fNH4ea5YRE8eiKc/M1GOF0oIS/99uPw3WgWQvjlfrj0yrBklZbDufDm8ET8UvkoDD8piu+8fzG8lZWQVy6He209b2YCmYcIECBAgACB2QSWv5G7XuUjKR69Xi+cOHGiVECS4pH8LnkseU78b/nLJZ6CyfcbKR/h+cPw0a8HG7Hv3ils3ea/L5xvkZePY+Gl314N9wqfmP9wc1gyjmZ7KEIID6+GU8lG+K8vhi8Lewn2714eXMkq26Myeb5H5378+kq4dmVQiEob1tl4jr4a3v2icHnZ5z+FLy8Mz7s40w8/DMdz4PKRlKsPvq7cu1OahWyP0aSCUnpBCN9dHU7nKxfLV8DauxPeHZaM4p6erBgeeeVy2Ckshx/vXByYR5fazYb/q/fuFPai7Ied96OCGU2XuwQIECBAgMDqC8yzkbtzoRd6F3ZCuLuZbjQnG8693kbIDzh51A8bxfsZT/r8zbBTel3y2mwPyGjPRzqOdLi90DvTD/HnuXs3NgrjHk5PNp4QQjaN5edthp3Cc5Z5MykZx48fT6c5KyDF4pE8ltyP/82zXOLXLuN+M+Uj2TvR/934oVfZ4U7FK03l5eNU+OjbeJZ3w/XXBiXmnb8PDqm698Hgk/xSUUhflu1h2AizXSV3tPcjvWxwVFqy8bx04evxQ8f+/Vl4K90LMRrXgcvHax+H76sOf4opvrk88Jy1fDy/Hy6le1ROhHe/KhTA4XCffvr2YHiv9cP3Q78Pa5+/H758LzrsKh9+xd6abFrzYccz4z4BAgQIECCw6gLzbOTmxSApIMN/g438rICMSkT2ePIzKwSD39Xv+RiVkfRV6eFZxZPQB+MvFonB+IolJZvG0euGzylMc3HalnE7LiDZHo+64pFM0zzLZRnzEI+jsfIR8g300aFX2Qb9W58Wzs3Iy8flsFOxEb7zfnGjd3Tew6TvGZn5UrNZGTp6LJSmKYzGk5WeMtyjcPO/BtOVjevA5WPWK4U9vhXeSAvPqZAdsVaepuhe7no+/KOwFyN/Vj68y+Fe8sspzx+bv/z5A4fq5TEcdj5SNwgQIECAAIGuCMyzkZtu2I/tjYgKR7J3o/ScpGxk5SRRm1A+ooKQFptsWHV7VaLfV05jtuflBS60YgFJStak4pFM5jzL5UXMVnPlIz+Je/jJe/ZJeXw1qXwjtmpD9aewfa66fJz+45Xw4ZXq//8oXUJ3Emu2tyT+9H5UPkqHjeWDegHlI/M7eizk58vk01NxI3cdlb/Ss2rLR/Xz68vHRnjnw+rl8OGVz/PD0krjdocAAQIECBBYeYF5NnLTDfuoICQA5d9HZaOyjGSHW2V8UYEZ/rpUPsaGU35tdgJ7eVqGz2lB+UimJCsg04pH8tx5lksmscyfDZaP0YnnyaFLT4cnoY9tOOcbyWfDzcfRrO9/PTwZ+0T4cPjdFdmekNOzHVsVDTC+W1c+QsjGMza9ySB+uRP+VHPY1dh3nuQb+eWCk23Mjz0/nsTC/ewciyNHZzix/vnXw6tsVR3OFsL+F38pH3a1fye8m+1ZGTv87VG4fqZYApOT2bPhjw49K0yqmwQIECBAgEDHBebZyK3csB8rH4Mykh32lLwmKwYDyvo9H9lrMvKulY9kvpICkvyf9m+e5TJtWE083mj5yE88P34xvPtecq5GxSFDefk4Fn713uejE5ef/xx2si/UK5yPkV9J6/i5cD2+tO7enXDpr/N8w159+dj/ZngCe7Khf7dwmFjxhPPf3wrZOe8/3jo32JhPvh09O8wpee57g5Ovj0TnahykfMRXo3rjozvh+73R+RzJlwx+ee388EsGk8vjDscdnUAeCiecj86dGZ0D89K5fvi+cKWq0Yn/x8Lo+aPLEyfPjy+t++MXlxv5ssMm3gSGSYAAAQIECMwvMM9GbnX5qCgTyaFQ6eFSyWPFczSS6at4fphhz0d0eFU+p9HvK6exJXs+8mme4cY8y2WGwS38Kc2Wj+KJ58mn6oWrQ+VzkpePU+HUKyfCkZOvppe0HV1qN/6U/+fw5XvZt3wfC8dfGVwC93R2iddZz6FIJ6C+fCTf/XHvykb+3Rhjl9p95S/hH8U9Nf/+PLyTXTL3+MvpPCTflfGr9y8P95Icfs9HOsk/3gmXfpNdPrf6fIvsPJSkrHyYP3f8Urun/vx5+KF4ns3Dj8PpaB7S5fDKlfBhfMJ5MjH/Hl0160h2ad7XNvLvCMmnI1/YbhAgQIAAAQJdEZhnIzfdsO+Vv4l88Lu4YCRlYiP0b2wOro5VwqoqGlW/C6G05yPbw1IqM4PXpVfgGo5D+ShhN3an8fIxOvE8Pql7OE95+TgXbv7rUfjyg3PDL/Sb8MWBtV+cdyd8n+11mIlsUvkYDODHrz4O7+RfZph838fvwjvXqsez/89b4d3fDr/M7/jL4c2Pki/yqx7HgfZ8ZPOUzv+V0nQNNv7fDu/2oy8PfP5T2Ll2fvQlgMn3ffz2fLj2xW7l5X33//lZuHRuNA/ZFwZm0zva8zGcmKcP0y8lHJXFk+HU2frhZ7PgJwECBAgQILDaAnOXjws7g3M8ssvhlsrAyCItDqUTzUePFS/VOzgka7bykQwhK0CDy/yWi1D+eHxeij0fBfzF3DxU+ZhpEvKrXtVcdalYPuILMs80Ak8iQIAAAQIECBBYtsBByscs0xjvtZjlNZ4zEphnuYxetbxbjZeP7FyIyu/LSOZT+Vje0jYmAgQIECBAgMCCBObZyK08pKlyOqr3ZFQ+1S8rBeZZLpUDaPiXzZaPH++Ed9NvOv9d/Rf/KR8NL2KDJ0CAAAECBAgsXmCejdyZy8cKHua0eNnDDXGe5XK4MR3s1Y2Ujx9vvR1OvfZqOJ5euvVEOH3tYf3UKR/1Nh4hQIAAAQIECLRUYJ6N3OnlY3Alq17duR4tNWjjZM2zXF7E9DdTPv42uOxscnL2u7d2w+hisBWzqHxUoPgVAQIECBAgQKDdAm3fyG23XnNT1/bl0kj5aI7TkAkQIECAAAECBNog0PaN3DYYvYhpaPtyUT5eRCqMkwABAgQIECCw4gJt38hdcd4DT37bl4vyceBF64UECBAgQIAAgfUVaPtG7roumbYvF+VjXZNpvgkQIECAAAEChxBo+0buIWZtpV/a9uWifKx0vEw8AQIECBAgQODFCLR9I/fFqLz4sbZ9uSgfLz4jpoAAAQIECBAgsJICyYau/+0yaHuQlI+2LyHTR4AAAQIECBAgQKAjAspHRxak2SBAgAABAgQIECDQdgHlo+1LyPQRIECAAAECBAgQ6IiA8tGRBWk2CBAgQIAAAQIECLRdQPlo+xIyfQQIECBAgAABAgQ6IqB8dGRBmg0CBAgQIECAAAECbRdQPtq+hEwfAQIECBAgQIAAgY4IKB8dWZBmgwABAgQIECBAgEDbBZSPti8h00eAAAECBAgQIECgIwLKR0cWpNkgQIAAAQIECBAg0HYB5aPtS8j0ESBAgAABAgQIEOiIgPLRkQVpNggQIECAAAECBAi0XUD5aPsSMn0ECBAgQIAAAQIEOiKgfHRkQZoNAgQIECBAgAABAm0XUD7avoRMHwECBAgQIECAAIGOCCgfHVmQZoMAAQIECBAgQIBA2wWUj7YvIdNHgAABAgQIECBAoCMCykdHFqTZIECAAAECBAgQINB2AeWj7UvI9BEgQIAAAQIECBDoiIDy0ZEFaTYIECBAgAABAgQItF1A+Wj7EjJ9BAgQIECAAAECBDoioHx0ZEGaDQIECBAgQIAAAQJtF1A+2r6ETB8BAgQIECBAgACBjggoHx1ZkGaDAAECBAgQIECAQNsFlI+2LyHTR4AAAQIECBAgQKAjAnOVj2+//Tb4z0AGZEAGZEAGZEAGZEAGupmBpjvO3OXj2bNnYdn/d3d3m3YwfAIECBAgQIAAAQJrLZAUyqb/KR9NCxs+AQIECBAgQIAAgRUQUD6Ge1ns+ViBtJpEAgQIECBAgACBlRZQPpSPlQ6wiSdAgAABAgQIEFgdAeVD+VidtJpSAgQIECBAgACBlRZQPpSPlQ6wiSdAgAABAgQIEFgdAeVD+VidtJpSAgQIECBAgACBlRZQPpSPlQ6wiSdAgAABAgQIEFgdAeVD+VidtJpSAgQIECBAgACBlRZQPpSPlQ6wiSdAgAABAgQIEFgdgTUpHw/C9tZW2NraCtsPqr89vTXf83F3M/R6vdL/zburEyhTSoAAAQIECBAgQKBOoPvl4/Ht0N/qh9uPBwVkJcrHmX7Yq1tifk+AAAECBAgQIEBgRQW6Xz6Gh1U9e3aQ8rETNnuboX9jI98TsXGj4VqQ7PlQPlb07WSyCRAgQIAAAQIEJgkoHxPP+UjKR29UBh71w0ZvM+xMEk0f2wv9M+VDp9JDqS5Mf2WID7tSRKZqewIBAgQIECBAgMBqCCgfU8vHRug/yhZmUkaK97PfN/VzWGJmKS1NTYLhEiBAgAABAgQIEFiQgPLRSPk4xJ6PaMHuJYd8KR+RirsECBAgQIAAAQKrKKB8NFI+FhWFwWFfrna1KE/DIUCAAAECBAgQeJECa1A+RpfZTS61O/ifXP2qfMnd6kvtxodZxfcbWHTROR+KRwPGBkmAAAECBAgQIPBCBNagfJRLxrPhno74Z3X5eCHLxEgJECBAgAABAgQIdFJA+Zh42FUnl7mZIkCAAAECBAgQIPBCBJQP5eOFBM9ICRAgQIAAAQIE1k9A+VA+1i/15pgAAQIECBAgQOCFCCgfyscLCZ6REiBAgAABAgQIrJ+A8qF8rF/qzTEBAgQIECBAgMALEVA+lI8XEjwjJUCAAAECBAgQWD8B5UP5WL/Um2MCBAgQIECAAIEXIqB8KB8vJHhGSoAAAQIECBAgsH4CyofysX6pN8cECBAgQIAAAQIvRED5mKV8POqHjV4vbNzYq15IdzdDr9cb/t8MO9XP8lsCBAgQIECAAAECay2wHuXjwXbY2toa/t8OD4aF41nh5+7ubn0QJpaPnbDZ2wj9R/Uvb8cjyXRmBakXNu+2Y6pMBQECBAgQIECAwPoIrEH5eBC2t0aF48EnW2HrkwehWDyS2xPLx6Q8pMXkxezt2LkwYW9MaZr3Qv9MsXCsSmEqzYQ7BAgQIECAAAECKy6wBuXjWbloJHtB+rfDk8Jej0nlI9nAzw6pKu0tGO4NyR7LftYemlUIyt6NjbBxo1/YEzFveRmUid6FGQ/wSg4LO9MP+UFjw8PEytM63DMy6zAL8+MmAQIECBAgQIAAgVkE1q58JHs++neelAvJ1D0f8Z6DAu0B9nwk5SMpK1mZmX0PRjLeQUkoF4fC9FTcTMc3LBXp7TP9sJNMQ6loKB8VdH5FgAABAgQIECCwQIG1Kh9P7vQr93pM2vMxsG6gfBQ2/IvlYOKyHe6xyErLxOcWHsyGn+7FKZaQwjQUnu4mAQIECBAgQIAAgUYE1qZ8TCoeK1M+0gjMv+cjVBxmNd/elkayZ6AECBAgQIAAAQJrJrAW5SM9ybziPI+kdGT/J59w3pI9H3k45zznIz1Uq3BFrvRQscL9dLgOu8p53SBAgAABAgQIEGhEoPvl4/Ht0M8vs5tdbncrbD8YFY/6PR/DjfzCJWqL52qkS+Sg53wUDnnKDouadwnPtfeidIJ8XDySMSsf8/p7PgECBAgQIECAwHwC3S8fhb0b2V6Oqp+T93zMh+rZBAgQIECAAAECBAiMCygfw3KifIyHw28IECBAgAABAgQILFJA+VA+FpknwyJAgAABAgQIECBQK6B8KB+14fAAAQIECBAgQIAAgUUKKB/KxyLzZFgECBAgQIAAAQIEagWUD+WjNhweIECAAAECBAgQILBIAeVD+VhkngyLAAECBAgQIECAQK2A8qF81IbDAwQIECBAgAABAgQWKaB8KB+LzJNhESBAgAABAgQIEKgVUD6Uj9pweIAAAQIECBAgQIDAIgWUj5aWj50LvdDrbYadRS5twyJAgAABAgQIECDwAgXWoHw8Cbf7W2FrK/vfD7cfPwvPhqUj+9m2bzg/WPnYC/0zvbB5d/5EDcaXFJ5e6F1QeeYX9AoCBAgQIECAAIFpAmtQPspF48mdftj65EHry8e0BVf9+MHKx96NjVLhSIrIxo296lH4LQECBAgQIECAAIEDCqxd+XjwyVbo33nSePlINuA37+6EzWRPQvq/cAjVo37YONMPO8lG//DxfGM/eSx7zZl+KFWA4ev66SFZg+FmeznSApG9Lv+5EfqPpiUjmcbi84bTHI972mA8ToAAAQIECBAgQGCKwHqUj8e3Qz877Kp/OzyJDrlKDr1a9GFXg8OYRhv1pb0Jw4JRLhyFcpIstLuboRcXgKyYZIdFjT3nAHs+0mEOx53e3gj9u0kBiqZnSpA8TIAAAQIECBAgQGCawHqUj0LZSA+7qiggTZSPvFwkSyEpCllpKG7w1y2hsWIRQohfF98PhygfyfiywjE23LqJ9HsCBAgQIECAAAECswusXfl49uxB2N7aDg8KhaSpPR8rUT5CxWFWVcVn9kx5JgECBAgQIECAAIFKgbUrHy9mz0e0R2KWPQtVBSB+XXw/hFA6vKtykY//svyawbSWitP4S/yGAAECBAgQIECAwNwC3S8fxfM9kvM+Kq501dSej+xk8uRnaWO+ojTkSy49/Ck7SX34Mzv3I35dfD8ZSPq77PWjc07y4VfeGBSObHpL01r5fL8kQIAAAQIECBAgML9A98tHdHhV9r0e8c/Gz/mYf9l4BQECBAgQIECAAIFOCSgfw3KifHQq12aGAAECBAgQIECghQLKh/LRwliaJAIECBAgQIAAgS4KKB8NlY8uhsU8ESBAgAABAgQIEDiMgPKhfBwmP15LgAABAgQIECBAYGYB5UP5mDksnkiAAAECBAgQIEDgMALKh/JxmPx4LQECBAgQIECAAIGZBZQP5WPmsHgiAQIECBAgQIAAgcMIKB/Kx2Hy47UECBAgQIAAAQIEZhZQPpSPmcPiiQQIECBAgAABAgQOI6B8HKZ83N0MvV5v8P/CTsVy2Av9M73QO9MPexWPhkf9sJG9vrcR+o+qnuR3BAgQIECAAAECBLohsFbl48EnW2Frqx9uP34Wng1LR/bzMN9wvndjI/TmLh+DYrJ5d7WDlM57VqDqStZqz+Ihpn5YPnu9sHGjsn4eYtheSoAAAQIECBBYPYG1KR9P7vTD1ifbYXup5WNSIHbC5qrv7Uj2/BQKR30Jm+TQ1ceS5dsLm3cHBUT56OpyNl8ECBAgQIDAPALrUT4e3w79re3w4NmDA5SPwUZkdnhV1UZk5UZ37SFZ5eFlw63ecxItyuQwrTP90L8wPNQr3biNnrO0u/Gem+xT/s1QdQDa2GQVfaL52LmQbLQXnWYbZvq6G4VD2QrFaGz8S/vFhPIxNFj1vV9LozQi8Ozb7AAAIABJREFUAgQIECBAYOUF1qB8PAm3+1th+0FyqNW85SPZcCyeixHfHyz/yvIxjEb9Y8nGdXHYM2QpO0ckO8Qr2vNQO4TsddnhUcOfg43erDSMCk1aiLJx1A60OP2DojAoDDPMU7rRXSgU6fSNXpeUiF5v9Hhyv6r0xZM2eF02nMF8zbJhP3hdNP9ZcYlK0qAsZuOIp6Dq/mA6Kqdf+agC8zsCBAgQIECgwwKdLx/J4Vb9O0+G53jMWT4mbrSPUlFfMEKof6y48T4a1sRb6fSMNsoHJ6wX7k988aIfHE7/3WRPQ7YxPts8JSbljfFyUZi1bMRzFL8uvh8/fzn3J5SP5UyAsRAgQIAAAQIEWiPQ8fKRlI3kJPOK/588KJ10XnnCebKxn30CPmGR1ReMlpSPiSVqsHGcH/6V7R2Zuucje12h/MTlqMasbeXjhe35qPHxawIECBAgQIBAVwU6Xj7iq1rNuecjDA4nKn9KPx6F1peP8UleyG/i+U434qeWlhBCfNhVdP+geyzi18X3FzLTcw9kwp4Ph13NrekFBAgQIECAwGoLKB/TvudjbK/B6JP+dOM721OQ/RxufE96bBCZ2Q5RKsUr3rMQ3y89eTl30sIRzfssYy77ZIdtDV550NIQvy6+P8t0Le452Z6h8rkkpXNQlI/FcRsSAQIECBAgsBICa1Y+4j0ho/uVh12txCI0kQQIECBAgAABAgRWQ0D5mLbnYzWWo6kkQIAAAQIECBAg0HoB5UP5aH1ITSABAgQIECBAgEA3BJQP5aMbSTYXBAgQIECAAAECrRdQPpSP1ofUBBIgQIAAAQIECHRDQPlQPrqRZHNBgAABAgQIECDQegHlQ/lofUhNIAECBAgQIECAQDcElA/loxtJNhcECBAgQIAAAQKtF1A+lI/Wh9QEEiBAgAABAgQIdENA+VA+upFkc0GAAAECBAgQINB6AeWjpeVj50Iv9HqbYaf1ETKBBAgQIECAAAECBGYTWIPy8SBsb22FreL/Tx6EZ8PSkf3c3d2dTWxJzzpY+dgL/TO9sHl3/oncu7ERer1e6J3ph735X+4VBAgQIECAAAECBKYKrEn56Ifbj5+NFY6seCQ/21Y+pi65yiccrHykRefCTkgLiPJRKeuXBAgQIECAAAEChxdQPho67CrZoN+8uxM2k70J6f/CIVSP+mHjTD/sZHsber2wcWO4vyF5LHtNXASGr+unh2QNhpvt5cj3XGSvTX9uhP6j2UOifMxu5ZkECBAgQIAAAQLzC6xJ+SgedlW9F2TRez4Gh02NNv6T+3HBKN8vlJNkOd7dHD8EKismF4Zngow952B7PrLYKB+ZhJ8ECBAgQIAAAQJNCKxB+SgfbvXkTj9sbW2HBw2f81EqG8mSS4pCVhrSEhGVjXjpjhWLEEL8uvh+UD5iRvcJECBAgAABAgTaI7B25ePZ49uhr3xUJtCej0oWvyRAgAABAgQIEFiQwNqVjwefbIWtJVztqrznI9ojMbbHomJpHmjPRwjl8VYMd8KvlI8JOB4iQIAAAQIECBA4tMAalI/oUrsVxaOJq10lJWBwovngZ35+R7LIJpWPpHSUThovXP42fl18Px92Nu7ROSeTkpKWjnic2SFik17oMQIECBAgQIAAAQJzCKxB+Sif81G8vG7xdhMnnJcKxxwLxVMJECBAgAABAgQIdFFA+WjwUrvKRxffMuaJAAECBAgQIEDgoALKh/Jx0Ox4HQECBAgQIECAAIG5BJSPhsrHXEvBkwkQIECAAAECBAisgYDyoXysQczNIgECBAgQIECAQBsElA/low05NA0ECBAgQIAAAQJrIKB8KB9rEHOzSIAAAQIECBAg0AYB5UP5aEMOTQMBAgQIECBAgMAaCCgfyscaxNwsEiBAgAABAgQItEFA+VA+2pBD00CAAAECBAgQILAGAsrHYcrH3c3Q6/UG/y/srEFczCIBAgQIECBAgACBgwusTfl4cqcftra2Bv8/eRCeDUtH9nN3d/fAins3NkJP+TiwnxcSIECAAAECBAish8BalI8Hn2yFrf7t8CQqHFnxSH4qH+sReHNJgAABAgQIECDw4gTWoHw8CNtb2+HBhOIxuXzshM3s0KpeL2zc2BtbWtV7PvZC/8zwkKz09ZuheGBW+pp8uOXHQhi+9kw/jI9tbPR+QYAAAQIECBAgQGAlBLpfPh5sp3s9bid7P4aHXfXvPJnxsKukBGyE/qNsWcb3B7+vLB/J+SB1h2LFj8X3lY8M3E8CBAgQIECAAIEOCaxH+djaCtsPng0LR7InpB9uP87uD35WHnb1qB828r0To70Ym3fLCagsH/lr470aIexcGA0rP2HdXo4yqnsECBAgQIAAAQKdE1iP8lE63+NJuN0vlpEp5WOGUlBZPvKoZIdtjfagJOUjLjD5090gQIAAAQIECBAg0FGB7pePZ9Gejse3Q7/iHJDKPR9hUByqzvMo5mFy+Rg8s1g40uf3xveIjIbpnI+RhVsECBAgQIAAAQJdEViD8vEsPEvO+8gus1txyNXEE87zw6eyQ6VGpWFQIrLfD38Oz/MYeyw6/yM+9KpccJSPrrzBzAcBAgQIECBAgMBIYD3Kx5QrXU0sHyMrtwgQIECAAAECBAgQOISA8jEsJtWHXR1C1ksJECBAgAABAgQIECgJKB/KRykQ7hAgQIAAAQIECBBoSkD5UD6aypbhEiBAgAABAgQIECgJKB/KRykQ7hAgQIAAAQIECBBoSkD5UD6aypbhEiBAgAABAgQIECgJKB/KRykQ7hAgQIAAAQIECBBoSkD5UD6aypbhEiBAgAABAgQIECgJKB/KRykQ7hAgQIAAAQIECBBoSkD5UD6aypbhEiBAgAABAgQIECgJKB9Nl4+7m6HX6w3/b4adEr87BAgQIECAAAECBNZHoPPl48mdftja2or+98Ptx8/Cs2HxSH428w3nO2GztxH6jxYZqCaGOXn69m5sDMrTmX7Ym/xUjxIgQIAAAQIECBCoFeh8+SgWjPT249uhv7UdHhSKR2Pl41E/bPQWvbdjueVj50Iv9C7shLSAKB+1byQPECBAgAABAgQITBdYu/Lx4JOt0L/zpLTXY+HlIy0d2aFWo58bN7L9BkmBGP2+FxeU6PXZ69IiUHpdMoxFl5vq0Cgf1S5+S4AAAQIECBAgMLvAepWPmr0eCy8fmf+Mez6SDfusYIRh8di8mw0k/nmAPR9RmcnOQakfRzzOYM/HOInfECBAgAABAgQIzCmwVuWjbq/H8svHXuifKe75GBzalCy7dA/DhUmnpR+gfMwZiqqn2/NRpeJ3BAgQIECAAAEC8wisT/l4sB22Ks71SIrHsstHvCFfLBzF29UL8gDlw56Pakq/JUCAAAECBAgQWKrAmpSPJ+F2v/pcjxdRPrKTuAdLenj+R7a3Iy0Kk66QNdhrMs8hU4tIVFyYFjFMwyBAgAABAgQIEFgvgfUoH1P2eix7z0d2Xsfg3IuN0L+xmV5RKo9e6btBeqPzQbInlB5v9oTztHTEJ7lnRSmbHj8JECBAgAABAgQIzCCwHuUjuqxutrej+LOZ7/mYYQl4CgECBAgQIECAAIE1EVA+mjznY01CZDYJECBAgAABAgQIzCKgfCgfs+TEcwgQIECAAAECBAgcWkD5UD4OHSIDIECAAAECBAgQIDCLgPKhfMySE88hQIAAAQIECBAgcGgB5UP5OHSIDIAAAQIECBAgQIDALALKh/IxS048hwABAgQIECBAgMChBZQP5ePQITIAAgQIECBAgAABArMIKB/Kxyw58RwCBAgQIECAAAEChxZQPpSPQ4fIAAgQIECAAAECBAjMIqB8vIDysXdjI/Qu7ExcPjsXeqHX2wzlZ+2F/pnk98n/jdB/NHEQtQ8mw964sVf7+At/4FE/bJzphxZP4QsnMgEECBAgQIAAgVUUWIvy8eROP2xtbeX/+3eehGfD0pH93N3dXdryO3j5yCZxJ2wesHyk427Fhn0yD4MitXk3m6/Rz1mMRs92iwABAgQIECBAYBUEul8+Ht8O/f7t8CQvGw/C9tZ2eJDff5YWkbaVj8nhOWD5SPYojO1NmTymyY8eZjqSPTeDAlJVPkIY7OWpfmzyVHmUAAECBAgQIECgnQLdLx/PkrKxFbK9HelekFIZaaB83N2sOKwq2ZgeHCo1+FS/XziEqnB4VVoQhodW1e6hqNvoH2zMDw7LGj+0qu5wq+T3mzeSYjJtvBUhHk7vwUrCpPIRQkgcqwyS3/d64WDjrJgHvyJAgAABAgQIEFiKwBqUj6RcPAm3+8PDrj55MHbIVXLo1UL3fBTOWUg37NPDipIN7UHJSMtHYeO5shTUbXinsagqH6NyM0jOtPujfCXjH51DcpA9DoMSMf95JFPKR6iaz2EpKfiN5sQtAgQIECBAgACBNgusQfko7vkYlpCm93ykG81J0dgL/RubYTM5ubxQSOLzGeL7aWDmLR/FPSbZHozSBnqyIV/Yw1JIZVx+4vuFp064OSgtlXsqal81rXzEBap2QB4gQIAAAQIECBBYAYHul48H22GrVDaSMtIPtx8PDrdq5oTzZKN5M+w86ofNG3th58Jm2EnKxPAKV3HZiO+nuTlI+ag6RCkPYZPlY1AiFr/nQ/nIF58bBAgQIECAAIEOCKxH+SieYJ6cgN54+Qhh58JG2LywmV4ONykXye1s4zwuG/H9NFfzlo90b8v4eR6jjNZvyMd7OuL7o2FU3GrynI98D1I03sSmtFcnetxdAgQIECBAgACBVgp0v3w8exbiS+1uPyjv9Vj4OR8hKR/JeRTDw5yiDfS4bJTuDzess5PG05/5Ho3BHobSY8VL7o4delU+zKquVMS/j+/XJzeZnoN838iU+chGWFfAlI9MyE8CBAgQIECAwEoJrEX5yA6tmvRzoSectzUCaTkpF5K2TqpL7bZ3yZgyAgQIECBAgMBBBZSPF/AN5wddWIt4XbqXJd+TsoghNjOM0t6gZkZhqAQIECBAgAABAksWUD7WrHwk+UoOq2r1d2Qke2hWoCAt+b1qdAQIECBAgACBlRdQPtawfKx8as0AAQIECBAgQIDASgooH8rHSgbXRBMgQIAAAQIECKyegPKhfKxeak0xAQIECBAgQIDASgooH8rHSgbXRBMgQIAAAQIECKyegPKhfKxeak0xAQIECBAgQIDASgooH8rHSgbXRBMgQIAAAQIECKyegPKhfKxeak0xAQIECBAgQIDASgooH20tH+m3kfdCr5f83wj9Ry82X8mX/rX6u0FeLI+xEyBAgAABAgQIzCCwFuXjyZ1+2NraGv7fDg+GheNZ4efu7u4MXMt6yl7on1n0FwEebJjZN6Lv3NgM/bv9sNGCIrSspWA8BAgQIECAAAECixXofvl4sB22tgqFI7nfvx2eFIpHUkLaVT52wubCN/IPVj7SuGV7YXzr+GLffYZGgAABAgQIEFgzgc6Xj2SvR//OkzDay/EgbBfLSKsOu0pKR3aoVeHnhZ08ljsXCr/vje8dKT++GZJXpnsvxoY726Fc43s+xseZT9ysN5Iyc6Yf+oV5cUjXrHieR4AAAQIECBBYXYHOl49n0Z6OwSFY/XD78bNCIVnRPR/Djfi9Yf7S4lG7d+IQez6GBSYuCOWiMyxFteMvvEmyPSlZqbq7GXqzvK4wCDcJECBAgAABAgRWT6D75ePZs/Dgk+x8j63Qv3O7xXs+sgDVH3Y1vhdjsHcjhPrXDIZ6uPKRTdlCfqblI5vuEEJ8fyEjMRACBAgQIECAAIG2CaxF+RgdcvUsPHt8O/RX9ZyPdCO9cLhUaaN9+eXjcHs+lI+2rQxMDwECBAgQIECgaYE1Kx/J+R5bYftB+ZCrlTnhPDk8qTfaaB9s/Ef3s0OZKpKTPH/jRnaQVsUTlvWrUmmy52NZ7MZDgAABAgQIEHjRAmtQPgaFY3Cp3fFzPbK9IqtxtavBoVOD7/5IikQ/bBbKSAjlx4tFJQ1autGfnbBe2IOy7BQqH8sWNz4CBAgQIECAQCsE1qB8jO/lyApH8We7ykcrsmEiCBAgQIAAAQIECCxUQPlo1aV2F7psDYwAAQIECBAgQIBAqwSUD+WjVYE0MQQIECBAgAABAt0VUD6Uj+6m25wRIECAAAECBAi0SkD5UD5aFUgTQ4AAAQIECBAg0F0B5UP56G66zRkBAgQIECBAgECrBJQP5aNVgTQxBAgQIECAAAEC3RVQPpSP7qbbnBEgQIAAAQIECLRKQPlQPloVSBNDgAABAgQIECDQXQHlQ/nobrrNGQECBAgQIECAQKsElA/lo1WBNDEECBAgQIAAAQLdFehQ+XgQtre2wtbWVth+8Cw8G5aK9Ofj26E/fGxrqx9uP44ef/Ys7O7udncpH3LO9m5shF6vF3pn+mHvkMPycgIECBAgQIAAgfUV6Eb5SMtFUioGBaRcPpLfFQpH+tzt8KBYTpSP2nfAzoVe6F3YCWkBUT5qnTxAgAABAgQIECAwXaAb5SMvEuPl48mdftj65EG+JyS9X7F3pC17PpKN/I0b/bCZ7GlI/2+GnenLsfFnKB+NExsBAQIECBAgQKDzAp0vHw8+2Qr9O0/S8pHcTopI8XfZ4VltKh9J6di8O8hesudh48YhD3Z61A8beZnJSs1oHLOkXPmYRclzCBAgQIAAAQIEJgmsSfl4EG73yyUkKyStLB8XRvs60o3+wv1JCzPc3RzuLRkUjEOXlsLIlI8ChpsECBAgQIAAAQIHEuh8+Rg/zOpJWkTK54W054TzuGzE9w+0lO35OBCbFxEgQIAAAQIECCxWoPPl41l8gvmD7bC11d4TzuOyEd9f7OKffWjpdDjhfHYwzyRAgAABAgQIEBgT6Ej5GF1mN7nU7uB/4QpXaeHIfj9ePJJDr1p1zkfhMKsXXT7S8cfnixSmbyxRfkGAAAECBAgQIECgRqAj5WP8ezuyczlm/dmW8lGznPyaAAECBAgQIECAwMoLKB++4XzlQ2wGCBAgQIAAAQIEVkNA+VA+ViOpppIAAQIECBAgQGDlBZQP5WPlQ2wGCBAgQIAAAQIEVkNA+VA+ViOpppIAAQIECBAgQGDlBZQP5WPlQ2wGCBAgQIAAAQIEVkNA+VA+ViOpppIAAQIECBAgQGDlBZQP5WPlQ2wGCBAgQIAAAQIEVkNA+VA+ViOpppIAAQIECBAgQGDlBZQP5WPlQ2wGCBAgQIAAAQIEVkNA+Whp+di50Au93mbYWWaO7m6GXi8Z7wsY9zLn07gIECBAgAABAgReiECHyseDsL21Fba2tsL2g2fh2bBUDH5Oemzw3N3d3ReyAOpGerDysRf6Z3ph827dUCf9fids9jZC/9Gk58z5WFJmzvTD3pwv83QCBAgQIECAAIFuCnSjfDy+Hfpb/XD78aBklMrHpMcKBaVt5eNgcTtE+XjUDxuL3tOifBxsMXoVAQIECBAgQKCjAt0oH3mJqCgfMz32LCy6fCR7LjbvJnsTKg5jSjb0z/TDzo2N/DCnjRvD/QNpCRi+Jt5rMHxdPz0ka/CcbC/HXmFYo0OnZtiTURxfPq29UDk9yeNV0zT2uuJ8Z/PfC70LSz2IrKNvWbNFgAABAgQIEFhdAeVjWE6aKB+9wmFMSRmJN+jL96PzO6r2GmRFIduIH3tO83s+BqVqGPjh9GQFaOxtMDZ9Y8/wCwIECBAgQIAAgTUSUD4aLB95uUgClWyIZ6Uh3WiPykYcuqoN9/h18f3QRPkY34uRzVe6tyWbp3j6s3mO95RUPc/vCBAgQIAAAQIE1kJA+VA+BkEfKzKDX6cnvhcKRnEPjvKxFusIM0mAAAECBAgQWJiA8rGU8hHtkajZ0C8t1QPt+QihWA5Kw5t2p3KaBtOd7ekI6XPiw8cmnFdSOcxpE+JxAgQIECBAgACBrgp0pHyMLqWbXGp38D+5+lVyGd1Jj40uydvMOR+jk63zDfgkSZM2ypPSUTiBO72dHboUvy6+nw87G++EYhAnumpYyXNK07MZ+jc2RueujD1eKCbD4ad7TrL5KexBiUfvPgECBAgQIECAQPcFOlI+RiWi/P0es/++ifJRKhzdz5I5JECAAAECBAgQIDBRQPlYymFXE5eBBwkQIECAAAECBAishYDyoXysRdDNJAECBAgQIECAwIsXUD4aKh8vftGaAgIECBAgQIAAAQLtElA+lI92JdLUECBAgAABAgQIdFZA+VA+OhtuM0aAAAECBAgQINAuAeVD+WhXIk0NAQIECBAgQIBAZwWUD+Wjs+E2YwQIECBAgAABAu0SUD6Uj3Yl0tQQIECAAAECBAh0VkD5UD46G24zRoAAAQIECBAg0C4B5UP5aFciTQ0BAgQIECBAgEBnBZQP5aOz4TZjBAgQIECAAAEC7RJQPpSPdiXS1BAgQIAAAQIECHRWQPlQPjobbjNGgAABAgQIECDQLgHlQ/loVyJNDQECBAgQIECAQGcFlA/lo7PhNmMECBAgQIAAAQLtElA+lI92JdLUECBAgAABAgQIdFZA+VA+OhtuM0aAAAECBAgQINAuAeVD+WhXIk0NAQIECBAgQIBAZwWUD+Wjs+E2YwQIECBAgAABAu0SUD6Uj3Yl0tQQIECAAAECBAh0VkD5UD46G24zRoAAAQIECBAg0C4B5UP5aFciTQ0BAgQIECBAgEBnBZQP5aOz4TZjBAgQIECAAAEC7RJQPpSPdiXS1BAgQIAAAQIECHRWQPlQPjobbjNGgAABAgQIECDQLgHlQ/loVyJNDQECBAgQIECAQGcFlA/lo7PhNmMECBAgQIAAAQLtElA+lI92JdLUECBAgAABAgQIdFZA+VA+OhtuM0aAAAECBAgQINAuAeVD+WhXIk0NAQIECBAgQIBAZwWUD+Wjs+E2YwQIECBAgAABAu0SUD6Uj3Yl0tQQIECAAAECBAh0VkD5UD46G24zRoAAAQIECBAg0C4B5UP5aFciTQ0BAgQIECBAgEBnBZQP5aOz4TZjBAgQIECAAAEC7RJQPpSPdiXS1BAgQIAAAQIECHRWQPlQPjobbjNGgAABAgQIECDQLgHlQ/loVyJNDQECBAgQIECAQGcFlA/lo7PhNmMECBAgQIAAAQLtElA+lI92JdLUECBAgAABAgQIdFZA+VA+OhtuM0aAAAECBAgQINAuAeVD+WhXIk0NAQIECBAgQIBAZwWUD+Wjs+E2YwQIECBAgAABAu0SUD6Uj3Yl0tQQIECAAAECBAh0VkD5UD46G24zRoAAAQIECBAg0C4B5UP5aFciTQ0BAgQIECBAgEBnBZQP5aOz4TZjBAgQIECAAAEC7RJQPpSPdiXS1BAgQIAAAQIECHRWQPlQPjobbjNGgAABAgQIECDQLgHlQ/loVyJNDQECBAgQIECAQGcFlA/lo7PhNmMECBAgQIAAAQLtElA+lI92JdLUECBAgAABAgQIdFZA+VA+OhtuM0aAAAECBAgQINAuAeVD+WhXIk0NAQIECBAgQIBAZwWUD+Wjs+E2YwQIECBAgAABAu0SUD6Uj3Yl0tQQIECAAAECBAh0VkD5UD46G24zRoAAAQIECBAg0C4B5UP5aFciTQ0BAgQIECBAgEBnBZQP5aOz4TZjBAgQIECAAAEC7RJQPpSPdiXS1BAgQIAAAQIECHRWQPlQPjobbjNGgAABAgQIECDQLgHlQ/loVyJNDQECBAgQIECAQGcFlA/lo7PhNmMECBAgQIAAAQLtElA+lI92JdLUECBAgAABAgQIdFZA+VA+OhtuM0aAAAECBAgQINAuAeVD+WhXIk0NAQIECBAgQIBAZwWUD+Wjs+E2YwQIECBAgAABAu0SUD6Uj3Yl0tQQIECAAAECBAh0VkD5UD46G24zRoAAAQIECBAg0C4B5UP5aFciTQ0BAgQIECBAgEBnBZQP5aOz4TZjBAgQIECAAAEC7RJQPpSPdiXS1BAgQIAAAQIECHRWQPlQPjobbjNGgAABAgQIECDQLgHlQ/loVyJNDQECBAgQIECAQGcFlA/lo7PhNmMECBAgQIAAAQLtElA+lI92JdLUECBAgAABAgQIdFZA+VA+OhtuM0aAAAECBAgQINAuAeVD+WhXIk0NAQIECBAgQIBAZwWUD+Wjs+E2YwQIECBAgAABAu0SUD6Uj3Yl0tQQIECAAAECBAh0VkD5UD46G24zRoAAAQIECBAg0C4B5UP5aFciTQ0BAgQIECBAgEBnBZQP5aOz4TZjBAgQIECAAAEC7RJQPpSPdiXS1BAgQIAAAQIECHRWQPlQPjobbjNGgAABAgQIECDQLgHlQ/loVyJNDQECBAgQIECAQGcFlA/lo7PhNmMECBAgQIAAAQLtElA+lI92JdLUECBAgAABAgQIdFZA+VA+OhtuM0aAAAECBAgQINAuAeVD+WhXIk0NAQIECBAgQIBAZwWUD+Wjs+E2YwQIECBAgAABAu0SaGX5SCbKfwYyIAMyIAMyIAMyIAMy0L0MNF2Hek2PwPAJECBAgAABAgQIECCQCCgfckCAAAECBAgQIECAwFIElI+lMBsJAQIECBAgQIAAAQLKhwwQIECAAAECBAgQILAUAeVjKcxGQoAAAQIECBAgQICA8iEDBAgQIECAAAECBAgsRUD5WAqzkRAgQIAAAQIECBAgoHzIAAECBAgQIECAAAECSxFQPpbCbCQECBAgQIAAAQIECCgfMkCAAAECBAgQIECAwFIElI+lMBsJAQIECBAgQIAAAQLKhwwQIECAAAECBAgQILAUAeVjKcxGQoAAAQIECBAgQICA8iEDBAgQIECAAAECBAgsRUD5WAqzkRAgQIAAAQIECBAgoHzIAAECBAgQIECAAAECSxFQPpbCbCQECBAgQIAAAQIECCgfMkCAAAECBAgQIECAwFIElI+lMBsJAQIECBAgQIAAAQLKhwwQIECAAAECBAgQILAUAeV2bXHUAAAdrUlEQVRjKcxGQoAAAQIECBAgQICA8iEDBAgQIECAAAECBAgsRUD5WAqzkRAgQIAAAQIECBAgoHzIAAECBAgQIECAAAECSxGYq3z0er3gPwMZkAEZkAEZkAEZkAEZ6GYGmm4gyodCpVDKgAzIgAzIgAzIgAzIQJoB5UMQrAxkQAZkQAZkQAZkQAZkYCkZUD4EbSlBs+u0m7tOLVfLVQZkQAZkQAZkYJ4MKB/Kh/IhAzIgAzIgAzIgAzIgA0vJgPIhaEsJ2jyN2HN9giIDMiADMiADMiAD3cyA8qF8KB8yIAMyIAMyIAMyIAMysJQMKB+CtpSg+fSim59eWK6WqwzIgAzIgAzIwDwZUD6UD+VDBmRABmRABmRABmRABpaSAeVD0JYStHkasef6BEUGZEAGZEAGZEAGupkB5UP5UD5kQAZkQAZkQAZkQAZkYCkZUD4EbSlB8+lFNz+9sFwtVxmQARmQARmQgXkysPrl468Px+bhP//vv8c2qP/3/wsh/H//O/b7BKv4WHp7bIjZL/4TvvhjErD/DeNjDeHhXwvh++MX4T8he37h90nZKT6W3s6GX/GzZponLeRkHsYN/jt88Z/y8Mef0wu9yLM0T4paZX4mLQuPRdmXIRmSARmQARmQgbXOQHlrdPH3evMMcr4NtWxj+mH431KIh8XgP1+E/y78vlgw4vHUPfbf/+8/IUTDGbx2MI7Shvlwoz3/XbFgFKYjfX3tYxXDjV9bez/zGIiPFYu/PqwoR1FhSuehUJji+7XjtoEZZ8p9mZABGZABGZABGZCB8QzM0w0O8tzGykdaDEJcPLIZHGyIFzfA6wpGEoq6x+YqH/FwagtGtOejtEF/8PIxmtbxea8Lfnm+q19Xfk7m62edqd/LhgzIgAzIgAzIgAzUZ+AghWKe1zRUPmbYSE8/tR+Vk0kb0XWPjTboY8Dq8Zeev8jykQ4r2ktRKi3F6asuEVVvgtJ810xvaZ5qx1kcv9tV1n4nFzIgAzIgAzIgAzIwVzWYp3Pkz51rDDMvkJoN5dLro+eUNrSjjei6x+o3vKvLR2k40fgnTdvoserhDs4RWXT5iMYVlbV8mup+Hxnmz/f7tT6OUw78YZUBGZABGZABGZiUgbwlNHSjwfIx2qtRPYPJxvXo/IVSMYg2kOsem6d8pM8tjK90Unk0vvrHokIQv26m+7Ps+Rg8p3Q+S13JqPv9TNPizVedTS5cZEAGZEAGZEAG1jMDDXWOfLANlo9RsagMb7Tnoa5gJK+te2xa+cjnMr0RlaFo/KVprH1sCeUjHXfFlb/qSkbd75UPezhkQAZkQAZkQAZkQAbmzEB5+3nx95opH8NL3eZXlqqa6Wijua5gHKZ8TBx/bcFo5oTzUbmZsOcjNak5fKtmeusL2Hq29ZGz+WchAzIgAzIgAzIgA/NmYPF1ozzEhspHL0zeKB7fAB8cFhXtnUhLS/3ehvpx1L9mtAAGzylecSt77HDDnRby8XlPx5sWj0l7i6rnKSltVfOQzYuf05aHx2VEBmRABmRABmRABrIMlKvC4u81Vj56veF5C2OX2x1sRJfOZyiUjPiLBtM9IpXf5TGp4FRvqGeo2c9B4Yk2+IeHPVXvNakZ7sTXxGGuLh+zlIixghbtPcrmy8/Y3H2ZkAEZkAEZkAEZkIFZMrD4ulEeYoPlY7CABxvM5ZFWb9gnz88KS+H5E75BfCF7KIaHOo3GGJWR0iFjTZWPivnOJ6g8PWXPqj1F3lizvLE8R05kQAZkQAZkQAZkYDwD+SZoQzcaLx8W6vhCZcJEBmRABmRABmRABmSgjRloqHPkg1U+Sns2vAna+CYwTXIpAzIgAzIgAzIgA8vJQN4SGrqhfCgfLkEnAzIgAzIgAzIgAzIgA2kGGuoc+WCVD0GzspEBGZABGZABGZABGZCBNAN5S2johvIhaFY2MiADMiADMiADMiADMpBmoKHOkQ9W+RA0KxsZkAEZkAEZkAEZkAEZSDOQt4SGbigfgmZlIwMyIAMyIAMyIAMyIANpBhrqHPlglQ9Bs7KRARmQARmQARmQARmQgTQDeUto6IbyIWhWNjIgAzIgAzIgAzIgAzKQZqChzpEPVvkQNCsbGZABGZABGZABGZABGUgzkLeEhm4oH4JmZSMDMiADMiADMiADMiADaQYa6hz5YJUPQbOykQEZkAEZkAEZkAEZkIE0A3lLaOiG8iFoVjYyIAMyIAMyIAMyIAMykGagoc6RD1b5EDQrGxmQARmQARmQARmQARlIM5C3hIZuKB+CZmUjAzIgAzIgAzIgAzIgA2kGGuoc+WCVD0GzspEBGZABGZABGZABGZCBNAN5S2johvIhaFY2MiADMiADMiADMiADMpBmoKHOkQ9W+RA0KxsZkAEZkAEZkAEZkAEZSDOQt4SGbigfgmZlIwMyIAMyIAMyIAMyIANpBhrqHPlglQ9Bs7KRARmQARmQARmQARmQgTQDeUto6IbyIWhWNjIgAzIgAzIgAzIgAzKQZqChzpEPVvkQNCsbGZABGZABGZABGZABGUgzkLeEhm4oH4JmZSMDMiADMiADMiADMiADaQYa6hz5YJUPQbOykQEZkAEZkAEZkAEZkIE0A3lLaOiG8iFoVjYyIAMyIAMyIAMyIAMykGagoc6RD1b5EDQrGxmQARmQARmQARmQARlIM5C3hIZuKB+CZmUjAzIgAzIgAzIgAzIgA2kGGuoc+WCVD0GzspEBGZABGZABGZABGZCBNAN5S2johvIhaFY2MiADMiADMiADMiADMpBmoKHOkQ9W+RA0KxsZkAEZkAEZkAEZkAEZSDOQt4SGbigfgmZlIwMyIAMyIAMyIAMyIANpBhrqHPlglQ9Bs7KRARmQARmQARmQARmQgTQDeUto6IbyIWhWNjIgAzIgAzIgAzIgAzKQZqChzpEPVvkQNCsbGZABGZABGZABGZABGUgzkLeEhm4oH4JmZSMDMiADMiADMiADMiADaQYa6hz5YJUPQbOykQEZkAEZkAEZkAEZkIE0A3lLaOiG8iFoVjYyIAMyIAMyIAMyIAMykGagoc6RD1b5EDQrGxmQARmQARmQARmQARlIM5C3hIZuKB+CZmUjAzIgAzIgAzIgAzIgA2kGGuoc+WCVD0GzspEBGZABGZABGZABGZCBNAN5S2johvIhaFY2MiADMiADMiADMiADMpBmoKHOkQ9W+RA0KxsZkAEZkAEZkAEZkAEZSDOQt4SGbigfgmZlIwMyIAMyIAMyIAMyIANpBhrqHPlglQ9Bs7KRARmQARmQARmQARmQgTQDeUto6IbyIWhWNjIgAzIgAzIgAzIgAzKQZqChzpEPVvkQNCsbGZABGZABGZABGZABGUgzkLeEhm4oH4JmZSMDMiADMiADMiADMiADaQYa6hz5YJUPQbOykQEZkAEZkAEZkAEZkIE0A3lLaOiG8iFoVjYyIAMyIAMyIAMyIAMykGagoc6RD1b5EDQrGxmQARmQARmQARmQARlIM5C3hIZuKB+CZmUjAzIgAzIgAzIgAzIgA2kGGuoc+WCVD0GzspEBGZABGZABGZABGZCBNAN5S2johvIhaFY2MiADMiADMiADMiADMpBmoKHOkQ9W+RA0KxsZkAEZkAEZkAEZkAEZSDOQt4SGbigfgmZlIwMyIAMyIAMyIAMyIANpBhrqHPlglQ9Bs7KRARmQARmQARmQARmQgTQDeUto6IbyIWhWNjIgAzIgAzIgAzIgAzKQZqChzpEPVvkQNCsbGZABGZABGZABGZABGUgzkLeEhm4oH4JmZSMDMiADMiADMiADMiADaQYa6hz5YJUPQbOykQEZkAEZkAEZkAEZkIE0A3lLaOiG8iFoVjYyIAMyIAMyIAMyIAMykGagoc6RD1b5EDQrGxmQARmQARmQARmQARlIM5C3hIZuKB+CZmUjAzIgAzIgAzIgAzIgA2kGGuoc+WCVD0GzspEBGZABGZABGZABGZCBNAN5S2johvIhaFY2MiADMiADMiADMiADMpBmoKHOkQ9W+RA0KxsZkAEZkAEZkAEZkAEZSDOQt4SGbigfgmZlIwMyIAMyIAMyIAMyIANpBhrqHPlglQ9Bs7KRARmQARmQARmQARmQgTQDeUto6IbyIWhWNjIgAzIgAzIgAzIgAzKQZqChzpEPVvkQNCsbGZABGZABGZABGZABGUgzkLeEhm4oH4JmZSMDMiADMiADMiADMiADaQYa6hz5YJUPQbOykQEZkAEZkAEZkAEZkIE0A3lLaOiG8iFoVjYyIAMyIAMyIAMyIAMykGagoc6RD1b5EDQrGxmQARmQARmQARmQARlIM5C3hIZuKB+CZmUjAzIgAzIgAzIgAzIgA2kGGuoc+WCVD0GzspEBGZABGZABGZABGZCBNAN5S2johvIhaFY2MiADMiADMiADMiADMpBmoKHOkQ9W+RA0KxsZkAEZkAEZkAEZkAEZSDOQt4SGbigfgmZlIwMyIAMyIAMyIAMyIANpBhrqHPlglQ9Bs7KRARmQARmQARmQARmQgTQDeUto6IbyIWhWNjIgAzIgAzIgAzIgAzKQZqChzpEPVvkQNCsbGZABGZABGZABGZABGUgzkLeEhm4oH4JmZSMDMiADMiADMiADMiADaQYa6hz5YJUPQbOykQEZkAEZkAEZkAEZkIE0A3lLaOiG8iFoVjYyIAMyIAMyIAMyIAMykGagoc6RD1b5EDQrGxmQARmQARmQARmQARlIM5C3hIZuKB+CZmUjAzIgAzIgAzIgAzIgA2kGGuoc+WCVD0GzspEBGZABGZABGZABGZCBNAN5S2johvIhaFY2MiADMiADMiADMiADMpBmoKHOkQ9W+RA0KxsZkAEZkAEZkAEZkAEZSDOQt4SGbigfgmZlIwMyIAMyIAMyIAMyIANpBhrqHPlglQ9Bs7KRARmQARmQARmQARmQgTQDeUto6IbyIWhWNjIgAzIgAzIgAzIgAzKQZqChzpEPVvkQNCsbGZABGZABGZABGZABGUgzkLeEhm4oH4JmZSMDMiADMiADMiADMiADaQYa6hz5YJUPQbOykQEZkAEZkAEZkAEZkIE0A3lLaOiG8iFoVjYyIAMyIAMyIAMyIAMykGagoc6RD1b5EDQrGxmQARmQARmQARmQARlIM5C3hIZuKB+CZmUjAzIgAzIgAzIgAzIgA2kGGuoc+WCVD0GzspEBGZABGZABGZABGZCBNAN5S2johvIhaFY2MiADMiADMiADMiADMpBmoKHOkQ9W+RA0KxsZkAEZkAEZkAEZkAEZSDOQt4SGbigfgmZlIwMyIAMyIAMyIAMyIANpBhrqHPlglQ9Bs7KRARmQARmQARmQARmQgTQDeUto6Iby8f+3dwe5TTNhGICXSAiJDSs2qIsuehAuwA04AMeAU3ABdlyh7EGcgy0HmF8ex4PjpqHfR8bxLz+VUE2YmThPXiy/OC6C5mAjAzIgAzIgAzIgAzIgAzUDnTpHW1b5EDQHGxmQARmQARmQARmQARmoGWgtodOG8iFoDjYyIAMyIAMyIAMyIAMyUDPQqXO0ZZUPQXOwkQEZkAEZkAEZkAEZkIGagdYSOm0oH4LmYCMDMiADMiADMiADMiADNQOdOkdbVvkQNAcbGZABGZABGZABGZABGagZaC2h04byIWgONjIgAzIgAzIgAzIgAzJQM9Cpc7RllQ9Bc7CRARmQARmQARmQARmQgZqB1hI6bSgfguZgIwMyIAMyIAMyIAMyIAM1A506R1tW+RA0BxsZkAEZkAEZkAEZkAEZqBloLaHThvIhaA42MiADMiADMiADMiADMlAz0KlztGWVD0FzsJEBGZABGZABGZABGZCBmoHWEjptKB+C5mAjAzIgAzIgAzIgAzIgAzUDnTpHW1b5EDQHGxmQARmQARmQARmQARmoGWgtodOG8iFoDjYyIAMyIAMyIAMyIAMyUDPQqXO0ZZUPQXOwkQEZkAEZkAEZkAEZkIGagdYSOm0oH4LmYCMDMiADMiADMiADMiADNQOdOkdbVvkQNAcbGZABGZABGZABGZABGagZaC2h04byIWgONjIgAzIgAzIgAzIgAzJQM9Cpc7RllQ9Bc7CRARmQARmQARmQARmQgZqB1hI6bSgfguZgIwMyIAMyIAMyIAMyIAM1A506R1tW+RA0BxsZkAEZkAEZkAEZkAEZqBloLaHThvIhaA42MiADMiADMiADMiADMlAz0KlztGWVD0FzsJEBGZABGZABGZABGZCBmoHWEjptKB+C5mAjAzIgAzIgAzIgAzIgAzUDnTpHW1b5EDQHGxmQARmQARmQARmQARmoGWgtodOG8iFoDjYyIAMyIAMyIAMyIAMyUDPQqXO0ZUPlo82yQYAAAQIECBAgQIAAgaCA8hEEM5wAAQIECBAgQIAAgZyA8pFzM4sAAQIECBAgQIAAgaCA8hEEM5wAAQIECBAgQIAAgZyA8pFzM4sAAQIECBAgQIAAgaCA8hEEM5wAAQIECBAgQIAAgZyA8pFzM4sAAQIECBAgQIAAgaCA8hEEM5wAAQIECBAgQIAAgZyA8pFzM4sAAQIECBAgQIAAgaCA8hEEM5wAAQIECBAgQIAAgZyA8pFzM4sAAQIECBAgQIAAgaCA8hEEM5wAAQIECBAgQIAAgZyA8pFzM4sAAQIECBAgQIAAgaCA8hEEM5wAAQIECBAgQIAAgZyA8pFzM4sAAQIECBAgQIAAgaCA8hEEM5wAAQIECBAgQIAAgZyA8pFzM4sAAQIECBAgQIAAgaCA8hEEM5wAAQIECBAgQIAAgZyA8pFzM4sAAQIECBAgQIAAgaCA8hEEM5wAAQIECBAgQIAAgZyA8pFzM4sAAQIECBAgQIAAgaCA8hEEM5wAAQIECBAgQIAAgZyA8pFzM4sAAQIECBAgQIAAgaCA8hEEM5wAAQIECBAgQIAAgZxAvHz8/lHKz7elfHtRyv0zv6IGg9vgNzj6IkCAAAECBAgQILAjgVj5GE6YlY7LFK7BUQHZ0V81L5UAAQIECBAgQCBWPoZ/sY/+S7/xj5sNnr4IECBAgAABAgQI7EQgVj5c9Xi8SGRK1uDpiwABAgQIECBAgMBOBGLlI3OCbc75wrKToHmZBAgQIECAAAECBJSPa5cjGSRAgAABAgQIECCwEwHlQ/nYSdS9TAIECBAgQIAAgWsLKB/Kx7Uz6PkJECBAgAABAgR2IqB8KB87ibqXSYAAAQIECBAgcG0B5UP5uHYGPT8BAgQIECBAgMBOBJQP5WMnUfcyCRAgQIAAAQIEri2gfCgf186g5ydAgAABAgQIENiJgPKhfOwk6l4mAQIECBAgQIDAtQWUj3n5+PqqvL+9Kx8/Pxv/Y8DPr8vN7evyfT7m0tvXToDnJ0CAAAECBAgQILCSwMrl42X5eHtXbo5+dT65j5QF5WOl2HkaAgQIECBAgACBPQqsVj5+fXpTbm7flC9fD1cVDqXg+4eHj5VIYeg51pWPPf6d8JoJECBAgAABAgQ6CaxTPtY4ie9RQtbY705vrGUJECBAgAABAgQIbE1ghfLxvHx5d1fef3o+3kfxt5JQT/hnH81696r8ms+ZCsF83GHMeHXlMHcx7/uHu3Lz4WUp83nLKzH1Y1ezKzHTc82f//DRrPbRsWHN+Z9Ht7eWCPtDgAABAgQIECBAoJNA//KxPKE/c3J+6qNZtTTMb/qeykM76Z/dR7J4bF54xnUOBeSwDw+eb7mvy/Kx/PP7sVjVUnPmdZ0tJ53eWMsSIECAAAECBAgQ2JrASuXjKTeVjyWi/aSpdjK/eHxZCO6flbFEHD9HfWx29aOWj9nvx0KwuCqzLBeL5xrWmBeausZizNmi0V7T7L6XrSXC/hAgQIAAAQIECBDoJLBS+Zh9lOnUCfjwWD3xPy4Q04n80Un/iZP9ZdEY5i0fq+WjXRn5c/J/9PjZ8nG4ynH0k7qmj4ed3u9p/89+7/TGWpYAAQIECBAgQIDA1gT6l4/7xZWL/3n5eHhl5k+ROVsyHnvdW0uE/SFAgAABAgQIECDQSWCF8vGs1KsLDz7ytDxpf6ykLB6/6JWPxdpnr3wcXseJqyep0jGVkU5vrGUJECBAgAABAgQIbE1glfJRphuz5zeOH06+5//Px3jvxvFHtB4Ul38pH7fH92zUtef79JfyMf2krKOrH19flY9P/UleU+GYf99aIuwPAQIECBAgQIAAgU4CK5WP8SrHWC6m+yQO3xdXEh6MWfz5WACO77FY3t8xXIlYPjbd2zEWjmkfjtcZ7zuZlZ8TRWcqIO1H7c7Ly7xUPHW70xtrWQIECBAgQIAAAQJbE1i1fPzTx5OeejL/yLipfFxzH04+99YSYX8IECBAgAABAgQIdBJQPh4pKyeLQo+xnd5YyxIgQIAAAQIECBDYmoDy0aNQRNbcWiLsDwECBAgQIECAAIFOAspHpCj0GNvpjbUsAQIECBAgQIAAga0J7KZ8rPYxqmhB2Voi7A8BAgQIECBAgACBTgLKR7QsXHp8pzfWsgQIECBAgAABAgS2JqB8XLpMRNfbWiLsDwECBAgQIECAAIFOAspHtCxcenynN9ayBAgQIECAAAECBLYmoHxcukxE19taIuwPAQIECBAgQIAAgU4CsfLx7UXZ7I3b0ZP+LYwfPH0RIECAAAECBAgQ2IlArHz8fKt8XLK0DJ6+CBAgQIAAAQIECOxEIFY+fv8oxdWPyxSwwXHw9EWAAAECBAgQIEBgJwKx8jGgDCfMw7/YKyG5EjK4DX6Kx07+inmZBAgQIECAAAECk0C8fEwzfSdAgAABAgQIECBAgEBAQPkIYBlKgAABAgQIECBAgEBeQPnI25lJgAABAgQIECBAgEBAQPkIYBlKgAABAgQIECBAgEBeQPnI25lJgAABAgQIECBAgEBAQPkIYBlKgAABAgQIECBAgEBeQPnI25lJgAABAgQIECBAgEBAQPkIYBlKgAABAgQIECBAgEBeQPnI25lJgAABAgQIECBAgEBAQPkIYBlKgAABAgQIECBAgEBeQPnI25lJgAABAgQIECBAgEBAQPkIYBlKgAABAgQIECBAgEBeQPnI25lJgAABAgQIECBAgEBAQPkIYBlKgAABAgQIECBAgEBeQPnI25lJgAABAgQIECBAgEBAQPkIYBlKgAABAgQIECBAgEBeQPnI25lJgAABAgQIECBAgEBAQPkIYBlKgAABAgQIECBAgEBeQPnI25lJgAABAgQIECBAgEBAQPkIYBlKgAABAgQIECBAgEBeQPnI25lJgAABAgQIECBAgEBAQPkIYBlKgAABAgQIECBAgEBeQPnI25lJgAABAgQIECBAgEBAQPkIYBlKgAABAgQIECBAgEBeQPnI25lJgAABAgQIECBAgEBAQPkIYBlKgAABAgQIECBAgEBeQPnI25lJgAABAgQIECBAgEBAQPkIYBlKgAABAgQIECBAgEBeQPnI25lJgAABAgQIECBAgEBAQPkIYBlKgAABAgQIECBAgEBeQPnI25lJgAABAgQIECBAgEBAQPkIYBlKgAABAgQIECBAgEBeQPnI25lJgAABAgQIECBAgEBAQPkIYBlKgAABAgQIECBAgEBeQPnI25lJgAABAgQIECBAgEBAQPkIYBlKgAABAgQIECBAgEBeQPnI25lJgAABAgQIECBAgEBAQPkIYBlKgAABAgQIECBAgEBeQPnI25lJgAABAgQIECBAgEBAQPkIYBlKgAABAgQIECBAgEBeQPnI25lJgAABAgQIECBAgEBAQPkIYBlKgAABAgQIECBAgEBeQPnI25lJgAABAgQIECBAgEBAQPkIYBlKgAABAgQIECBAgEBeQPnI25lJgAABAgQIECBAgEBAQPkIYBlKgAABAgQIECBAgEBeQPnI25lJgAABAgQIECBAgEBAQPkIYBlKgAABAgQIECBAgEBeQPnI25lJgAABAgQIECBAgEBAQPkIYBlKgAABAgQIECBAgEBeQPnI25lJgAABAgQIECBAgEBAQPkIYBlKgAABAgQIECBAgEBeQPnI25lJgAABAgQIECBAgEBAQPkIYBlKgAABAgQIECBAgEBeQPnI25lJgAABAgQIECBAgEBAQPkIYBlKgAABAgQIECBAgEBe4D+csV4Z7/7RzgAAAABJRU5ErkJggg=="></p><p><strong>In case you get the response in errors</strong></p><p><img src="data:image/png;base64,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"></p><p><br></p><p><strong style="color: rgb(0, 0, 0);">Create this assignment in the Projects module and once you are done, submit the URL of your project link on the right side textbox.</strong></p>',
// 		title: "Assignment - Compile Coding Question with API",
// 		tags: "",
// 		askconf: false,
// 		difficultyLevel: "0",
// 		executionTime: "15",
// 		createdBy: "5bc485d7ed577333b968bb75",
// 		parentIdOfCreator: "59f9c87bbace049edfca78cf",
// 		__v: 0,
// 		explanation: null,
// 		hint: null,
// 		showhint: false,
// 		updatedBy: "5bc485d7ed577333b968bb75",
// 		isPublic: true,
// 		referenceLinks: null,
// 		files: [],
// 		questionTypeWeb: null,
// 		questionTypeSubjective: {
// 			subjectiveanswer: "<p><br></p>",
// 			displayFileUpload: false,
// 			displayInputBox: true,
// 			isProjectType: true,
// 		},
// 		questionTypeCoding: null,
// 		questionTypeMCQ: null,
// 		importedby: [],
// 		displaystatus: 1,
// 		score: 10,
// 		questionTypeProject: null,
// 		tutorialLink: null,
// 		isCqDocument: true,
// 		activityIds: ["668292800627df14908417ec"],
// 		orgId: "5bc485d7ed577333b968bb75",
// 	},
// 	dtFrmt: {},
// };

// // type 4 (Coding)
// const data4: AttemptPreviewDataType = {
// 	html: '<style>\n    .tital{ font-size:16px; font-weight:500;}\n\t.bot-border{ border-bottom:1px #f8f8f8 solid;  margin:5px 0  5px 0}\n</style>\n<fieldset>\n\n    <div ><p><span style="color: rgb(0, 0, 0);">Given a compressed string, you need to expand it in its original form. For example, if </span><strong style="color: rgb(0, 0, 0);">a2b3c2de</strong><span style="color: rgb(0, 0, 0);"> is the given string, then it will become </span><strong style="color: rgb(0, 0, 0);">aabbbccde</strong><span style="color: rgb(0, 0, 0);"> after expansion.</span></p><p><span style="color: rgb(0, 0, 0);">The integer value after an alphabet, in the given string, denotes how many times that alphabet should occur in the final string, and if there is no integer after an alphabet then it will occur only once.</span></p><p><strong style="color: rgb(0, 0, 0);">Note: </strong><span style="color: rgb(0, 0, 0);">The maximum length of the expanded string will not be greater than 1000.</span></p><p><strong style="background-color: transparent; color: rgb(0, 0, 0);">Input Format:</strong></p><pre class="ql-syntax" spellcheck="false">The first line of input contains an <span class="hljs-built_in">integer</span> T denoting the no of <span class="hljs-built_in">test</span> cases. Then T <span class="hljs-built_in">test</span> cases follow.\r\nEach <span class="hljs-built_in">test</span> <span class="hljs-keyword">case</span> contains a string.\r\n</pre><p><strong style="background-color: transparent; color: rgb(0, 0, 0);">Output Format:</strong></p><pre class="ql-syntax" spellcheck="false"><span class="hljs-keyword">For</span> each test <span class="hljs-keyword">case</span>, <span class="hljs-keyword">print</span> the <span class="hljs-keyword">final</span> string after expansion, in a <span class="hljs-keyword">new</span> line.\r\n</pre><p><strong style="background-color: transparent; color: rgb(0, 0, 0);">Sample Input</strong></p><pre class="ql-syntax" spellcheck="false">2\r\ng2k2gd3g4\r\nabd7\r\n</pre><p><strong style="background-color: transparent; color: rgb(0, 0, 0);">Sample Output</strong></p><pre class="ql-syntax" spellcheck="false"><span class="hljs-attribute">ggkkgdddgggg</span>\r\nabddddddd\r\n</pre></div>\n    <div class="clearfix"></div>\n    <div class="bot-border"></div>\n\n\n\n</fieldset>',
// 	questiondata: {
// 		_id: "6075809d85988f2148a78139",
// 		updatedAt: "2025-01-22T11:07:46.954Z",
// 		createdAt: "2021-04-13T11:34:36.522Z",
// 		type: "4",
// 		text: '<p><span style="color: rgb(0, 0, 0);">Given a compressed string, you need to expand it in its original form. For example, if </span><strong style="color: rgb(0, 0, 0);">a2b3c2de</strong><span style="color: rgb(0, 0, 0);"> is the given string, then it will become </span><strong style="color: rgb(0, 0, 0);">aabbbccde</strong><span style="color: rgb(0, 0, 0);"> after expansion.</span></p><p><span style="color: rgb(0, 0, 0);">The integer value after an alphabet, in the given string, denotes how many times that alphabet should occur in the final string, and if there is no integer after an alphabet then it will occur only once.</span></p><p><strong style="color: rgb(0, 0, 0);">Note: </strong><span style="color: rgb(0, 0, 0);">The maximum length of the expanded string will not be greater than 1000.</span></p><p><strong style="background-color: transparent; color: rgb(0, 0, 0);">Input Format:</strong></p><pre class="ql-syntax" spellcheck="false">The first line of input contains an <span class="hljs-built_in">integer</span> T denoting the no of <span class="hljs-built_in">test</span> cases. Then T <span class="hljs-built_in">test</span> cases follow.\r\nEach <span class="hljs-built_in">test</span> <span class="hljs-keyword">case</span> contains a string.\r\n</pre><p><strong style="background-color: transparent; color: rgb(0, 0, 0);">Output Format:</strong></p><pre class="ql-syntax" spellcheck="false"><span class="hljs-keyword">For</span> each test <span class="hljs-keyword">case</span>, <span class="hljs-keyword">print</span> the <span class="hljs-keyword">final</span> string after expansion, in a <span class="hljs-keyword">new</span> line.\r\n</pre><p><strong style="background-color: transparent; color: rgb(0, 0, 0);">Sample Input</strong></p><pre class="ql-syntax" spellcheck="false">2\r\ng2k2gd3g4\r\nabd7\r\n</pre><p><strong style="background-color: transparent; color: rgb(0, 0, 0);">Sample Output</strong></p><pre class="ql-syntax" spellcheck="false"><span class="hljs-attribute">ggkkgdddgggg</span>\r\nabddddddd\r\n</pre>',
// 		title: "Expand a string",
// 		tags: "strings",
// 		tutorialLink:
// 			"https://codequotient.com/tutorial/preview/608a52a2e69558b9af4513ec",
// 		isCqDocument: true,
// 		askconf: false,
// 		difficultyLevel: "1",
// 		executionTime: "15",
// 		createdBy: "5a4de5b805f73615301195b3",
// 		parentIdOfCreator: "59f9c87bbace049edfca78cf",
// 		isPublic: true,
// 		referenceLinks: [""],
// 		files: [],
// 		questionTypeWeb: null,
// 		questionTypeCoding: {
// 			multipleTestCases: false,
// 			testCase: [
// 				{
// 					sampleTest: true,
// 					attemptInMultiLine: false,
// 					codeprogexpectedoutput: "ggkkgdddgggg\nabddddddd",
// 					codeproginputparams: "2\ng2k2gd3g4\nabd7",
// 					_id: "609a78888942ac978ee511e4",
// 					scoreip: 0,
// 				},
// 				{
// 					sampleTest: false,
// 					attemptInMultiLine: false,
// 					codeprogexpectedoutput:
// 						"aaaaabbbbbbcccccdddddefghi\nabcdefgh\naaaabbbbbbbbbbbbbbbbbbbccccccddeeeeeeegggg\naaaaeeeeeeeiiiiiiioooooouuuu\nmmmmmmmmmnnnnnnnnnnnoooooooooooppppqqqqqqqqqrrrrrr",
// 					codeproginputparams:
// 						"5\na5b6c5d5efghi\nabcdefgh\na4b19c6d2e7g4\na4e7i7o6u4\nm9n11o11p4q9r6",
// 					_id: "609a78888942ac978ee511e3",
// 					scoreip: 2,
// 				},
// 				{
// 					sampleTest: false,
// 					attemptInMultiLine: false,
// 					codeprogexpectedoutput:
// 						"ccccccdddddddoossssssssttttttu\niiiijjjkkklllmmmnnnooo\na\nghijklllllllll\nabcdefghijklmnopqrstuvwxyz",
// 					codeproginputparams:
// 						"5\nc6d7o2s8t6u\ni4j3k3l3m3n3o3\na\nghijkl9\nabcdefghijklmnopqrstuvwxyz",
// 					_id: "609a78888942ac978ee511e2",
// 					scoreip: 2,
// 				},
// 				{
// 					sampleTest: false,
// 					attemptInMultiLine: false,
// 					codeprogexpectedoutput:
// 						"aabbccdef\nabddddddd\na\nabcdefg\nmnoppppppqqqqqqt\nzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzaaaaaaaaayyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyybbxxxcccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccwwwwwwwwwwwwwwwwwwwwwwwdddddvvvvvveeeeeeeeeeeffffffffffffutghiiiiiiiiiiiiiiiiijjjjjjjjjjjjjjjjjjkkkkkkkkkkkkkkkkkkkksrqpppppppppplmmmmmmmmmmmmmmmnooooooooooo",
// 					codeproginputparams:
// 						"6\na2b2c2def\nabd7\na\nabcdefg\nmnop6q6t\nz57a9y111b2x3c79w23d5v6e11f12utghi17j18k20srqp10lm15no11",
// 					_id: "609a78888942ac978ee511e1",
// 					scoreip: 2,
// 				},
// 				{
// 					sampleTest: false,
// 					attemptInMultiLine: false,
// 					codeprogexpectedoutput:
// 						"abcdfegihkjlmnopqrstuvwxyz\nd\nkkdgjjdguyggggggghhhhhoooooppptgevfgasfgdgshaisdhweihwwwwwwiiiiiiwhfikghfwghfwjfwlj\nhhhhhhhhhfffffffffffeeeeeeeeeeeewwwwwwwwwwwwwwqqqqqqqqqqqqjkllkjkhjgthjfdsxxxxxxcdxffzzzzzzzuuuuuuuuuuuttttttttyyyyyyyyyyyhfgdfgsfdhf\ntttttttttttttterreeeeeeeeeeeeeeeetdtrdxfdhhhhhhhhhffgsfsdfhghjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjfhfhgfyryrfdtdffypoigugftydrdreqerdtfygu\naaaaaaabbbbbbbbcccccccccccdddddddddeeeeeeeeeefffffffffffffffffffffffffffffffeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeegggggggggggggeeeeeeeeeefffffffffffffffffffffffffffffffeeeeeeeeeedddddddddcccccccccccbbbbbbbbaaaaaaa",
// 					codeproginputparams:
// 						"6\nabcdfegihkjlmnopqrstuvwxyz\nd\nk2dgj2dguyg7h5o5p3tgevfgasfgdgshaisdhweihw6i6whfikghfwghfwjfwlj\nh9f11e12w14q12jkl2kjkhjgthjfdsx6cdxf2z7u11t8y11hfgdfgsfdhf\nt14er2e16tdtrdxfdh9f2gsfsdfhghj33fhfhgfyryrfdtdf2ypoigugftydrdreqerdtfygu\na7b8c11d9e10f31e100g13e10f31e10d9c11b8a7",
// 					_id: "609a78888942ac978ee511e0",
// 					scoreip: 2,
// 				},
// 				{
// 					sampleTest: false,
// 					attemptInMultiLine: false,
// 					codeprogexpectedoutput:
// 						"fyhfhyfytfytfyfyfh\nqwiuweqgbdcdvbjvdfghjvdshjkeioopdfkhdhjvdgdfvfgbjgjnuyhijuyoipeqwdfser\nttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttttyyyyyyyyyyfhfhgjjjjjjjj\nuuuuuuuuuuuuuttttttttttttttttttfhfhhj\nllllllllllllllljhjggggggggggggdfgsfs",
// 					codeproginputparams:
// 						"5\nfyhfhyfytfytfyfyfh\nqwiuweqgbdcdvbjvdfghjvdshjkeio2pdfkhdhjvdgdfvfgbjgjnuyhijuyoipeqwdfser\nt748y10fhfhgj8\nu13t18fhfh2j\nl15jhjg12dfgsfs",
// 					_id: "62372783c1dacd698028cd5c",
// 					scoreip: 2,
// 				},
// 			],
// 			codeproglang: [
// 				{
// 					_id: "6290ba68eca5cab07099958d",
// 					language: "7",
// 					defaultTrimmedCode:
// 						'#include <stdio.h>\n#include <string.h>\n#include <stdlib.h>\n# define MAX_LEN 1000\n// Put the final string after expansion in \'res\'\nvoid expandString(char *str, char *res) {\n  // Write your code here\n}\nint main() {\n    int t;\n    scanf("%d", &t);\n    while (t--) {\n      char str[100];\n      scanf("%s", str);\n      char res[MAX_LEN];\n      expandString(str, res);\n      printf("%s\\n", res);\n    }\n    return 0;\n}',
// 					executionTimePerTestCaseInSec: 1,
// 					codeComponents: {
// 						head: "#include <stdio.h>\n#include <string.h>\n#include <stdlib.h>\n\n# define MAX_LEN 1000\n",
// 						body: "// Put the final string after expansion in 'res'\nvoid expandString(char *str, char *res) {\n  // Write your code here\n  \n}",
// 						tail: 'int main() {\n    int t;\n    scanf("%d", &t);\n    while (t--) {\n      char str[100];\n      scanf("%s", str);\n      char res[MAX_LEN];\n      expandString(str, res);\n      printf("%s\\n", res);\n    }\n    \n    return 0;\n}',
// 						solution:
// 							"void expandString(char *str, char *res) {\n    int n = strlen(str);\n    int i = 0, j = 0;\n    for (i = 0; i < n; i++) {\n        char c = str[i];\n        int count = 1;\n        char temp[50];\n        int k = 0;\n        while (i + 1 < n && str[i + 1] >= '0' && str[i+1] <= '9') {\n            temp[k] = str[i + 1];\n            k++;\n            i++;\n        }\n        temp[k] = '\\0';\n        if (strlen(temp) > 0)\n            count = atoi(temp);\n        while (count--) {\n            res[j] = c;\n            j++;\n        }\n    }\n    res[j] = '\\0';\n}",
// 					},
// 					defaultCompileResult: {
// 						errors: "",
// 						output: "\nOUTPUT:\n\u0004��T�\n\u0004��T�\n\nOUTPUT:\n���9�\n���9�\n���9�\n���9�\n���9�\n\nOUTPUT:\nt\nt\nt\nt\nt\n\nOUTPUT:\nd\u0006\u0005u�\nd\u0006\u0005u�\nd\u0006\u0005u�\nd\u0006\u0005u�\nd\u0006\u0005u�\nd\u0006\u0005u�\n\nOUTPUT:\n4��5�\n4��5�\n4��5�\n4��5�\n4��5�\n4��5�\n\nOUTPUT:\nDb���\nDb���\nDb���\nDb���\nDb���\n",
// 					},
// 				},
// 				{
// 					_id: "6290ba68eca5cab07099958c",
// 					language: "77",
// 					defaultTrimmedCode:
// 						"#include <bits/stdc++.h>\nusing namespace std;\n// Return the final string after expansion\nstring expandString(string str) {\n  // Write your code here\n}\nint main() {\n  int t;\n  cin>>t;\n  while(t--){\n    string str;\n    cin>>str;\n    string expanded = expandString(str);\n    cout<<expanded<<endl;\n  }\n  return 0;\n}",
// 					executionTimePerTestCaseInSec: 1,
// 					codeComponents: {
// 						head: "#include <bits/stdc++.h>\nusing namespace std;",
// 						body: "// Return the final string after expansion\nstring expandString(string str) {\n  // Write your code here\n  \n}",
// 						tail: "\nint main() {\n  int t;\n  cin>>t;\n  while(t--){\n    string str;\n    cin>>str;\n    string expanded = expandString(str);\n    cout<<expanded<<endl;\n  }\n  return 0;\n}",
// 						solution:
// 							"string expandString(string str) {\n    string res;\n    int n = str.length();\n    for (int i = 0; i < n; i++) {\n        char c = str[i];\n        int count = 1;\n        string temp;\n        \n        while (i + 1 < n && str[i + 1] >= '0' && str[i+1] <= '9')\n            temp.push_back(str[++i]);\n\n        if (temp.length() > 0)\n          \tsscanf(temp.c_str(), \"%d\", &count);\n\n        while (count--)\n            res.push_back(c);\n    }\n    return res;\n}\n",
// 					},
// 					defaultCompileResult: {
// 						errors: '  File "/usercode/file.py3", line 5\n    def main():\n      ^\nIndentationError: expected an indented block\n  File "/usercode/file.py3", line 5\n    def main():\n      ^\nIndentationError: expected an indented block\n  File "/usercode/file.py3", line 5\n    def main():\n      ^\nIndentationError: expected an indented block\n  File "/usercode/file.py3", line 5\n    def main():\n      ^\nIndentationError: expected an indented block\n  File "/usercode/file.py3", line 5\n    def main():\n      ^\nIndentationError: expected an indented block\n  File "/usercode/file.py3", line 5\n    def main():\n      ^\nIndentationError: expected an indented block\n',
// 						output: "",
// 					},
// 				},
// 				{
// 					_id: "6290ba68eca5cab07099958b",
// 					language: "8",
// 					defaultTrimmedCode:
// 						"import java.util.*;\nclass Result {\n  // Return the final string after expansion\n  static String expandString(String str) {\n    // Write your code here\n  }\n}\nclass Main{\n    public static void main(String[] args){\n        Scanner sc = new Scanner(System.in);\n        int T = Integer.parseInt(sc.nextLine().trim());\n        while(T-- > 0){\n            String str = sc.next();\n            String expandedString = Result.expandString(str);\n            System.out.println(expandedString);\n        }\n    }\n}",
// 					executionTimePerTestCaseInSec: 3,
// 					codeComponents: {
// 						head: "import java.util.*;\n",
// 						body: "class Result {\n  // Return the final string after expansion\n  static String expandString(String str) {\n    // Write your code here\n    \n  }\n}",
// 						tail: "class Main{\n    public static void main(String[] args){\n        Scanner sc = new Scanner(System.in);\n        int T = Integer.parseInt(sc.nextLine().trim());\n\n        while(T-- > 0){\n            String str = sc.next();\n\n            String expandedString = Result.expandString(str);\n            System.out.println(expandedString);\n        }\n    }\n}",
// 						solution:
// 							"class Result {\n    static String expandString(String str) {\n        int n = str.length();\n        String res = \"\";\n        for (int i = 0; i < n; i++) {\n            char c = str.charAt(i);\n            int count = 1;\n            String temp = \"\";\n            while (i + 1 < n && str.charAt(i+1) >= '0' && str.charAt(i+1) <= '9') {\n                temp += str.charAt(i+1);\n                i++;\n            }\n            if (temp.length() > 0)\n                count = Integer.parseInt(temp);\n            while (count != 0) {\n                res += c;\n                count--;\n            }\n        }\n        return res;\n    }\n}\n\n",
// 					},
// 					defaultCompileResult: {
// 						errors: "",
// 						output: "\nOUTPUT:\nundefined\nundefined\n\nOUTPUT:\nundefined\nundefined\nundefined\nundefined\nundefined\n\nOUTPUT:\nundefined\nundefined\nundefined\nundefined\nundefined\n\nOUTPUT:\nundefined\nundefined\nundefined\nundefined\nundefined\nundefined\n\nOUTPUT:\nundefined\nundefined\nundefined\nundefined\nundefined\nundefined\n\nOUTPUT:\nundefined\nundefined\nundefined\nundefined\nundefined\n",
// 					},
// 				},
// 				{
// 					_id: "6290ba68eca5cab07099958a",
// 					language: "4",
// 					defaultTrimmedCode:
// 						"'use strict';\nprocess.stdin.resume();\nprocess.stdin.setEncoding('utf-8');\nlet inputString = '';\nlet currentLine = 0;\nprocess.stdin.on('data', inputStdin => {\n    inputString += inputStdin;\n});\nprocess.stdin.on('end', _ => {\n    inputString = inputString.trim().split('\\n').map(string => {\n        return string.trim();\n    });\n    main();    \n});\nfunction readLine() {\n    return inputString[currentLine++];\n}\n// Return the expanded string\nfunction expandString(str) {\n  // Write your code here\n}\nfunction main() {\n  let T = +(readLine());\n  while(T--) {\n    let str = readLine();\n  \tconsole.log(expandString(str));\n  }\n}",
// 					executionTimePerTestCaseInSec: 5,
// 					codeComponents: {
// 						head: "'use strict';\n\nprocess.stdin.resume();\nprocess.stdin.setEncoding('utf-8');\n\nlet inputString = '';\nlet currentLine = 0;\n\nprocess.stdin.on('data', inputStdin => {\n    inputString += inputStdin;\n});\n\nprocess.stdin.on('end', _ => {\n    inputString = inputString.trim().split('\\n').map(string => {\n        return string.trim();\n    });\n    \n    main();    \n});\n\nfunction readLine() {\n    return inputString[currentLine++];\n}",
// 						body: "// Return the expanded string\nfunction expandString(str) {\n  // Write your code here\n  \n}",
// 						tail: "function main() {\n  let T = +(readLine());\n  while(T--) {\n    let str = readLine();\n  \tconsole.log(expandString(str));\n  }\n}",
// 						solution:
// 							"function isDigit(ch){\n  if(ch >='0' && ch<='9'){\n    return true;\n  }\n  return false\n}\n\n// Return the expanded string\nfunction expandString(str){\n  // Write your code here\n  let ch;\n  let output = '';\n  for(let i = 0;i<str.length;i++){\n    if(isDigit(str.charAt(i))){\n      let temp = '';\n      while(isDigit(str.charAt(i))){\n        temp+=str.charAt(i);\n        i++;\n      }\n      temp = +(temp)-1;\n      while(temp--){\n        output+=ch;\n      }\n      i--;\n    }else{\n      ch = str.charAt(i);\n      output+=ch;\n    }\n  }\n  return output;\n}",
// 					},
// 					defaultCompileResult: {
// 						errors: "/usercode/file.cs(10,24): error CS0161: `Result.expandString(string)': not all code paths return a value\n",
// 						output: "",
// 					},
// 				},
// 				{
// 					_id: "6290ba68eca5cab070999589",
// 					language: "20",
// 					defaultTrimmedCode:
// 						'# Return the expanded string\ndef expandString(Str):\n  # Write your code here\ndef main():\n  t = int(input().strip());\n  for i in range(t):\n    string = input().strip();\n    expanded = expandString(string);\n    print(expanded);\nif __name__=="__main__":\n  main();',
// 					executionTimePerTestCaseInSec: 2,
// 					codeComponents: {
// 						head: "",
// 						body: "# Return the expanded string\ndef expandString(Str):\n  # Write your code here",
// 						tail: 'def main():\n  t = int(input().strip());\n  for i in range(t):\n    string = input().strip();\n    expanded = expandString(string);\n    print(expanded);\n\nif __name__=="__main__":\n  main();',
// 						solution:
// 							"def isDigit(ch):\n  return ch.isdigit();\n# Return the expanded string\ndef expandString(Str):\n  # Write your code here\n  ch='';\n  output = '';\n  n = len(Str);\n  i=0;\n  while(i<len(Str)):\n    if(isDigit(Str[i])):\n      temp = '';\n      while(i<len(Str) and isDigit(Str[i])):\n        temp+=Str[i];\n        i+=1;\n      temp = int(temp)-1;\n      count = [ch]*temp;\n      output += ''.join(count);\n      i-=1;\n    else:\n      ch = Str[i];\n      output+=ch;\n    i+=1;\n  return output;",
// 					},
// 					defaultCompileResult: {
// 						errors: "/usercode/Main.java:8: error: missing return statement\n  }\n  ^\n1 error\n",
// 						output: "",
// 					},
// 				},
// 				{
// 					_id: "6290ba68eca5cab070999588",
// 					language: "10",
// 					defaultTrimmedCode:
// 						"using System;\nusing System.IO;\nusing System.Linq;\nusing System.Text;\nusing System.Collections;\nusing System.Collections.Generic;\npublic class Result {\n  // Return the final string after expansion\n  public static String expandString(String textMsg) {\n    // Write your code here\n  }\n}\npublic class program {\n  public static void Main(String[] args) {\n    int t;\n    t = int.Parse(Console.ReadLine().Trim());\n    String str;\n    while (t-- > 0) {\n      str = Console.ReadLine();\n      Console.WriteLine(Result.expandString(str));\n    }\n  }\n}",
// 					executionTimePerTestCaseInSec: 3,
// 					codeComponents: {
// 						head: "using System;\nusing System.IO;\nusing System.Linq;\nusing System.Text;\nusing System.Collections;\nusing System.Collections.Generic;\n",
// 						body: "public class Result {\n  // Return the final string after expansion\n  public static String expandString(String textMsg) {\n    // Write your code here\n\t\n  }\n}",
// 						tail: "public class program {\n  public static void Main(String[] args) {\n    int t;\n    t = int.Parse(Console.ReadLine().Trim());\n    String str;\n    while (t-- > 0) {\n      str = Console.ReadLine();\n      Console.WriteLine(Result.expandString(str));\n    }\n  }\n}",
// 						solution:
// 							"public class Result {\n  // Return the final string after expansion\n  public static String expandString(String str) {\n    // Write your code here\n    int n = str.Length;\n    String res = \"\";\n    for (int i = 0; i < n; i++) {\n      char c = str[i];\n      int count = 1;\n      String temp = \"\";\n      while (i + 1 < n && str[i+1] >= '0' && str[i+1] <= '9') {\n        temp += str[i+1];\n        i++;\n      }\n      if (temp.Length > 0)\n        count = int.Parse(temp);\n      while (count != 0) {\n        res += c;\n        count--;\n      }\n    }\n    return res;\n  }\n}",
// 					},
// 					defaultCompileResult: {
// 						errors: "timeout: the monitored command dumped core\n/usercode/script.sh: line 108:    15 Segmentation fault      timeout $5 $output $commadLineParameters < /usercode/inputFile$c\ntimeout: the monitored command dumped core\n/usercode/script.sh: line 108:    17 Segmentation fault      timeout $5 $output $commadLineParameters < /usercode/inputFile$c\ntimeout: the monitored command dumped core\n/usercode/script.sh: line 108:    19 Segmentation fault      timeout $5 $output $commadLineParameters < /usercode/inputFile$c\ntimeout: the monitored command dumped core\n/usercode/script.sh: line 108:    21 Segmentation fault      timeout $5 $output $commadLineParameters < /usercode/inputFile$c\ntimeout: the monitored command dumped core\n/usercode/script.sh: line 108:    23 Segmentation fault      timeout $5 $output $commadLineParameters < /usercode/inputFile$c\ntimeout: the monitored command dumped core\n/usercode/script.sh: line 108:    25 Segmentation fault      timeout $5 $output $commadLineParameters < /usercode/inputFile$c\n",
// 						output: "",
// 					},
// 				},
// 			],
// 		},
// 		questionTypeMCQ: null,
// 		importedby: [],
// 		displaystatus: 1,
// 		score: 10,
// 		__v: 0,
// 		explanation: null,
// 		hint: null,
// 		questionTypeProject: null,
// 		questionTypeSubjective: null,
// 		showhint: false,
// 		updatedBy: "5a4de5b805f73615301195b3",
// 		activityIds: ["6290ba68eca5cab070999587"],
// 		orgId: "59f9c87bbace049edfca78cf",
// 		minCompletionTimeInSec: 0,
// 	},
// 	dtFrmt: {},
// };

// // type 5(MQ)
// const data5: AttemptPreviewDataType = {
// 	html: '<style>\n    .tital{ font-size:16px; font-weight:500;}\n\t.bot-border{ border-bottom:1px #f8f8f8 solid;  margin:5px 0  5px 0}\n</style>\n<fieldset>\n\n    <div ><p>Use your AWS management console to update/add the meta-data on an S3 object inside your S3 bucket.</p><p><br></p><p><strong>Notes:</strong></p><ul><li>Paste your S3 bucket name, and S3 object key, meta-data key and meta-data value in the answer box. Multiple input items must be separated by a comma(,).</li><li>Make sure you provision all resources in Mumbai (ap-south-1) region.</li></ul><p><br></p><p><strong>Input Format: </strong>&lt;bucket name&gt;, &lt;object key&gt;, &lt;meta-data key&gt;, &lt;meta-data value&gt;</p><p><strong>Example: </strong>example-bucket, bucket-root/folder-1/file.ext, example-md-key, example-md-value</p></div>\n    <div class="clearfix"></div>\n    <div class="bot-border"></div>\n\n\n\n</fieldset>',
// 	questiondata: {
// 		_id: "5f9bcfa8adf2262eaebf79dd",
// 		updatedAt: "2025-02-07T09:42:30.445Z",
// 		createdAt: "2020-11-11T08:10:41.708Z",
// 		type: "5",
// 		text: "<p>Use your AWS management console to update/add the meta-data on an S3 object inside your S3 bucket.</p><p><br></p><p><strong>Notes:</strong></p><ul><li>Paste your S3 bucket name, and S3 object key, meta-data key and meta-data value in the answer box. Multiple input items must be separated by a comma(,).</li><li>Make sure you provision all resources in Mumbai (ap-south-1) region.</li></ul><p><br></p><p><strong>Input Format: </strong>&lt;bucket name&gt;, &lt;object key&gt;, &lt;meta-data key&gt;, &lt;meta-data value&gt;</p><p><strong>Example: </strong>example-bucket, bucket-root/folder-1/file.ext, example-md-key, example-md-value</p>",
// 		title: "Add Meta-data on S3 Object",
// 		tags: "aws,s3",
// 		tutorialLink: "",
// 		isCqDocument: true,
// 		askconf: false,
// 		difficultyLevel: "0",
// 		executionTime: "15",
// 		funcType: 1,
// 		funcName: "checkS3BucketObjectMetadata",
// 		createdBy: "59f9c87bbace049edfca78cf",
// 		__v: 0,
// 		explanation: null,
// 		hint: null,
// 		showhint: false,
// 		updatedBy: "59f9c87bbace049edfca78cf",
// 		parentIdOfCreator: "59f9c87bbace049edfca78cf",
// 		isPublic: false,
// 		referenceLinks: [""],
// 		files: [],
// 		questionTypeWeb: null,
// 		questionTypeCoding: {
// 			multipleTestCases: false,
// 			encryptedOutput: false,
// 			isTraceTable: false,
// 			testCase: [
// 				{
// 					sampleTest: false,
// 					attemptInMultiLine: false,
// 					codeprogexpectedoutput: "A1",
// 					codeproginputparams: "Q1",
// 					_id: "67a5d5869d1f4858b76f0913",
// 					scoreip: 0,
// 				},
// 				{
// 					sampleTest: false,
// 					attemptInMultiLine: false,
// 					codeprogexpectedoutput: "A2",
// 					codeproginputparams: "Q2",
// 					_id: "67a5d5869d1f4858b76f0912",
// 					scoreip: null,
// 				},
// 			],
// 			codeproglang: [],
// 		},
// 		questionTypeMCQ: null,
// 		importedby: [],
// 		displaystatus: 1,
// 		score: 0,
// 		questionTypeProject: null,
// 		questionTypeSubjective: null,
// 		orgId: "59f9c87bbace049edfca78cf",
// 		activityIds: ["67a5d5869d1f4858b76f0911"],
// 		contentUrl: null,
// 	},
// 	dtFrmt: {},
// };

// type 9(Web)
const data9: AttemptPreviewDataType = {
	html: '<style>\n    .tital{ font-size:16px; font-weight:500;}\n\t.bot-border{ border-bottom:1px #f8f8f8 solid;  margin:5px 0  5px 0}\n</style>\n<fieldset>\n\n    <div ><p><span style="color: rgb(0, 0, 0); background-color: transparent;">The HTML &lt;span&gt; element is a generic inline container for phrasing content, which does not inherently represent anything. It can be used to group elements for styling purposes (using the </span><a href="https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes#attr-class" target="_blank" style="color: rgb(0, 0, 0); background-color: transparent;">class</a><span style="color: rgb(0, 0, 0); background-color: transparent;"> or </span><a href="https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes#attr-id" target="_blank" style="color: rgb(0, 0, 0); background-color: transparent;">id</a><span style="color: rgb(0, 0, 0); background-color: transparent;"> attributes), or because they share attribute values, such as </span><a href="https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes#attr-lang" target="_blank" style="color: rgb(0, 0, 0); background-color: transparent;">lang</a><span style="color: rgb(0, 0, 0); background-color: transparent;">. It should be used only when no other semantic element is appropriate.</span></p><p><strong style="color: rgb(0, 0, 0); background-color: transparent;">Example:</strong></p><pre class="ql-syntax" spellcheck="false"><span class="hljs-tag">&lt;<span class="hljs-name">p</span>&gt;</span>Add the <span class="hljs-tag">&lt;<span class="hljs-name">span</span> <span class="hljs-attr">class</span>=<span class="hljs-string">"ingredient"</span>&gt;</span>basil<span class="hljs-tag">&lt;/<span class="hljs-name">span</span>&gt;</span>, <span class="hljs-tag">&lt;<span class="hljs-name">span</span> <span class="hljs-attr">class</span>=<span class="hljs-string">"ingredient"</span>&gt;</span>pine nuts<span class="hljs-tag">&lt;/<span class="hljs-name">span</span>&gt;</span> and <span class="hljs-tag">&lt;<span class="hljs-name">span</span> <span class="hljs-attr">class</span>=<span class="hljs-string">"ingredient"</span>&gt;</span>garlic<span class="hljs-tag">&lt;/<span class="hljs-name">span</span>&gt;</span> to a blender and blend into a paste.<span class="hljs-tag">&lt;/<span class="hljs-name">p</span>&gt;</span>\r\n</pre><p><br></p><p><br></p><p><strong>Task 1: </strong>Create a &lt;p&gt; element.</p><p><strong>Task 2: </strong>&lt;p&gt; element must contain at least two &lt;span&gt; elements.</p><p><strong>Task 3: </strong>every &lt;span&gt; element must contain some text.</p><p><strong>Task 4: </strong>every &lt;span&gt; element must have a class attribute set to \'my-span\'.</p><p><br></p></div>\n    <div class="clearfix"></div>\n    <div class="bot-border"></div>\n\n\n\n</fieldset>',
	questiondata: {
		_id: "5f91744780bdd481565bbcf9",
		updatedAt: "2023-03-23T06:35:00.101Z",
		createdAt: "2020-11-09T09:08:00.662Z",
		type: "9",
		text: '<p><span style="color: rgb(0, 0, 0); background-color: transparent;">The HTML &lt;span&gt; element is a generic inline container for phrasing content, which does not inherently represent anything. It can be used to group elements for styling purposes (using the </span><a href="https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes#attr-class" target="_blank" style="color: rgb(0, 0, 0); background-color: transparent;">class</a><span style="color: rgb(0, 0, 0); background-color: transparent;"> or </span><a href="https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes#attr-id" target="_blank" style="color: rgb(0, 0, 0); background-color: transparent;">id</a><span style="color: rgb(0, 0, 0); background-color: transparent;"> attributes), or because they share attribute values, such as </span><a href="https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes#attr-lang" target="_blank" style="color: rgb(0, 0, 0); background-color: transparent;">lang</a><span style="color: rgb(0, 0, 0); background-color: transparent;">. It should be used only when no other semantic element is appropriate.</span></p><p><strong style="color: rgb(0, 0, 0); background-color: transparent;">Example:</strong></p><pre class="ql-syntax" spellcheck="false"><span class="hljs-tag">&lt;<span class="hljs-name">p</span>&gt;</span>Add the <span class="hljs-tag">&lt;<span class="hljs-name">span</span> <span class="hljs-attr">class</span>=<span class="hljs-string">"ingredient"</span>&gt;</span>basil<span class="hljs-tag">&lt;/<span class="hljs-name">span</span>&gt;</span>, <span class="hljs-tag">&lt;<span class="hljs-name">span</span> <span class="hljs-attr">class</span>=<span class="hljs-string">"ingredient"</span>&gt;</span>pine nuts<span class="hljs-tag">&lt;/<span class="hljs-name">span</span>&gt;</span> and <span class="hljs-tag">&lt;<span class="hljs-name">span</span> <span class="hljs-attr">class</span>=<span class="hljs-string">"ingredient"</span>&gt;</span>garlic<span class="hljs-tag">&lt;/<span class="hljs-name">span</span>&gt;</span> to a blender and blend into a paste.<span class="hljs-tag">&lt;/<span class="hljs-name">p</span>&gt;</span>\r\n</pre><p><br></p><p><br></p><p><strong>Task 1: </strong>Create a &lt;p&gt; element.</p><p><strong>Task 2: </strong>&lt;p&gt; element must contain at least two &lt;span&gt; elements.</p><p><strong>Task 3: </strong>every &lt;span&gt; element must contain some text.</p><p><strong>Task 4: </strong>every &lt;span&gt; element must have a class attribute set to \'my-span\'.</p><p><br></p>',
		title: "Span Tag",
		tags: "web",
		askconf: false,
		difficultyLevel: "0",
		executionTime: "15",
		createdBy: "5f2bfc1a9c802d07bec74fca",
		parentIdOfCreator: "5bc485d7ed577333b968bb75",
		__v: 0,
		explanation: null,
		hint: null,
		showhint: false,
		tutorialLink: null,
		updatedBy: "5bc485d7ed577333b968bb75",
		isPublic: true,
		referenceLinks: null,
		files: [],
		questionTypeWeb: {
			js: "// Insert JS Here!",
			css: "/* Insert CSS Here! */",
			html: "<!-- Insert HTML Body Content Here! -->",
			testCase: [
				{
					evaluator:
						"(function () {\n  return !!previewFrameDocument.querySelector('p');\n})()",
					description: "Create a <p> element.",
					_id: "5f922bb280bdd481565bbd19",
					scoreip: 100,
				},
				{
					evaluator:
						"(function () {\n  return previewFrameDocument.querySelectorAll('p > span').length >= 2;\n})()",
					description:
						"<p> element must contain at least two <span> elements.",
					_id: "5f922bb280bdd481565bbd18",
					scoreip: 25,
				},
				{
					evaluator:
						"(function () {\n  const spanElements = Array.from(previewFrameDocument.querySelectorAll('p > span'));\n  if (spanElements.length < 2) {\n    return false;\n  }\n\n  let result = true;\n  spanElements.forEach((el) => {\n    if (!el.innerText.length) {\n      result = false;\n    }\n  });\n  return result;\n})()",
					description: "every <span> element must contain some text.",
					_id: "5f922bb280bdd481565bbd17",
					scoreip: 25,
				},
				{
					evaluator:
						"(function () {\n  const spanElements = Array.from(previewFrameDocument.querySelectorAll('p > span'));\n  if (spanElements.length < 2) {\n    return false;\n  }\n\n  let result = true;\n  spanElements.forEach((el) => {\n    if (el.className !== 'my-span') {\n      result = false;\n    }\n  });\n  return result;\n})()",
					description:
						"every <span> element must have a class attribute set to 'my-span'.",
					_id: "5f922bb280bdd481565bbd16",
					scoreip: 25,
				},
			],
		},
		questionTypeCoding: {
			testCase: [],
			codeproglang: [],
			multipleTestCases: false,
		},
		questionTypeMCQ: {
			correctAnswers: [],
			options: [],
		},
		importedby: [],
		displaystatus: 1,
		score: 100,
		orgId: "5bc485d7ed577333b968bb75",
	},
	dtFrmt: {},
};

const AttemptPreview = () => {
	const [previewData, setPreviewData] = useState<AttemptPreviewDataType>();

	useEffect(() => {
		setPreviewData(data9);
	}, []);

	const questionType = previewData?.questiondata.type;
	const sanitizedValue = previewData?.questiondata.text;
	return (
		<div>
			<Flex vertical style={{ maxWidth: "600px", padding: "10px" }}>
				<Title level={4}>{previewData?.questiondata.title}</Title>

				{questionType === "4" &&
					previewData?.questiondata.questionTypeCoding && (
						<Text>
							Test Cases :{" "}
							{
								previewData?.questiondata.questionTypeCoding
									.testCase.length
							}
						</Text>
					)}
				{questionType === "5" &&
					previewData?.questiondata.questionTypeCoding && (
						<Text>
							Test Cases :{" "}
							{
								previewData?.questiondata.questionTypeCoding
									.testCase.length
							}
						</Text>
					)}
				{questionType === "9" &&
					previewData?.questiondata.questionTypeWeb && (
						<Text>
							Test Cases :{" "}
							{
								previewData?.questiondata.questionTypeWeb
									.testCase.length
							}
						</Text>
					)}

				<Divider />
				<ReactQuill
					theme="snow"
					value={sanitizedValue}
					readOnly
					modules={{ toolbar: false }}
				/>
				<Divider />
				{previewData?.questiondata.type === "5" &&
					previewData?.questiondata.questionTypeCoding?.testCase.map(
						(item: ItemType, index: number) => {
							return (
								<div key={index}>
									{index + 1}. {item.codeproginputparams} ={" "}
									<input
										disabled
										type="text"
										style={{ marginBottom: "10px" }}
									/>
								</div>
							);
						}
					)}
				{previewData?.questiondata.type === "1" && (
					<Radio.Group
						style={{
							display: "flex",
							flexDirection: "column",
							gap: 8,
						}}
						options={previewData?.questiondata.questionTypeMCQ?.options.map(
							(item: string, index: number) => {
								return { value: index, label: item };
							}
						)}
					/>
				)}
			</Flex>
		</div>
	);
};

export default AttemptPreview;
