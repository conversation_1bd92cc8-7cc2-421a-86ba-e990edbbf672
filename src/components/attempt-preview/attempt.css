.ql-snow .ql-editor pre.ql-syntax {
	background-color: #f5f5f5;
	color: #333;
	display: block;
	padding: 9.5px;
	margin: 0 0 10px;
	font-size: 13px;
	line-height: 1.42857143;
	word-break: break-all;
	word-wrap: break-word;
	border: 1px solid #ccc;
	border-radius: 4px;
	white-space: pre; /* Keeps it in a single line */
	overflow-x: auto; /* Enables horizontal scrolling */
}

pre::-webkit-scrollbar {
	height: 6px; /* Thin scrollbar */
}

pre::-webkit-scrollbar-track {
	background: #e0e0e0; /* Track color */
	border-radius: 10px; /* Rounded edges */
}

pre::-webkit-scrollbar-thumb {
	background: #bababa; /* Scrollbar color */
	border-radius: 10px; /* Rounded scrollbar */
	cursor: auto;
}

.ql-snow p {
	font-size: 1em;
	font-weight: 400;
	line-height: 1.5;
	margin-bottom: 1em;
}
.ql-container {
	border: none !important;
}
