import { useState } from "react";
import { Select, Tag } from "antd";

interface TagsProps {
	value?: string[];
	invalidValues?: string[];
	onChange?: (value: string[]) => void;
	separator?: RegExp;
	max?: number;
	placeholder?: string;
	isDisabled?: boolean;
}

export default function Tags({
	value = [],
	invalidValues = [],
	onChange,
	separator = /[ ,;]+/,
	max = Infinity,
	placeholder = "Type here",
	isDisabled = false,
}: TagsProps) {
	const invalidValuesSet = new Set(invalidValues);
	const [inputValue, setInputValue] = useState("");

	// Function to process the input value when a separator is detected
	const handleSearch = (searchText: string) => {
		if (separator.test(searchText)) {
			const newValues = searchText
				.split(separator)
				.map(token => token.trim())
				.filter(token => token !== "");

			if (newValues.length > 0) {
				let newTags = [...value, ...newValues];
				newTags = Array.from(new Set(newTags));

				if (newTags.length > max) {
					newTags = newTags.slice(0, max);
				}

				if (onChange) {
					onChange(newTags);
				}
				setInputValue("");
				return;
			}
		}
		setInputValue(searchText);
	};

	const handleChange = (newTags: string[]) => {
		if (onChange) {
			onChange(newTags);
		}
	};

	const handleTagDoubleClick = (tag: string) => {
		const newTags = value.filter(t => t !== tag);
		if (onChange) {
			onChange(newTags);
		}
		setInputValue(tag);
	};

	return (
		<Select
			mode="tags"
			value={value}
			style={{ width: "100%" }}
			placeholder={placeholder}
			open={false}
			searchValue={inputValue}
			onSearch={handleSearch}
			onChange={handleChange}
			tokenSeparators={[]} // We're handling tokenizing manually
			onBlur={() => setInputValue("")}
			onInputKeyDown={e => {
				if (e.key === "Enter") {
					// Prevent the default behavior so the input doesn't retain the text
					e.preventDefault();
					if (inputValue.trim() !== "") {
						// Append a space to force our tokenization logic to run
						handleSearch(inputValue + " ");
					}
				}
			}}
			tagRender={tagProps => {
				const { label, value, closable, onClose } = tagProps;
				return (
					<Tag
						color={invalidValuesSet.has(value) ? "red" : "default"}
						closable={closable}
						onClose={onClose}
						onDoubleClick={() =>
							!isDisabled && handleTagDoubleClick(value)
						}
						style={{ textWrap: "wrap", lineBreak: "anywhere" }}
					>
						{label}
					</Tag>
				);
			}}
			suffixIcon={
				value.length > 0 ? (
					<span>
						{value.length}
						{max !== Infinity ? `/${max}` : ""}
					</span>
				) : null
			}
		/>
	);
}
