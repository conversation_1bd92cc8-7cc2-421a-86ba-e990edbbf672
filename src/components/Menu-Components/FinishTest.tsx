import { useCallback, useEffect, useState } from "react";
import { Modal, Input, Button, Flex, Typography } from "antd";
import type { GetProps } from "antd";
import { ReportDataType } from "@/testReport/data/data";
import { useSocketMutation, useSocketQuery } from "@/hooks/socket";
import { quizClient } from "../../store/index";

type OTPProps = GetProps<typeof Input.OTP>;

const FinishTest = ({
	users,
	quizId,
	onClose,
	sendOtp,
	onSubmit,
}: {
	users: ReportDataType[];
	onClose: () => void;
	sendOtp: () => Promise<void>;
	onSubmit?: (_message: string, _type: "success" | "error") => void;
	quizId?: string;
}) => {
	const [otp, setOtp] = useState<string>("");
	const [counter, setCounter] = useState<number>(0);
	const [otpSent, setOtpSent] = useState<boolean>(false);
	const [otpStatus, setOtpStatus] = useState<"" | "error">("");
	const [loading, setLoading] = useState<boolean>(false);
	const [resendLoading, setResendLoading] = useState<boolean>(false);

	const { get, data } = useSocketQuery<
		"completed",
		Promise<{ error: string }>
	>("completed", async data => {
		console.log("Socket Result: ", data);
		setLoading(false);
		return data;
	});

	useEffect(() => {
		let timer: NodeJS.Timeout;
		if (otpSent && counter > 0) {
			timer = setInterval(() => {
				setCounter(prev => prev - 1);
			}, 1000);
		} else if (!otpSent && counter > 0) {
			timer = setInterval(() => {
				setCounter(prev => prev - 1);
			}, 1000);
		}
		return () => clearInterval(timer);
	}, [otpSent, counter]);

	const handleResendOTP = async () => {
		setResendLoading(true);
		await sendOtp();
		await getResendOTPTime();
		setOtpStatus("");
		setOtpSent(true);
		setResendLoading(false);
	};

	const { mutateAsync } = useSocketMutation<
		[string, string, ReportDataType[]],
		void
	>(
		async (
			socket,
			quizId: string,
			otp: string,
			users: ReportDataType[]
		): Promise<void> => {
			console.log(users);
			socket.emit(
				"submit_all_test",
				quizId,
				otp,
				users.map(user => user._id)
			);
		}
	);

	useEffect(() => {
		if (data) {
			console.log("Socket Data: ", data);
		}
		let response;
		if (typeof data === "string") {
			response = JSON.parse(data);
		}
		if (response && response?.error) {
			setOtpStatus("error");
			setLoading(false);
		} else if (response) {
			setOtpStatus("");
			setLoading(false);
			const failedUsers = response?.failedUserIds.filter(
				(id: string) => !users.find(user => user._id === id)?.completed
			);
			if (failedUsers.length === 0) {
				onSubmit?.("Test submitted successfully", "success");
			} else {
				onSubmit?.(
					`Failed to submit test for ${failedUsers.length} ${failedUsers.length > 1 ? "users" : "user"}`,
					"error"
				);
				setOtp("");
				setOtpSent(false);
			}
			setOtpSent(false);
			setOtp("");
			onClose();
		}
	}, [data]);

	const handleVerify = useCallback(async () => {
		if (!quizId) {
			return;
		}
		if (!otp || otp.trim() === "" || otp.length !== 6) {
			setOtpStatus("error");
			return;
		}
		console.log("Submitted OTP:", otp);
		setLoading(true);
		get(async () => {
			await mutateAsync(quizId, otp, users);
		});
	}, [quizId, otp, get, mutateAsync, users]);

	const isModalOpen = users !== undefined;
	if (users === undefined || users.length === 0) {
		return <></>;
	}

	const onChange: OTPProps["onChange"] = text => {
		setOtp(text);
	};

	const getResendOTPTime = async () => {
		try {
			const timer = await quizClient.getOtpWaitTime(quizId || "");
			if (timer.waitTime_Sec > 0) setCounter(timer.waitTime_Sec);
			else {
				setCounter(0);
			}
		} catch (error) {
			console.log(error);
			setCounter(0);
		}
	};

	const handleSendOTP = async () => {
		console.log("OTP Sent");
		await sendOtp();
		await getResendOTPTime();
		setOtpSent(true);
	};

	return (
		<Modal
			title={
				users.length > 1
					? "Submit selected Tests"
					: `Submit Test for User ${users[0].displayname}`
			}
			open={isModalOpen}
			onCancel={() => onClose()}
			footer={
				otpSent ? (
					<>
						<Button
							type="primary"
							onClick={handleResendOTP}
							disabled={counter > 0}
							loading={resendLoading}
						>
							{counter > 0
								? `Resend OTP (${counter}s)`
								: "Resend OTP"}
						</Button>
						<Button
							onClick={handleVerify}
							type="primary"
							loading={loading}
						>
							Verify
						</Button>
					</>
				) : (
					<Button
						onClick={handleSendOTP}
						type="primary"
						disabled={counter > 0}
					>
						{counter > 0 ? `Send OTP (${counter}s)` : "Send OTP"}
					</Button>
				)
			}
			style={{ top: 20 }}
		>
			{otpSent ? (
				<>
					<Flex gap={16} vertical style={{ marginBottom: 16 }}>
						<Typography.Text>Enter OTP</Typography.Text>
						<Input.OTP
							style={{ width: "70%" }}
							onChange={onChange}
							value={otp}
							status={otpStatus}
						/>
						{otpStatus === "error" && (
							<Typography.Text
								type="danger"
								style={{ margin: 0 }}
							>
								! Invalid OTP
							</Typography.Text>
						)}
					</Flex>
				</>
			) : (
				<Flex vertical gap={16}>
					<Typography.Text>
						A one-time password (OTP) will be sent to your email
						address.
					</Typography.Text>
				</Flex>
			)}
		</Modal>
	);
};

export default FinishTest;
