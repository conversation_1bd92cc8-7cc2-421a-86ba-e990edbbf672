import { ReportDataType } from "@/testReport/data/data";
import { quizClient } from "../../store";
import { Modal, Select, Typography, Flex, Form, Badge } from "antd";

const { Text } = Typography;

export type SectionControlDataType = {
	segmentNames?: string[];
	users?: ReportDataType[];
};

const SectionControlModal = ({
	sectionControlData,
	onClose,
	quizId,
	onSubmit,
}: {
	sectionControlData?: SectionControlDataType;
	onClose: () => void;
	quizId: string;
	onSubmit?: (_message: string, _type: "success" | "error") => void;
}) => {
	const [form] = Form.useForm();
	const { users, segmentNames } = sectionControlData || {};

	const isSectionControlOpen = users !== undefined;
	if (users === undefined || users.length === 0) {
		return <></>;
	}
	const handleSubmit = async (values: { section: number }) => {
		onClose();

		try {
			const data = {
				userId: users?.map(user => user._id),
				resetUpto: values.section,
			};
			const response = await quizClient.userSectionControl(quizId, data);
			if ("error" in response) {
				onSubmit?.(response.error, "error");
			} else {
				onSubmit?.("Section reset complete", "success");
			}
		} catch (ex) {
			onSubmit?.(ex as string, "error");
		}
	};
	return (
		<Modal
			title={
				users.length > 1 ? (
					<Flex align="center" gap={5}>
						<p>Section Control for Selected Users </p>
						<Badge
							size="default"
							color="#df6733"
							count={users.length}
						/>
					</Flex>
				) : (
					`Section Control for ${users[0].displayname}`
				)
			}
			open={isSectionControlOpen}
			okText="Submit"
			onOk={() => form.submit()}
			onCancel={() => {
				onClose();
			}}
			style={{ top: 20 }}
		>
			<Form onFinish={handleSubmit} form={form}>
				<Flex vertical gap={20}>
					<Text>
						The student will be shifted to the selected section.
					</Text>

					<Form.Item
						name="section"
						initialValue={0}
						rules={[
							{
								required: true,
								message: "Please select a section!",
							},
						]}
					>
						<Select
							options={segmentNames?.map((segment, index) => ({
								value: index,
								label: `${index + 1} - ${segment}`,
							}))}
						/>
					</Form.Item>
				</Flex>
			</Form>
		</Modal>
	);
};

export default SectionControlModal;
