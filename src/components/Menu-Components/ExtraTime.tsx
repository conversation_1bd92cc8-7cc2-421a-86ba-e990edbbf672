import { Mo<PERSON>, Input<PERSON><PERSON><PERSON>, <PERSON>, Flex, Badge } from "antd";
import { ReportDataType } from "@/testReport/data/data";
import { quizClient } from "../../store/";
type extraTimeType = {
	num1?: number;
	num2?: number;
	users?: ReportDataType[];
	onClose: () => void;
	quizId: string;
	quizTime?: number;
	onSubmit?: (_message: string, _type: "success" | "error") => void;
};
const ExtraTime = ({
	num1 = 10,
	num2 = 10,
	users,
	onClose,
	quizId,
	quizTime,
	onSubmit,
}: extraTimeType) => {
	const [form] = Form.useForm();
	const isExtraTimeModalOpen = users !== undefined;
	const shouldNotShowExpireTime = !users?.some(
		user => user.status === "InProgress"
	);
	if (users === undefined || users.length === 0) {
		return <></>;
	}

	const onFinish = async (values: {
		extraTime: number;
		expireTime?: number;
	}) => {
		onClose();
		if (!values.expireTime) {
			values.expireTime = 10;
		}
		try {
			const response = await quizClient.addExtraTime({
				quizId: quizId,
				userId: users?.map(user => user._id),
				time: values.extraTime,
				...(shouldNotShowExpireTime && {
					extraTimeExpire: values.expireTime,
				}),
			});
			if ("error" in response) {
				onSubmit?.(response.error, "error");
			} else {
				onSubmit?.(response.msg, "success");
			}
		} catch (ex) {
			onSubmit?.(ex as string, "error");
		}
	};

	return (
		<Modal
			title={
				users.length > 1 ? (
					<div
						style={{
							display: "flex",
							alignItems: "center",
							gap: 5,
							borderBottom: "none",
						}}
					>
						<p>Add Extra Time to Selected Users </p>
						<Badge
							size="default"
							color="#df6733"
							count={users.length}
						/>
					</div>
				) : (
					`Add Extra Time to ${users[0].displayname}`
				)
			}
			open={isExtraTimeModalOpen}
			style={{ top: 20 }}
			okText="Save"
			onOk={() => form.submit()}
			onCancel={() => onClose()}
		>
			<Form
				form={form}
				onFinish={onFinish}
				initialValues={{ extraTime: num1, expireTime: num2 }}
				layout="vertical"
			>
				<Flex justify="space-between" gap={20}>
					<Form.Item
						label="Extra time"
						tooltip="Time in minutes"
						name="extraTime"
						style={{ width: "100%" }}
						rules={[
							{
								required: true,
								message: "Please enter a number!",
							},
							() => ({
								validator(_, value) {
									if (value > (quizTime ?? 0)) {
										return Promise.reject(
											new Error(
												"Extra Time Must Be Less Than Test Duration."
											)
										);
									}
									return Promise.resolve();
								},
							}),
						]}
					>
						<InputNumber
							min={1}
							placeholder="Enter extra time (minutes)"
							style={{ width: "100%" }}
						/>
					</Form.Item>

					{shouldNotShowExpireTime && (
						<Form.Item
							style={{ width: "100%" }}
							label="Expires in"
							tooltip="Time in minutes"
							name="expireTime"
							rules={[
								{
									required: true,
									message: "Please enter a number!",
								},
								() => ({
									validator(_, value) {
										if (value > (quizTime ?? 0)) {
											return Promise.reject(
												new Error(
													"Expire Time Must Be Less Than Test Duration"
												)
											);
										}
										return Promise.resolve();
									},
								}),
							]}
						>
							<InputNumber
								min={1}
								placeholder="Enter expiry time (minutes)"
								style={{ width: "100%" }}
							/>
						</Form.Item>
					)}
				</Flex>
			</Form>
		</Modal>
	);
};

export default ExtraTime;
