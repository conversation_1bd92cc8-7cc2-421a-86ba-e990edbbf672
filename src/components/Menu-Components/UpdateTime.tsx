import { InvitedUsers } from "@/testReport/testReport";
import { Button, DatePicker, Form, Modal } from "antd";
import dayjs from "dayjs";

const UpdateTime = ({
	data,
	onClose,
	onSubmit,
	endTime,
}: {
	data: InvitedUsers | null;
	onClose: () => void;
	endTime?: dayjs.Dayjs;
	onSubmit?: (time: { expireTime: string }) => void;
}) => {
	const [updateTimeForm] = Form.useForm();
	return (
		<Modal
			title="Expire Link"
			open={data !== null}
			onCancel={onClose}
			footer={[
				<Button
					key="update"
					type="primary"
					onClick={() => updateTimeForm.submit()}
				>
					Update
				</Button>,
			]}
			destroyOnClose
		>
			<Form
				form={updateTimeForm}
				name="update-time"
				initialValues={{
					expireTime: data?.validTill
						? dayjs(data.validTill)
						: undefined,
				}}
				onFinish={onSubmit}
				layout="vertical"
			>
				<Form.Item
					label="Expiration Time"
					name="expireTime"
					rules={[
						{
							required: true,
							message: "Please Enter Date and Time.",
						},
						{
							validator(_rule, value) {
								if (
									value &&
									endTime &&
									value.valueOf() > endTime.valueOf()
								) {
									return Promise.reject(
										"Expire time cannot be greater than end time"
									);
								} else if (
									value &&
									value.valueOf() < dayjs().valueOf()
								) {
									return Promise.reject(
										"Expire time cannot be less than current time"
									);
								}
								return Promise.resolve();
							},
						},
					]}
				>
					<DatePicker
						minDate={dayjs()}
						maxDate={endTime}
						showTime
						showNow={false}
						format="YYYY-MM-DD HH:mm"
						style={{ width: "100%" }}
					/>
				</Form.Item>
			</Form>
		</Modal>
	);
};

export default UpdateTime;
