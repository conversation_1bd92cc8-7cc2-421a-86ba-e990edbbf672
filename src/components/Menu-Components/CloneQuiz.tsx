import { useEffect, useState } from "react";
import { quizClient } from "../../store/";
import { Modal, Form, Switch } from "antd";
import { useNavigate } from "react-router";

const CloneQuiz = ({
	token,
	quizId,
	onClose,
	onSubmit,
	randomizeQuestion,
	poolQuestion,
}: {
	token?: string | undefined;
	quizId?: string | undefined;
	randomizeQuestion?: boolean;
	poolQuestion?: boolean;
	onClose: () => void;
	onSubmit?: (_message: string, _type: "success" | "error") => void;
}) => {
	const isModalOpen = quizId !== undefined || token !== undefined;
	const [form] = Form.useForm();
	const navigate = useNavigate();
	const [switchValues, setSwitchValues] = useState({
		randomizeQuestions: false,
		poolQuestion: false,
	});
	const [loading, setLoading] = useState<boolean>(false);

	useEffect(() => {
		if (randomizeQuestion) {
			form.setFieldValue("randomizeQuestions", randomizeQuestion);
		} else if (poolQuestion) {
			form.setFieldValue("poolQuestion", !randomizeQuestion);
		}
	}, [form, poolQuestion, randomizeQuestion]);
	const handleOk = async () => {
		try {
			const values = await form.validateFields();

			let response;
			setLoading(true);
			if (token) {
				response = await quizClient.cloneQuizByToken({
					token: token,
					randomizeQuestion: values.randomizeQuestions,
					poolQuestion: values.poolQuestion,
					isClone: true,
				});
			} else if (quizId) {
				response = await quizClient.cloneQuiz(quizId, {
					quizId: quizId,
					randomizeQuestion: values.randomizeQuestions,
					poolQuestion: values.poolQuestion,
					isClone: true,
				});
			} else {
				throw new Error("Token or Quiz id is not available");
			}
			if ("error" in response) {
				throw new Error(response.error);
			}
			onSubmit?.("Quiz Cloned Successfully", "success");
			return navigate(`/tests/${response._id}`);
		} catch (error) {
			console.error(error);
			onSubmit?.("Failed to clone quiz", "error");
		} finally {
			setLoading(false);
		}
	};

	const handleSwitchChange = (name: string, checked: boolean) => {
		setSwitchValues(() => ({
			randomizeQuestions: name === "randomizeQuestions" ? checked : false,
			poolQuestion: name === "poolQuestion" ? checked : false,
		}));
		form.setFieldsValue({
			randomizeQuestions: name === "randomizeQuestions" ? checked : false,
			poolQuestion: name === "poolQuestion" ? checked : false,
		});
	};

	return (
		<>
			<Modal
				title="Clone Quiz"
				open={isModalOpen}
				onOk={handleOk}
				onCancel={onClose}
				okText="Clone"
				cancelText="Cancel"
				confirmLoading={loading}
			>
				<Form form={form} layout="horizontal" name="cloneQuizForm">
					<Form.Item
						name="randomizeQuestions"
						label="Shuffle Questions"
						valuePropName="checked"
					>
						<Switch
							checked={switchValues.randomizeQuestions}
							onChange={checked =>
								handleSwitchChange(
									"randomizeQuestions",
									checked
								)
							}
						/>
					</Form.Item>
					<Form.Item
						name="poolQuestion"
						label="Show Random Questions"
						valuePropName="checked"
					>
						<Switch
							checked={switchValues.poolQuestion}
							onChange={checked =>
								handleSwitchChange("poolQuestion", checked)
							}
						/>
					</Form.Item>
				</Form>
			</Modal>
		</>
	);
};

export default CloneQuiz;
