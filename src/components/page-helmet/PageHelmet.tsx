// components/PageHelmet.tsx
import { AppConfig } from "@/config";
import { PlatformOrganisation, platformTitle } from "@/constants";
import { Helmet } from "react-helmet-async";

interface PageHelmetProps {
	title: string;
	description?: string;
	suffix?: string;
}

export default function PageHelmet({
	title,

	suffix = "| " +
		(AppConfig.platformOrganisation === PlatformOrganisation.CHITKARA
			? platformTitle[1]
			: platformTitle[0]),
	description,
}: PageHelmetProps) {
	return (
		<Helmet>
			<title>
				{title} {suffix}
			</title>
			{description && <meta name="description" content={description} />}
		</Helmet>
	);
}
