import { <PERSON><PERSON>, <PERSON>lex, In<PERSON>, <PERSON>con<PERSON>rm, <PERSON><PERSON>, <PERSON><PERSON>, Toolt<PERSON> } from "antd";
import "./attempt-web.scoped.css";
import { useCallback, useRef, useState } from "react";
import { QuestionTypeWeb } from "@/testReport/data/data";
import {
	CaretDownFilled,
	CaretRightFilled,
	CaretUpFilled,
	CheckOutlined,
	CloseOutlined,
	MinusCircleFilled,
	ReloadOutlined,
	UndoOutlined,
	WarningFilled,
} from "@ant-design/icons";
import AceEditor, { IAceEditorProps } from "react-ace";
import { BrowserIframe } from "../browser-iframe/browser-iframe";
import { useAppStore } from "../../store";

const enum TestCaseStatus {
	NotExecuted = 0,
	Running = 1,
	Passed = 2,
	Failed = 3,
	Unknown = 4,
}

type WebTestCaseMeta = QuestionTypeWeb["testCase"][number];

interface AttemptWebProps {
	questionId: string;
	questionData: QuestionTypeWeb;
}

// interface OutputArray {
// 	userOutput: string;
// 	testCasePassed: boolean;
// 	score: number;
// 	testCase?: {
// 		codeproginputparams: string;
// 		codeprogexpectedoutput: string;
// 		attemptInMultiLine: boolean;
// 		sampleTest: boolean;
// 		scoreip: number;
// 		_id: string;
// 		onCloud?: boolean;
// 	};
// }

const editorProps: IAceEditorProps = {
	theme: "textmate",
	fontSize: 14,
	lineHeight: 24,
	width: "100%",
	height: "100%",
	setOptions: {
		enableBasicAutocompletion: false,
		enableLiveAutocompletion: false,
		enableSnippets: false,
		enableMobileMenu: true,
		showLineNumbers: true,
		tabSize: 2,
	},
};

// function useCodeCompile(props: { questionId: string }) {

// 	const [running, setRunning] = useState<Record<string, boolean>>({});

// 	useSocketWatch("compile", {
// 		listnerFunction(data) {
// 			if (!data) {
// 				return;
// 			}
// 			const lang = parseInt(data.progLang);
// 			const outputArray: Array<OutputArray> = [];
// 			(data.outputArray ?? []).forEach((ele, index) => {
// 				outputArray.push({
// 					...ele,
// 					testCase: data?.testCase[index] ?? null,
// 				});
// 			});
// 			setRunning(prev => ({
// 				...prev,
// 				[lang]: false,
// 			}));
// 			return;
// 		},
// 		key: [props.questionId],
// 	});

// 	const { mutateAsync } = useSocketMutation<
// 		[
// 			questionId: string,
// 			args: {
// 				language: string,
// 				html: string,
// 				css: string,
// 				js: string,
// 				codeId: string,
// 			}
// 		],
// 		void
// 	>(
// 		async function (socket, questionId, args) {
// 			socket.emit("compile", {
// 				code: "",
// 				html: args.html,
// 				css: args.css,
// 				js: args.js,
// 				language: args.language,
// 				questionId: questionId,
// 				stdin: "",
// 				isInvalidAttempt: true,
// 			});
// 			return;
// 		}
// 	);
// 	return { compile: mutateAsync, running };
// }

export default function PreviewWeb(props: AttemptWebProps) {
	const { questionData } = props;

	const { messageInstance } = useAppStore();

	const iframeBrowserRef: React.Ref<HTMLIFrameElement> = useRef(null);

	const [testCasesStatus, setTestCasesStatus] = useState<TestCaseStatus[]>(
		Array(questionData.testCase.length).fill(TestCaseStatus.NotExecuted)
	);
	const [consoleVisible, setConsoleVisible] = useState<boolean>(false);
	const [previewBrowserHTML, setPreviewBrowserHTML] = useState<
		string | undefined
	>(`
		<p style="margin: 20px 10px; text-align: center;">click on the ${questionData.testCase.length ? "run code/" : ""}preview button to see the preview.</p>
	`);

	const [submitting] = useState<boolean>(false);
	const [runningTests, setRunningTests] = useState<boolean>(false);
	const [selectedTestCase, setSelectedTestCase] = useState<
		WebTestCaseMeta | undefined
	>();

	const [htmlEditorValue, setHTMLEditorValue] = useState<string>(
		questionData.html
	);
	const [cssEditorValue, setCSSEditorValue] = useState<string>(
		questionData.css
	);
	const [jsEditorValue, setJSEditorValue] = useState<string>(questionData.js);

	const resetCode = useCallback(() => {
		setHTMLEditorValue(questionData.html);
		setCSSEditorValue(questionData.css);
		setJSEditorValue(questionData.js);
	}, [questionData]);

	const toggleConsoleVisible = useCallback(() => {
		setConsoleVisible(!consoleVisible);
	}, [consoleVisible, setConsoleVisible]);

	const handlePreview = useCallback(() => {
		let isReactQuestion = false;
		let reactRendererCode = "";
		if (questionData) {
			reactRendererCode = questionData.reactRendererCode;
			isReactQuestion = questionData.isReactQuestion;
		}

		const template = getIframeTemplate(
			htmlEditorValue,
			cssEditorValue,
			jsEditorValue,
			reactRendererCode,
			isReactQuestion
		);

		setPreviewBrowserHTML("");
		setTimeout(() => setPreviewBrowserHTML(template), 0);
	}, [htmlEditorValue, cssEditorValue, jsEditorValue, questionData]);

	const setTestCaseStatus = useCallback(
		(index: number, status: TestCaseStatus) => {
			setTestCasesStatus(prevStatus => {
				const newStatus = [...prevStatus];
				newStatus[index] = status;
				return newStatus;
			});
		},
		[setTestCasesStatus]
	);

	const runWebTests = useCallback(
		async (e: MessageEvent<string>) => {
			try {
				if (typeof e.data !== "string") {
					return;
				}

				const data = JSON.parse(e.data);
				if (data.loaded) {
					window.removeEventListener("message", runWebTests);

					const contentWindow =
						iframeBrowserRef.current?.contentWindow;
					if (contentWindow) {
						const testRunners = questionData.testCase.map(
							(testCase, index) => async () =>
								(async () => {
									const result = await webTestsRunner(
										contentWindow,
										testCase.evaluator
									).catch(error => {
										messageInstance?.error(
											`Error running test case ${index + 1}: ${error.message}`
										);
										return false;
									});

									setTestCaseStatus(
										index,
										result
											? TestCaseStatus.Passed
											: TestCaseStatus.Failed
									);
								})()
						);

						if (testRunners) {
							for (const run of testRunners) {
								await run();
							}
							setRunningTests(false);
						}
					}
				}
			} catch (error) {
				console.error(error);
			}
		},
		[questionData, setTestCaseStatus]
	);

	const handleRunWebTests = useCallback(async () => {
		setRunningTests(true);

		setTestCasesStatus(
			Array(questionData.testCase.length).fill(TestCaseStatus.Running)
		);

		if (questionData.testCase.length) {
			setConsoleVisible(true);
		}
		handlePreview();

		window.addEventListener("message", runWebTests, false);
	}, [questionData, setTestCaseStatus, handlePreview, runWebTests]);

	return (
		<div className="attempt-web-container">
			<div style={{ height: consoleVisible ? "65%" : "100%" }}>
				<Splitter>
					<Splitter.Panel min={200} collapsible={false}>
						<Tabs
							type="card"
							size="small"
							tabBarExtraContent={{
								right: (
									<Button
										type="primary"
										size="small"
										onClick={handlePreview}
										disabled={runningTests || submitting}
										style={{
											marginRight: "0.5rem",
											fontSize: 12,
											fontFamily: "Hind, sans-serif",
											fontWeight: 500,
										}}
									>
										preview
										<CaretRightFilled
											style={{
												position: "relative",
												top: 1,
											}}
										/>
									</Button>
								),
							}}
							items={[
								{
									key: "index.html",
									label: "index.html",
									hidden: !questionData.isHtmlAllowed,
									children: (
										<AceEditor
											{...editorProps}
											mode="html"
											value={htmlEditorValue}
											onChange={setHTMLEditorValue}
											readOnly={
												runningTests || submitting
											}
										/>
									),
								},
								{
									key: "index.css",
									label: "index.css",
									hidden: !questionData.isCssAllowed,
									children: (
										<AceEditor
											{...editorProps}
											mode="css"
											value={cssEditorValue}
											onChange={setCSSEditorValue}
											readOnly={
												runningTests || submitting
											}
										/>
									),
								},
								{
									key: "index.js",
									label: "index.js",
									hidden: !questionData.isJsAllowed,
									children: (
										<AceEditor
											{...editorProps}
											mode="javascript"
											value={jsEditorValue}
											onChange={setJSEditorValue}
											readOnly={
												runningTests || submitting
											}
										/>
									),
								},
							]
								.filter(item => !item.hidden)
								.map(item =>
									Object.assign(item, {
										style: { height: "100%" },
									})
								)}
						/>
					</Splitter.Panel>
					{/* <div className="editors-browser-divider" /> */}
					<Splitter.Panel
						min={200}
						className="preview-browser-container"
						collapsible={false}
					>
						<div className="preview-browser-header">
							<div className="action-icon">
								<Tooltip title="reload" placement="bottom">
									<Button
										type="text"
										icon={<ReloadOutlined />}
										onClick={handlePreview}
										disabled={runningTests || submitting}
									/>
								</Tooltip>
							</div>
							<div className="address-bar">
								<Input
									defaultValue="https://localhost"
									disabled
								/>
							</div>
						</div>
						<BrowserIframe
							browserRef={iframeBrowserRef}
							srcDoc={previewBrowserHTML}
						/>
					</Splitter.Panel>
				</Splitter>
			</div>
			{consoleVisible ? (
				<div className="console-container" style={{ height: "50%" }}>
					<Tabs
						type="card"
						defaultActiveKey="test-cases"
						size="small"
						tabBarStyle={{
							padding: "8px 8px 0px 8px",
							backgroundColor: "var(--primary-light)",
						}}
						tabBarExtraContent={{
							right: (
								<Button
									type="text"
									shape="circle"
									icon={
										<MinusCircleFilled
											style={{
												color: "var(--primary-color)",
												opacity: 0.75,
											}}
										/>
									}
									onClick={toggleConsoleVisible}
								/>
							),
						}}
						items={[
							{
								key: "test-cases",
								label: "Test Cases",
								children: (
									<Flex style={{ height: "100%" }}>
										<Flex
											vertical
											style={{
												width: 150,
												overflow: "auto",
											}}
										>
											{testCasesStatus.map(
												(status, index) => {
													const testCase =
														questionData.testCase[
															index
														];
													return (
														<Button
															className={
																selectedTestCase?._id ===
																testCase._id
																	? "active"
																	: ""
															}
															key={testCase._id}
															type="text"
															size="large"
															icon={(() => {
																if (
																	status ===
																	TestCaseStatus.NotExecuted
																) {
																	return (
																		<WarningFilled
																			style={{
																				color: "#fed639",
																				fontSize: 18,
																			}}
																		/>
																	);
																}
																if (
																	status ===
																	TestCaseStatus.Passed
																) {
																	return (
																		<CheckOutlined
																			style={{
																				color: "green",
																				fontSize: 18,
																			}}
																		/>
																	);
																}
																if (
																	status ===
																	TestCaseStatus.Failed
																) {
																	return (
																		<CloseOutlined
																			style={{
																				color: "red",
																				fontSize: 18,
																			}}
																		/>
																	);
																}
																return null;
															})()}
															style={{
																padding:
																	"0.5em",
															}}
															loading={
																status ===
																TestCaseStatus.Running
															}
															onClick={() =>
																setSelectedTestCase(
																	testCase
																)
															}
														>
															Test Case{" "}
															{index + 1}
														</Button>
													);
												}
											)}
										</Flex>
										<div className="test-case-details-container">
											{selectedTestCase && (
												<>
													<span>Description:</span>
													<pre>
														{
															selectedTestCase.description
														}
													</pre>
												</>
											)}
										</div>
									</Flex>
								),
							},
						]}
					></Tabs>
				</div>
			) : (
				<div />
			)}
			<div className="footer">
				<div>
					{questionData.testCase.length ? (
						<Button
							className={
								consoleVisible
									? "consoleBtn consoleOpened"
									: "consoleBtn"
							}
							type="text"
							onClick={toggleConsoleVisible}
						>
							<span>Test cases</span>
							{consoleVisible ? (
								<CaretDownFilled />
							) : (
								<CaretUpFilled />
							)}
						</Button>
					) : null}
				</div>
				<div>
					<Tooltip title="reset code" placement="left">
						<Popconfirm
							title="Do you really want to reset your code?"
							okText="yes"
							cancelText="no"
							onConfirm={resetCode}
							getPopupContainer={trigger =>
								trigger.parentElement || document.body
							}
							disabled={runningTests || submitting}
						>
							<Button
								type="text"
								shape="circle"
								disabled={runningTests || submitting}
								icon={<UndoOutlined />}
							/>
						</Popconfirm>
					</Tooltip>
					{questionData.testCase.length ? (
						<Button
							type="primary"
							disabled={submitting}
							loading={runningTests}
							onClick={handleRunWebTests}
						>
							run code
						</Button>
					) : null}
				</div>
			</div>
		</div>
	);
}

function getIframeTemplate(
	html: string,
	css: string,
	js: string,
	reactRendererCode: string,
	isReactQuestion: boolean
): string {
	return `<!DOCTYPE html>
			<html>
				<head>
					<meta charset="utf-8">
					<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
					<meta http-equiv="X-UA-Compatible" content="IE=edge">
					<script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
		${
			isReactQuestion
				? `<script crossorigin src="https://unpkg.com/react@17/umd/react.production.min.js"></script>
									<script crossorigin src="https://unpkg.com/react-dom@17/umd/react-dom.production.min.js"></script>`
				: ""
		}
					<title></title>
					<style>${css}</style>
					<script></script>
				</head>
				<body>
					${html}
					<script></script>
					<script>
						var __iframeParentRef = window.parent;
					</script>
					<script type="text/babel">
						${js.replace(/<\/script>/gi, "<\\/script>")}
					</script>
					<script></script>
					<script>
						window.addEventListener('error', (event) => {
								if(event && event.message && event.message == "Script error."){
									console.log('error in script');
									__iframeParentRef.postMessage(JSON.stringify({ loaded: true }));
								}
						});
					</script>
					<script type="text/babel">
						let intervalId;

						function checkIframeLoaded() {
							if (document.readyState === 'complete') {
								clearInterval(intervalId);
						
								__iframeParentRef.postMessage(JSON.stringify({ loaded: true }));
							}
						}
						intervalId = setInterval(checkIframeLoaded, 100);
					</script>
					<script type="text/babel">
						${isReactQuestion ? reactRendererCode : ""}
					</script>
					<script></script>
				</body>
			</html>`;
}

interface AsyncFunctionConstructor<T extends unknown[] = unknown[]> {
	new <T extends unknown[] = unknown[]>(
		...args: [...args: { [K in keyof T]: string }, body: string]
	): (...args: T) => Promise<unknown>;
	(...args: { [K in keyof T]: string }): (...args: T) => Promise<unknown>;
	readonly prototype: (...args: T) => Promise<unknown>;
}

// eslint-disable-next-line no-var
var AsyncFunction = async function () {}
	.constructor as AsyncFunctionConstructor;
// eslint-disable-next-line no-var
declare var AsyncFunction: AsyncFunctionConstructor;

async function webTestsRunner(previewFrameWindow: Window, evaluator: string) {
	const evaluatorFunction = new AsyncFunction<
		[previewFrameWindow: Window, previewFrameDocument: Document]
	>(
		"previewFrameWindow",
		"previewFrameDocument",
		`return await (${evaluator})`
	);
	const result = await evaluatorFunction(
		previewFrameWindow,
		previewFrameWindow.document
	);
	return Boolean(result);
}
