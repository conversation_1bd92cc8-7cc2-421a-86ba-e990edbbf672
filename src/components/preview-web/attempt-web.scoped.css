div.attempt-web-container {
	flex: auto;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	max-width: 100%;
	height: 100%;
}

div.attempt-web-container > div.footer {
	flex: 0 0 50px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	border-top: 1px solid #d0d0d0;
	padding: 0 1rem;
	background-color: #fff;
	z-index: 1;
}

div.footer button {
	border-radius: 4px;
	font-weight: 500;
}

div.footer > div:first-child > button {
	padding: 4px 10px;
}

div.footer > div:first-child > button span.anticon {
	position: relative;
	top: 1px;
}

div.footer > div:nth-child(2) > button {
	margin: 0 0.5rem;
}

div.attempt-web-container > div.main {
	flex: auto;
	/* display: flex;
	flex-direction: column;
	overflow: auto; */
}

div.main > div.attempt-area-container {
	display: flex;
	overflow: hidden;
}

div.attempt-area-container > div > div.code-editors-container,
div.attempt-area-container > div > div.preview-browser-container {
	display: flex;
	flex-direction: column;
}

div.code-editors-container > div.ant-tabs,
div.code-editors-container > div.ant-tabs > div.ant-tabs-content-holder,
div.code-editors-container
	> div.ant-tabs
	> div.ant-tabs-content-holder
	div.ant-tabs-tabpane {
	flex: auto;
	display: flex;
	flex-basis: 100%;
}

div.code-editors-container
	> div.ant-tabs
	> div.ant-tabs-content-holder
	> div.ant-tabs-content {
	display: flex;
	flex-basis: 100%;
}

div.code-editors-container
	> div.ant-tabs
	> div.ant-tabs-content-holder
	> div.ant-tabs-content
	> div.ant-tabs-tabpane-hidden {
	display: none;
}

div.main > div.ant-tabs div.ant-tabs-content-holder div.ant-tabs-content {
	display: flex;
}

div.code-editors-container > div.ant-tabs div.ant-tabs-tab {
	margin: 0 !important;
	font-size: 14px;
	border-bottom: none;
	border-top: 3px solid #fff;
}

div.code-editors-container > div.ant-tabs div.ant-tabs-tab > div {
	color: #333;
}

div.code-editors-container > div.ant-tabs div.ant-tabs-tab.ant-tabs-tab-active {
	border-top: 3px solid var(--primary-color) !important;
}

div.code-editors-container > div.ant-tabs > div.ant-tabs-nav {
	margin: 0;
	box-shadow: 0 1px 2px 1px #00000010;
	z-index: 1;
	background-color: #ededed;
}

div.code-editors-container div.html-editor,
div.code-editors-container div.css-editor,
div.code-editors-container div.js-editor {
	flex: auto;
	display: grid;
	overflow: auto;
}

div.attempt-area-container > div.editors-browser-divider {
	flex: 0 0 8px;
	background-color: #ddd;
}

div.preview-browser-container > div.preview-browser-header {
	display: flex;
	flex: 0 0 40px;
	padding: 2px 10px;
	box-shadow: 0 1px 2px 1px rgb(0 0 0 / 16%);
	align-items: center;
	background-color: #ededed;
}

div.preview-browser-header > div.address-bar {
	flex: 1 1 auto;
	margin: 0 5px;
}

div.preview-browser-header > div.address-bar > input {
	border-radius: 20px;
	color: #333;
}

div.preview-browser-header > div.action-icon button > span {
	position: relative;
	top: 2px;
}

div.main > div.console-container {
	display: flex;
	flex-direction: column;
	overflow: auto;
}

div.main > div.console-container div.ant-tabs {
	flex: auto;
}

div.main > div.console-container div.ant-tabs-content-holder {
	display: flex;
}

div.main > div.console-container div.ant-tabs-tabpane {
	display: flex;
	flex: auto;
	flex-direction: column;
}

div.main > div.console-container div.ant-tabs-nav {
	margin: 0;
	padding: 0.25rem 0.75rem 0 0.75rem;
	background-color: var(--primary-light);
}

div.main > div.console-container div.ant-tabs-nav > div.ant-tabs-extra-content {
	position: relative;
	top: -1.5px;
}

div.main > div.console-container div.ant-tabs-tab {
	background-color: var(--primary-light);
	border: none;
}

div.main > div.console-container div.ant-tabs-tab > div {
	color: #818181;
	font-weight: 500;
}

div.main > div.console-container div.ant-tabs-tab-active {
	background-color: #fff;
	border-radius: 10px 10px 0 0;
}

div.main > div.console-container div.ant-tabs-tab-active > div {
	color: #33333399;
	font-weight: 500;
}

div.test-cases-wrapper {
	padding: 1rem;
	flex: auto;
	display: flex;
	overflow: hidden;
}

div.test-cases-wrapper > div.test-cases-container {
	flex: 0 0 150px;
	display: flex;
	flex-direction: column;
	overflow: auto;
}

div.test-cases-wrapper > div.test-cases-container > button {
	font-weight: 500;
	font-size: 15px;
	color: var(--primary-color);
}

div.test-cases-wrapper > div.test-cases-container > button.active {
	background-color: #f7f7f7;
}

div.test-cases-wrapper > div.test-case-details-container {
	flex: auto;
	display: flex;
	flex-direction: column;
	padding: 0 1rem;
	overflow: auto;
}

div.test-case-details-container > span:first-child {
	font-size: 14px;
	font-weight: 500;
	color: #818181;
}

div.test-case-details-container > pre {
	flex: auto;
	display: flex;
	overflow: auto;
	background-color: #f8f8f8;
	padding: 0.5rem;
	overflow: unset;
	overflow-x: auto;
}

.consoleBtn:active,
.consoleBtn:focus,
.consoleBtn:hover {
	color: rgba(0, 0, 0, 0.85);
	border-color: #d9d9d9;
	background: transparent;
}

.consoleBtn.consoleOpened {
	color: #eb8b5b;
	border-color: #eb8b5b;
	background: transparent;
}
