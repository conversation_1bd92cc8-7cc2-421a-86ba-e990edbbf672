import {
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	Button,
	Flex,
	Layout,
	Space,
	Spin,
	TableColumnsType,
	Tabs,
	Typography,
} from "antd";
import {
	CopyOutlined,
	EyeOutlined,
	LinkOutlined,
	RightOutlined,
	ShareAltOutlined,
	UserAddOutlined,
	UsergroupAddOutlined,
	setTwoToneColor,
} from "@ant-design/icons";
import StickyBox from "react-sticky-box";
import { useCallback, useEffect, useMemo, useState } from "react";
import InviteSidebar from "../invite-sidebar/InviteSidebar";
import {
	Link,
	Navigate,
	Outlet,
	useMatch,
	useNavigate,
	useParams,
	useSearchParams,
} from "react-router";
import AllowedInvite from "../allowed-invite/AlloweInvite";
import { AnyObject } from "antd/es/_util/type";
import { quizClient, useAppStore } from "../../store";
import { TestContent, TestData, TestSaveProgressStep } from "@/client/test-add";
import useMessage from "antd/es/message/useMessage";
import InValidBulkUpload from "../bulk-error-modal/InValidBulkUpload";
import FinishTestLogs from "../finishTestLogs/finishTestLogs";
import { AppConfig } from "@/config";
import { RoleAction, RoleResource, UserRole } from "@/constants/roles";
import { useAppMessage } from "../../hooks/message";
import { PlatformOrganisation } from "@/constants";
import ShareTest from "../share-test/ShareTest";
import CloneQuiz from "../Menu-Components/CloneQuiz";
import confirm from "antd/es/modal/confirm";
import PageHelmet from "../page-helmet/PageHelmet";

setTwoToneColor("#de6834");

const columns: TableColumnsType<AnyObject> = [
	{
		key: "Serial Number",
		render: (_value, _record, index) => `${index + 1}.`,
		width: 45,
		align: "right",
	},
	{
		key: "email",
		title: "Emails",
		dataIndex: "email",
	},
];

export type TextLayoutContextType = {
	testId?: string;
	progressStep?: TestSaveProgressStep;
	isFromToken?: boolean;
	isFrozen?: boolean;
	isTemplate?: boolean;
	tabsData?: Partial<TestData["tabs"]>;
	setProgressStep: (_step: TestSaveProgressStep) => void;
	setTabsData: (_data: Partial<TestData["tabs"]>) => void;
	unsavedTabs: (keyof TestData["tabs"])[];
	setUnsavedTabs: (_keys: (keyof TestData["tabs"])[]) => void;
	testContent?: TestContent;
	setTestContent: (_content: TestContent) => void;
};

export default function TestLayout() {
	const messageInstance = useAppMessage();
	const { user, session } = useAppStore();
	const hasResourcePermission = useAppStore(
		state => state.hasResourcePermission
	);
	const navigate = useNavigate();
	const { id: testId, token } = useParams();
	const [progressStep, setProgressStep] = useState<
		TestSaveProgressStep | undefined
	>();
	const isTestBeingCreated =
		testId === undefined ||
		(progressStep ?? 0) < TestSaveProgressStep.PROCTORING;
	const [searchParams] = useSearchParams();
	const titleParam = searchParams.get("title");
	const [, settestLoaded] = useState(false);
	const [isLoading, setIsLoading] = useState(true);
	const [canShareTest, setCanShareTest] = useState<boolean>(false);
	const [cloneQuizToken, setCloneQuizToken] = useState<string | undefined>();
	const [isAlreadyCloned, setIsAlreadyCloned] = useState<boolean>(false);
	const match = useMatch(
		isTestBeingCreated ? "/tests/add/*" : "/tests/:testId/:tabId/*"
	);

	const matchToken = useMatch("/tests/cloneQuizByToken/:token/:tabId/*");

	const activeKey = useMemo(() => {
		if (match?.params.tabId) {
			return match.params.tabId;
		}
		if (token) {
			return matchToken?.params?.tabId;
		}
		return isTestBeingCreated ? "update" : undefined;
	}, [
		isTestBeingCreated,
		match?.params.tabId,
		matchToken?.params?.tabId,
		token,
	]);

	const [testContent, setTestContent] = useState<TestContent>();
	const [testProperties, setTestProperties] = useState<{
		isFrozen?: boolean;
		isTemplate?: boolean;
		isFromToken?: boolean;
	}>({ isFrozen: false, isTemplate: false, isFromToken: false });
	const [testTabsData, setTestTabsData] = useState<Partial<TestData["tabs"]>>(
		{}
	);
	const [unsavedTabs, setUnsavedTabs] = useState<(keyof TestData["tabs"])[]>(
		[]
	);
	const [testTitle, setTestTitle] = useState<string | null>("<no title>");

	const [isInviteSideBarOpen, setIsInviteSideBarOpen] =
		useState<boolean>(false);
	const [isAllowedInviteOpen, setIsAllowedInviteOpen] =
		useState<boolean>(false);
	const [isFinishTestLogsOpen, setIsFinishTestLogsOpen] =
		useState<boolean>(false);
	const [isShareTestOpen, setIsShareTestOpen] = useState<boolean>(false);
	const [failedEmails, setFailedEmails] = useState<AnyObject[]>([]);
	function setActiveKey(key: string) {
		let navigationUrl = `${testId}/${key}`;
		if (token) {
			navigationUrl = `${key}`;
		}
		navigate(navigationUrl);
	}

	const [messageAPI, messageContext] = useMessage();
	const setTabsData = useCallback(
		(data: Partial<TestData["tabs"]>) => {
			setTestTabsData(data);
		},
		[setTestTabsData]
	);

	const tabItems = useMemo(() => {
		const context: TextLayoutContextType = {
			testId,
			...testProperties,
			tabsData: testTabsData,
			unsavedTabs,
			setUnsavedTabs,
			progressStep,
			setTabsData,
			setProgressStep,
			testContent,
			setTestContent,
		};
		const hidden = context.isTemplate || testProperties.isFromToken;
		const disable = testProperties.isFromToken ? false : isTestBeingCreated;

		const canViewReport = hasResourcePermission(
			RoleResource.QUIZ,
			RoleAction.REPORT
		);
		const canViewContent =
			hasResourcePermission(RoleResource.QUEST) &&
			hasResourcePermission(RoleResource.QUIZ, RoleAction.DETAIL);
		const canEditSettings = hasResourcePermission(
			RoleResource.QUIZ,
			RoleAction.EDIT
		);

		const array: Array<{
			key: string;
			label: string;
			disabled?: boolean;
			isHidden?: boolean;
			requiredPermission?: boolean;
		}> = [];
		if (testProperties.isFromToken) {
			array.push(
				{
					key: "content",
					label: "Content",
					disabled: false,
					isHidden: true,
					requiredPermission: canViewContent,
				},
				{
					key: "settings",
					label: "Settings",
					disabled: false,
					isHidden: true,
					requiredPermission: canEditSettings,
				}
			);
		} else {
			array.push(
				{
					key: "report",
					label: "Candidates",
					disabled: disable,
					isHidden: false,
					requiredPermission: canViewReport,
				},
				{
					key: "content",
					label: "Content",
					disabled: disable,
					isHidden: hidden,
					requiredPermission: canViewContent,
				},
				{
					key: "metrics",
					label: "Metrics",
					disabled: disable,
					isHidden: false,
					requiredPermission: true,
				},
				{
					key: "rooms",
					label: "Rooms",
					disabled: disable,
					isHidden: false,
					requiredPermission: true,
				},
				{
					key: "update",
					label: "Settings",
					isHidden: hidden,
					requiredPermission: canEditSettings,
				}
			);
		}
		return array
			.filter(item => item.isHidden === hidden)
			.filter(item => item.requiredPermission)
			.filter(
				item =>
					item.key !== "rooms" ||
					(testTabsData.proctoring?.isWebCamAllowed &&
						testTabsData.proctoring.isLiveStreamEnabled)
			)
			.map(item => ({
				children:
					item.key === activeKey ? (
						<Outlet context={context} />
					) : (
						<></>
					),
				style: { height: "100%", overflow: "hidden" },
				...item,
			}));
	}, [
		activeKey,
		hasResourcePermission,
		isTestBeingCreated,
		progressStep,
		setTabsData,
		testContent,
		testId,
		testProperties,
		testTabsData,
		unsavedTabs,
	]);

	useEffect(
		function () {
			if (!token && testId === undefined) {
				setIsLoading(false);
				return;
			}
			(async function () {
				setIsLoading(true);
				try {
					let testData;
					if (token) {
						testData = await quizClient.getCloneQuizByToken(token);
					} else if (testId) {
						testData = await quizClient.getTestData(testId);
						if (
							!(
								hasResourcePermission(RoleResource.QUEST) &&
								hasResourcePermission(
									RoleResource.QUIZ,
									RoleAction.DETAIL
								)
							) ||
							!hasResourcePermission(
								RoleResource.QUIZ,
								RoleAction.EDIT
							)
						) {
							navigate(`/tests/${testId}/report`);
						}
					} else {
						setIsLoading(false);
						return;
					}
					setTestProperties({
						isFrozen: testData.isFrozen,
						isTemplate: testData.isTemplate,
						isFromToken: token ? true : false,
					});
					setTestTabsData(testData["tabs"]);
					if (
						(testData.progressStep ?? 0) <
						TestSaveProgressStep.PROCTORING
					) {
						handleNavigate(testData.progressStep ?? 0);
					}
					setProgressStep(testData.progressStep);
					if ("alreadyClonedThroughThisLink" in testData) {
						setIsAlreadyCloned(
							testData.alreadyClonedThroughThisLink ?? false
						);
					}
					settestLoaded(true);
				} catch (error) {
					console.error(error);
					messageAPI.error(error as string);
					navigate("/tests");
				} finally {
					setIsLoading(false);
				}
			})();
		},
		[messageAPI, testId, navigate, token]
	);

	useEffect(() => {
		if (isTestBeingCreated) {
			const title = titleParam;
			if (title === null) {
				setTestTitle(testTabsData?.general?.title ?? null);
			} else {
				setTestTitle(title);
			}
			return;
		}
		if (testTabsData.general) {
			setTestTitle(testTabsData?.general.title);
		}
	}, [isTestBeingCreated, titleParam, testTabsData]);

	useEffect(() => {
		if (titleParam === null) {
			return;
		}
		setTabsData({});
		setProgressStep(undefined);
	}, [titleParam]);

	useEffect(() => {
		if (!session) return;
		if (
			user?.role.id !== UserRole.USER &&
			user?.role.id !== UserRole.CUSTOM &&
			PlatformOrganisation.CHITKARA === AppConfig.platformOrganisation &&
			(session.isSuperOrg || session.isSubOrg)
		) {
			setCanShareTest(true);
		} else {
			setCanShareTest(false);
		}
	}, [session, user?.role.id]);

	const handleCloneClose = () => {
		setCloneQuizToken(undefined);
	};

	const handleCloneSubmit = (message: string, type: "success" | "error") => {
		if (type === "success") {
			messageInstance?.success(message);
			setCloneQuizToken(undefined);
		} else {
			messageInstance?.error(message);
		}
	};

	if (isLoading) {
		return (
			<Flex justify="center" align="center" style={{ height: "100%" }}>
				<Spin size="large" />
			</Flex>
		);
	}

	if (activeKey === undefined) {
		if (token) {
			return <Navigate to={`content`} />;
		}
		return (
			<Navigate
				to={
					`${testId}/content` +
					(isTestBeingCreated ? `?test=${testTitle}` : "")
				}
			/>
		);
	}

	function closeFailedEmailModal() {
		setFailedEmails([]);
	}

	function copyLink() {
		const link = AppConfig.testAttemptURL + "/" + testTabsData.access?.link;
		navigator.clipboard.writeText(link);
		messageInstance?.success("Link copied successfully.");
	}

	function handleNavigate(progressStep: TestSaveProgressStep) {
		switch (progressStep) {
			case TestSaveProgressStep.PROCTORING:
				navigate(`/tests/${testId}/update/notification`);
				break;
			case TestSaveProgressStep.CONTENT:
				navigate(`/tests/${testId}/update/proctoring`);
				break;
			case TestSaveProgressStep.ACCESS:
				navigate(`/tests/${testId}/update/content`);
				break;
			case TestSaveProgressStep.GENERAL:
				navigate(`/tests/${testId}/update/access`);
				break;
			default:
				navigate(`/tests/${testId}/update/general`);
				break;
		}
	}

	return (
		<>
			{(testTabsData.general?.title || titleParam) && (
				<PageHelmet
					title={testTabsData.general?.title ?? titleParam ?? "Test"}
				/>
			)}
			<Layout style={{ height: "100%" }}>
				<Layout.Header
					style={{ backgroundColor: "transparent", height: "auto" }}
				>
					<Flex
						align="flex-end"
						justify="space-between"
						style={{ height: "100%", border: "0px solid #000" }}
					>
						<Space
							direction="vertical"
							style={{ margin: "1rem 0px 0px" }}
						>
							{!testProperties.isFromToken && (
								<Breadcrumb
									separator={
										<RightOutlined
											style={{
												color: "#bbb",
												fontSize: 12,
											}}
										/>
									}
									style={{ border: "0px solid #000" }}
									items={[
										{
											key: "home",
											title: <Link to="/">Home</Link>,
										},
										{
											key: "tests",
											title: (
												<Link to="/tests">Tests</Link>
											),
										},
										{
											key: "test",
											title: (
												<Typography.Text
													ellipsis={true}
													style={{ maxWidth: 340 }}
												>
													{testTitle}
												</Typography.Text>
											),
										},
									]}
								/>
							)}
							<Flex
								align="center"
								gap="small"
								style={{ marginBottom: 8 }}
							>
								<Typography.Title
									level={2}
									ellipsis={true}
									style={{
										color: "#de6834",
										marginBottom: 0,
										maxWidth: 600,
									}}
								>
									{testTitle}
								</Typography.Title>
								{!isTestBeingCreated &&
								testTabsData.access?.isNotPrivate ? (
									<Button
										size="large"
										type="text"
										icon={<LinkOutlined />}
										onClick={copyLink}
									></Button>
								) : (
									!isTestBeingCreated && (
										<Typography.Text
											style={{
												color: "#de6834",
												marginTop: "12px",
											}}
										>
											(private)
										</Typography.Text>
									)
								)}
							</Flex>
						</Space>
						{!testProperties.isTemplate &&
							!testProperties.isFromToken && (
								<Flex
									gap={8}
									style={{
										display: isTestBeingCreated
											? "none"
											: "",
									}}
								>
									<Button
										type="default"
										size="middle"
										icon={<UsergroupAddOutlined />}
										onClick={() =>
											setIsInviteSideBarOpen(true)
										}
									>
										Invite
									</Button>
									{testTabsData.access?.isNotPrivate && (
										<Button
											type="default"
											size="middle"
											icon={<UserAddOutlined />}
											onClick={() =>
												setIsAllowedInviteOpen(true)
											}
										>
											Allow
										</Button>
									)}
									{canShareTest && (
										<Button
											type="default"
											size="middle"
											icon={<ShareAltOutlined />}
											onClick={() =>
												setIsShareTestOpen(true)
											}
										>
											Share
										</Button>
									)}
									{testTabsData.proctoring &&
										"isLiveStreamEnabled" in
											testTabsData.proctoring &&
										testTabsData.proctoring
											.isLiveStreamEnabled &&
										"onlySuspiciousRoom" in
											testTabsData.proctoring && (
											<Button
												type="default"
												size="middle"
												icon={<EyeOutlined />}
												iconPosition="start"
												href={`${AppConfig.quizServerURL}/meeting/invigilate/${testId}`}
												target="_blank"
												rel="noopener noreferrer"
											>
												Invigilate
											</Button>
										)}

									{hasResourcePermission(
										RoleResource.QUIZ,
										RoleAction.ALLOW_ALL_TEST_SUBMISSION
									) && (
										<Button
											type="default"
											size="middle"
											iconPosition="start"
											onClick={() =>
												setIsFinishTestLogsOpen(true)
											}
										>
											Finish Test Logs
										</Button>
									)}
								</Flex>
							)}
						{testProperties.isFromToken && (
							<Button
								type="default"
								size="middle"
								icon={<CopyOutlined />}
								onClick={() => {
									if (isAlreadyCloned) {
										confirm({
											title: "Test is already cloned. Want to continue?",
											type: "confirm",
											onOk() {
												setCloneQuizToken(token);
											},
										});
									} else {
										setCloneQuizToken(token);
									}
								}}
							>
								Clone Quiz
							</Button>
						)}
					</Flex>
				</Layout.Header>
				<Layout.Content>
					{messageContext}
					<Tabs
						destroyInactiveTabPane
						style={{ height: "100%", overflow: "auto" }}
						activeKey={activeKey}
						onChange={setActiveKey}
						items={tabItems}
						renderTabBar={(props, DefaultTabBar) => (
							<StickyBox
								offsetTop={0}
								offsetBottom={0}
								style={{ zIndex: 1 }}
							>
								<DefaultTabBar
									{...props}
									style={{
										paddingLeft: "48px",
										marginBottom: 0,
										backgroundColor: "#f0f0f0",
									}}
								/>
							</StickyBox>
						)}
					/>
				</Layout.Content>
				{isInviteSideBarOpen && (
					<InviteSidebar
						onClose={() => setIsInviteSideBarOpen(false)}
						testTitle={testTitle ?? "test"}
						isInviteSideBarOpen={isInviteSideBarOpen}
						startTime={testTabsData?.access?.startTime}
						endTime={testTabsData?.access?.endTime}
						entryStopTime={testTabsData?.access?.entryStopTime}
					/>
				)}
				{isAllowedInviteOpen && (
					<AllowedInvite
						onClose={() => setIsAllowedInviteOpen(false)}
						setFailedEmails={setFailedEmails}
						isAllowedInviteOpen={isAllowedInviteOpen}
						quizId={testId || "1234"}
					/>
				)}
				{isShareTestOpen && (
					<ShareTest
						onClose={() => setIsShareTestOpen(false)}
						isShareTestOpen={isShareTestOpen}
						quizId={testId}
					/>
				)}
				{failedEmails.length > 0 && (
					<InValidBulkUpload
						data={failedEmails}
						clearFailedData={closeFailedEmailModal}
						columns={columns}
						title="Invalid Emails"
						description="Below are the emails which do not follow correct email
						syntax"
					/>
				)}
				{isFinishTestLogsOpen && (
					<FinishTestLogs
						onClose={() => setIsFinishTestLogsOpen(false)}
						id={testId}
						isFinishTestLogsOpen={isFinishTestLogsOpen}
					/>
				)}
				{cloneQuizToken && testTabsData.content && (
					<CloneQuiz
						token={cloneQuizToken}
						onClose={handleCloneClose}
						onSubmit={handleCloneSubmit}
						randomizeQuestion={
							testTabsData.content.shuffleQuestions
						}
						poolQuestion={testTabsData.content.randomQuestions}
					/>
				)}
			</Layout>
		</>
	);
}
