import {
	<PERSON><PERSON>,
	<PERSON><PERSON>,
	InputNumber,
	Layout,
	message,
	Pagination,
	Row,
	Space,
	TableColumnsType,
	TablePaginationConfig,
	Tag,
	theme,
	Typography,
} from "antd";
import { Flex, Modal } from "antd";
import { Key, useCallback, useEffect, useMemo, useState } from "react";
import { CloseOutlined } from "@ant-design/icons";
import { DataTable } from "../dataTable/dataTable";
import { Tooltip } from "antd";
import {
	QuestionDificulty,
	QuestionType,
	TestContentQuestionType,
} from "@/client/test-add";
import { Content, Header } from "antd/es/layout/layout";
import Search from "antd/es/input/Search";
import { FilterIcon } from "lucide-react";
import Sider from "antd/es/layout/Sider";
import FilterMenu, { FilterMenuItem } from "@/components/filterMenu/filterMenu";
import { quizClient, useAppStore } from "../../store/";
import { UserRole } from "@/constants/roles";
import { AppConfig } from "@/config";
import { PlatformOrganisation } from "@/constants";
import { SortOrder } from "antd/es/table/interface";
import QuestionPreview from "../question-preview/QuestionPreview";
import { RandomIcon } from "../icons/random";
import useLocalStorageState from "@/localStorageState";
const langCodeToLangStringMapping: { [key: string]: string } = {
	"0": "Python 2",
	"1": "Ruby",
	"2": "React-Jest",
	"3": "Php",
	"4": "JavaScript",
	"5": "SQL",
	"7": "C",
	"8": "Java",
	"10": "C#",
	"11": "Bash",
	"17": "C99",
	"18": "Cpp11",
	"19": "Cpp14",
	"20": "Python 3",
	"23": "MySql",
	"24": "Oracle",
	"77": "Cpp",
};

const questionTypeMapping: { [key: string]: string } = {
	[QuestionType.MCQ]: "MCQ",
	[QuestionType.SUBJECTIVE]: "Subjective",
	[QuestionType.CODING]: "Coding",
	[QuestionType.MULTIPLE]: "MQ",
	[QuestionType.WEB]: "Web",
};

export type Question = {
	_id: string;
	title: string;
	tags: string;
	createdBy: { _id: string; email: string; displayname: string };
	isPublic?: boolean;
	score: number;
	parentIdOfCreator?: string;
	isLocked?: boolean;
} & (
	| {
			type:
				| QuestionType.MCQ
				| QuestionType.SUBJECTIVE
				| QuestionType.MULTIPLE;
			questionTypeWeb?: unknown;
			questionTypeCoding?: {
				testCase: object[];
				codeproglang: {
					language: string;
				}[];
			};
	  }
	| {
			type: QuestionType.CODING;
			questionTypeCoding?: {
				testCase: object[];
				codeproglang: {
					language: string;
				}[];
			};
			questionTypeWeb?: unknown;
	  }
	| {
			type: QuestionType.WEB;
			questionTypeWeb: {
				testCase?: {
					sampleTest: boolean;
					evaluator: string;
					description: string;
					_id: string;
					scoreip: number;
				}[];
				testRunnerFileContent: string;
				testRunnerNeeded: boolean;
				reactRendererCode: string;
				isReactQuestion: boolean;
				isJsAllowed: boolean;
				isCssAllowed: boolean;
				isHtmlAllowed: boolean;
				js: string;
				css: string;
				html: string;
			};
			questionTypeCoding?: unknown;
	  }
);

interface AddQuestionModalProps {
	open?: boolean;
	toggle: (_value: boolean) => void;
	ignoredKeys?: Key[];
	onAccept?: (
		_questions: TestContentQuestionType[],
		_poolCount: number
	) => void;
	languagesAllowed?: string[];
	quizId?: string;
	poolCount?: number;
	questionsCount?: number;
	isPoolQuestion?: boolean;
}

type Filters = {
	keywords?: string[];
	CreatedBy?: string[];
	dropdown?: string;
	questionType?: string;
};

const options = [
	{ value: 1, label: "My Questions" },
	{ value: 2, label: "My Organization" },
	{ value: 3, label: "Others" },
	{ value: 4, label: "CodeQuotient" },
];

const questionTypeOptions = [
	{ value: 0, label: "All" },
	{ value: 4, label: "Coding" },
	{ value: 1, label: "MCQ" },
	{ value: 5, label: "Multiple Questions" },
	{ value: 9, label: "Web" },
	{ value: 2, label: "Subjective" },
];

const mapFieldName = (dropDownValue: number): string => {
	const mapping: { [key: number]: string } = {
		1: "myList",
		2: "myOrgList",
		3: "otherList",
		4: "cqList",
	};
	return mapping[dropDownValue] ?? "myList";
};
const codingQuestionDefaultSettings: TestContentQuestionType["settings"] = {
	contentHead: true,
	contentTail: true,
	customInput: true,
	result: "0",
};

function AddQuestionModal(props: AddQuestionModalProps) {
	const {
		open,
		toggle: setOpen,
		poolCount,
		questionsCount,
		isPoolQuestion,
	} = props;

	const { user, session } = useAppStore();
	const [selectedRows, setSelectedRows] = useState<Question[]>([]);
	const [poolCountState, setPoolCountState] = useState<{
		poolCount: number;
		questionsCount: number;
		isPoolQuestion: boolean;
	}>({
		poolCount: poolCount || 0,
		questionsCount: questionsCount || 0,
		isPoolQuestion: isPoolQuestion || false,
	});
	const [selectedRowKeys, setSelectedRowKeys] = useState<Key[]>([]);
	const [showPreviewModal, setShowPreviewModal] = useState<Question>();
	const [collapsed, setCollapsed] = useState<boolean>(true);
	const [loading, setLoading] = useState(false);
	const [tableData, setTableData] = useState<Question[]>([]);
	const [tableState, setTableState] = useLocalStorageState<{
		searchValue: string;
		totalRecords: number;
		keywords: string[];
		dropDownValue: number;
		questionType: number;
		createdBy: string[];
		currentPage: number;
		sorting: { order?: string; field?: string };
		pageSize: number;
	}>(
		"addQuestionModal",
		{
			searchValue: "",
			totalRecords: 0,
			keywords: [],
			dropDownValue: 1,
			questionType: 0,
			createdBy: [],
			currentPage: 1,
			pageSize: 10,
			sorting: {},
		},
		open
	);

	const newOptions = useMemo(() => {
		const havePermission = options.find(
			item => item.value === tableState.dropDownValue
		);
		if (!havePermission) {
			setTableState({
				...tableState,
				dropDownValue: 1,
			});
		}

		if (
			user?.role.id !== UserRole.ADMIN &&
			AppConfig.platformOrganisation === PlatformOrganisation.CHITKARA
		) {
			const newData = options.filter(item => item.value !== 3);
			if (!session?.allowAdminContent) {
				return newData.filter(item => item.value !== 4);
			}
			return newData.map(item =>
				item.value === 4 ? { ...item, label: "Chitkara" } : item
			);
		} else if (
			user?.role.id !== UserRole.ADMIN &&
			AppConfig.platformOrganisation === PlatformOrganisation.CQ
		) {
			const newData = options.filter(item => item.value !== 3);
			if (!session?.allowAdminContent) {
				return newData.filter(item => item.value !== 4);
			}
			return newData;
		} else {
			return options.filter(item => item.value !== 4);
		}
	}, [user?.role]);

	const [messageInstance, contextHolder] = message.useMessage({
		maxCount: 1,
	});

	const [searchTerm, setSearchTerm] = useState<string>(
		tableState.searchValue
	);
	const [debouncedSearchTerm, setDebouncedSearchTerm] = useState<string>(
		tableState.searchValue
	);

	useEffect(() => {
		setSearchTerm(tableState.searchValue);
	}, [tableState.searchValue]);

	useEffect(() => {
		const handler = setTimeout(() => {
			setDebouncedSearchTerm(searchTerm);
		}, 300);

		return () => {
			clearTimeout(handler);
		};
	}, [searchTerm]);

	useEffect(() => {
		setTableState(prev => ({
			...prev,
			searchValue: debouncedSearchTerm,
			currentPage: 1,
		}));
	}, [debouncedSearchTerm]);

	const [keywordData, setkeywordData] = useState<{
		keywords: {
			_id: string;
			tag: string;
		}[];
		createdBy: {
			_id: string;
			displayname: string;
		}[];
	}>({
		keywords: [],
		createdBy: [],
	});

	function onRowSelection(
		_selectedRowKeys: Key[],
		_selectedRows: Question[]
	) {
		const currentPageIds = tableData.map(row => row._id);
		const filteredPrevKeys = selectedRowKeys.filter(
			key => !currentPageIds.includes(key.toString())
		);
		const filteredPrevRows = selectedRows.filter(
			row => !currentPageIds.includes(row._id)
		);

		const mergedKeys = [...filteredPrevKeys, ..._selectedRowKeys];
		const mergedRows = [...filteredPrevRows, ..._selectedRows];

		setSelectedRowKeys(mergedKeys);
		setSelectedRows(mergedRows);

		const prevCount = selectedRows.length;
		const newCount = mergedRows.length;

		setPoolCountState(prev => ({
			...prev,
			questionsCount: prev.questionsCount - prevCount + newCount,
		}));
	}

	const columns: TableColumnsType<Question> = [
		{
			key: "serialNumber",
			render: (_value, _record, index) => {
				return `${index + 1 + (tableState.currentPage - 1) * tableState.pageSize}.`;
			},
			width: "10%",
		},
		{
			title: "Title",
			dataIndex: "title",
			key: "title",
			width: "25%",
			sorter: true,
			showSorterTooltip: false,
			sortOrder:
				tableState.sorting?.field === "title"
					? (tableState.sorting.order as SortOrder)
					: undefined,
			ellipsis: true,
			render: (_text, record) => (
				<Tooltip title={"Preview"}>
					<Button
						type="link"
						onClick={() => setShowPreviewModal(record)}
					>
						{record.title}
					</Button>
				</Tooltip>
			),
		},
		{
			title: "Type",
			dataIndex: "type",
			key: "type",
			render: value => questionTypeMapping[value] ?? "NA",
		},
		{
			title: "Test cases",
			align: "center",
			dataIndex: ["questionTypeCoding", "testCase"],
			render: (_, record) => {
				if (
					record.type === QuestionType.CODING ||
					record.type === QuestionType.MULTIPLE
				) {
					return record.questionTypeCoding?.testCase?.length ?? 0;
				} else if (record.type === QuestionType.WEB) {
					return record.questionTypeWeb?.testCase?.length ?? 0;
				} else {
					return "-NA-";
				}
			},
		},
		{
			title: "Created by",
			dataIndex: ["createdBy", "displayname"],
			key: "createdBy",
		},
		{
			title: "Languages",
			key: "language",
			render: (_, record) => {
				if (record.type !== QuestionType.CODING) {
					return "-NA-";
				}
				const languages = record.questionTypeCoding?.codeproglang
					?.map(
						language =>
							langCodeToLangStringMapping[language.language]
					)
					.join(", ");
				return (
					<Typography.Text ellipsis={true} title={languages}>
						{record.questionTypeCoding?.codeproglang?.map(
							(langObj: { language: string }) => (
								<Tag key={langObj.language}>
									{
										langCodeToLangStringMapping[
											langObj.language
										]
									}
								</Tag>
							)
						) || "-NA-"}
					</Typography.Text>
				);
			},
		},
		{
			title: "Score",
			dataIndex: "score",
			key: "score",
			sorter: true,
			showSorterTooltip: false,
			sortOrder:
				tableState.sorting?.field === "score"
					? (tableState.sorting.order as SortOrder)
					: undefined,
		},
	];

	function handleDelete(id: string) {
		setSelectedRows(selectedRows.filter(row => row._id !== id));
		setSelectedRowKeys(selectedRowKeys.filter(key => key !== id));
	}

	function handlePageChange(page: number, pageSize?: number) {
		setTableState(prev => {
			return {
				...prev,
				currentPage: page,
				pageSize: pageSize || prev.pageSize,
			};
		});
	}

	useEffect(() => {
		if (!open) return;
		getQuestions();
	}, [
		tableState.keywords,
		tableState.searchValue,
		tableState.createdBy,
		tableState.dropDownValue,
		tableState.questionType,
		tableState.pageSize,
		tableState.currentPage,
		tableState.sorting,
		open,
	]);

	const fetchFiltersData = useCallback(async () => {
		try {
			const res = await quizClient.getFilterData("questionId");
			if (!res) {
				messageInstance.error("Failed to fetch filter data.");
				return;
			}
			if ("error" in res) {
				messageInstance.error("Something went wrong");
			} else {
				setkeywordData({
					keywords: res.keywords,
					createdBy: res.users,
				});
			}
		} catch (err) {
			console.log(err);
			messageInstance.error(err as string);
		}
	}, [messageInstance]);

	useEffect(() => {
		fetchFiltersData();
	}, [fetchFiltersData]);

	useEffect(() => {
		setPoolCountState({
			poolCount: poolCount ?? 0,
			questionsCount: questionsCount ?? 0,
			isPoolQuestion: isPoolQuestion ?? false,
		});
	}, [isPoolQuestion, poolCount, questionsCount]);

	async function getQuestions() {
		setLoading(true);
		try {
			const { order, field } = tableState.sorting || {};
			const payload = {
				[mapFieldName(tableState.dropDownValue)]: 1,
				length: tableState.pageSize,
				start: (tableState.currentPage - 1) * tableState.pageSize,
				search: { value: tableState.searchValue.trim() },
				sorting: {
					order:
						order === "ascend"
							? "asc"
							: order === "descend"
								? "dsc"
								: undefined,
					column: field,
				},
				questionType:
					tableState.questionType === 0
						? undefined
						: tableState.questionType,
				keywords: tableState.keywords,
				fltrCreatedBy: tableState.createdBy,
				contentIdList: props.ignoredKeys,
				languagesAllowed: props.languagesAllowed,
				quizId: props.quizId,
			};
			const response = await quizClient.getQuestions(payload);
			if ("error" in response) {
				messageInstance.error(response.error);
			} else {
				setTableData(response.data);
				setTableState(prev => {
					return {
						...prev,
						totalRecords: response.recordsFiltered,
					};
				});
			}
		} catch (ex) {
			console.log(ex);
			messageInstance.error(ex as string);
		} finally {
			setLoading(false);
		}
	}

	function onAdd() {
		if (isPoolQuestion && poolCountState.poolCount < 1) {
			messageInstance.error("Pool count must be greater than 0");
			return;
		}
		if (
			isPoolQuestion &&
			poolCountState.questionsCount < poolCountState.poolCount
		) {
			messageInstance.error(
				"Pool count cannot be greater than questions count"
			);
			return;
		}
		props.onAccept?.(
			selectedRows.map<TestContentQuestionType>(question => {
				const commonProperties = {
					_id: question._id,
					description: "",
					dificulty: QuestionDificulty.EASY,
					score: question.score,
					title: question.title,
					createdBy: question.createdBy.displayname,
					orgId: question.createdBy._id,
				};
				switch (question.type) {
					case QuestionType.MCQ:
						return {
							...commonProperties,
							type: QuestionType.MCQ,
						};

					case QuestionType.SUBJECTIVE:
						return {
							...commonProperties,
							type: QuestionType.SUBJECTIVE,
						};

					case QuestionType.CODING:
						return {
							...commonProperties,
							type: QuestionType.CODING,
							settings: codingQuestionDefaultSettings,
						};

					case QuestionType.MULTIPLE:
						return {
							...commonProperties,
							type: QuestionType.MULTIPLE,
						};

					case QuestionType.WEB:
						return {
							...commonProperties,
							type: QuestionType.WEB,
						};
				}
			}),
			poolCountState.poolCount
		);
		setSelectedRows([]);
		setSelectedRowKeys([]);
		setTableState(prev => ({
			...prev,
			currentPage: 1,
		}));
		setOpen(false);
		console.log(selectedRows, "table state updated");
	}

	const handleFiltersChange = (items: Filters) => {
		console.log(items);
		setTableState(prev => {
			return {
				...prev,
				currentPage: 1,
				dropDownValue:
					Number(items.dropdown) || tableState.dropDownValue,
				keywords: items.keywords || tableState.keywords,
				createdBy: items.CreatedBy || tableState.createdBy,
				questionType:
					items.questionType === undefined
						? tableState.questionType
						: Number(items.questionType),
			};
		});
	};

	const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
		setSearchTerm(e.target.value);
	};

	const handleSearchClear = () => {
		setSearchTerm("");
	};

	const handleFilterReset = () => {
		setTableState(prev => ({
			...prev,
			keywords: [],
			createdBy: [],
			questionType: 0,
			dropDownValue: 1,
			currentPage: 1,
			searchValue: "",
			sorting: {},
		}));
		setSearchTerm("");
	};

	const handleTableChange = (
		_pagination: TablePaginationConfig,
		_filter: unknown,
		sorting: unknown
	) => {
		console.log("table change called", sorting);
		setTableState(prev => {
			return {
				...prev,
				sorting: sorting as { order?: string; field?: string },
			};
		});
	};

	const handleDropdownChange = (value: number) => {
		setDataFilter(prevFilters =>
			prevFilters.map(filter => {
				if (filter.key === "dropdown" && filter.type === "select") {
					return { ...filter, value };
				}
				return filter;
			})
		);
		if (value === 1) {
			setDataFilter(prevFilters =>
				prevFilters.map(filter =>
					filter.key === "CreatedBy"
						? { ...filter, disabled: true, defaultValue: [] }
						: filter
				)
			);
		} else {
			setDataFilter(prevFilters =>
				prevFilters.map(filter =>
					filter.key === "CreatedBy"
						? {
								...filter,
								disabled: false,
								defaultValue: tableState.createdBy,
							}
						: filter
				)
			);
		}
	};

	const handleQuestionTypeChange = (value: number) => {
		setDataFilter(prevFilters =>
			prevFilters.map(filter => {
				if (filter.key === "questionType" && filter.type === "select") {
					return { ...filter, value };
				}
				return filter;
			})
		);
	};

	const [filterData, setDataFilter] = useState<FilterMenuItem[]>([
		{
			type: "select",
			key: "dropdown",
			title: "dropdown",
			items: newOptions,
			value: tableState.dropDownValue,
			onChange: handleDropdownChange,
		},
		{
			type: "collapse",
			key: "keywords",
			title: "keywords",
			options: keywordData?.keywords?.map(val => ({
				value: val.tag,
				label: val.tag,
			})),
			defaultValue: tableState.keywords,
			disabled: false,
		},
		{
			type: "collapse",
			key: "CreatedBy",
			title: "Created By",
			options: keywordData?.createdBy?.map(val => ({
				value: val._id,
				label: val.displayname,
			})),
			defaultValue: tableState.createdBy,
			disabled: tableState.dropDownValue === 1,
		},
		{
			type: "select",
			key: "questionType",
			title: "Question Type",
			items: questionTypeOptions,
			value: tableState.questionType,
			onChange: handleQuestionTypeChange,
		},
	]);

	const handleClose = () => {
		setSelectedRowKeys([]);
		setSelectedRows([]);
		// setTableState({
		// 	currentPage: 1,
		// 	pageSize: 10,
		// 	totalRecords: 0,
		// 	searchValue: "",
		// 	keywords: [],
		// 	createdBy: [],
		// 	dropDownValue: 1,
		// 	questionType: 0,
		// 	sorting: {},
		// });
		setCollapsed(true);
		setOpen(false);
		setPoolCountState({
			questionsCount: questionsCount ?? 0,
			poolCount: poolCount ?? 0,
			isPoolQuestion: isPoolQuestion ?? false,
		});
	};

	useEffect(() => {
		setDataFilter([
			{
				type: "select",
				key: "dropdown",
				title: "dropdown",
				items: newOptions,
				value: tableState.dropDownValue,
				onChange: handleDropdownChange,
			},
			{
				type: "collapse",
				key: "keywords",
				title: "keywords",
				options: keywordData?.keywords?.map(val => ({
					value: val.tag,
					label: val.tag,
				})),
				defaultValue: tableState.keywords,
				disabled: false,
			},
			{
				type: "collapse",
				key: "CreatedBy",
				title: "Created By",
				options: keywordData?.createdBy?.map(val => ({
					value: val._id,
					label: val.displayname,
				})),
				defaultValue: tableState.createdBy,
				disabled: tableState.dropDownValue === 1,
			},
			{
				type: "select",
				key: "questionType",
				title: "Question Type",
				items: questionTypeOptions,
				value: tableState.questionType,
				onChange: handleQuestionTypeChange,
			},
		]);
	}, [tableState]);

	const {
		token: { colorBgContainer },
	} = theme.useToken();

	return (
		<>
			<Modal
				title="Add Questions"
				onCancel={() => handleClose()}
				open={open}
				width={"90vw"}
				destroyOnHidden
				cancelButtonProps={{
					style: {
						border: "none",
						boxShadow: "none",
						color: "#dd6833",
					},
				}}
				styles={{
					footer: {
						position: "sticky",
						bottom: 0,
						backgroundColor: "white",
						margin: 0,
					},
					body: {
						height: "65vh",
					},
				}}
				footer={[
					<Flex key="footer" justify="space-between">
						<div style={{ width: "100%", padding: "0.75em" }}>
							<Pagination
								align="center"
								total={tableState.totalRecords}
								pageSize={tableState.pageSize}
								current={tableState.currentPage}
								onChange={handlePageChange}
								showSizeChanger
								onShowSizeChange={handlePageChange}
							/>
						</div>
						<Flex gap={10} style={{ padding: "0.75em" }}>
							{poolCountState.isPoolQuestion && (
								<InputNumber
									addonBefore={<RandomIcon />}
									value={poolCountState.poolCount}
									min={0}
									onChange={value =>
										setPoolCountState(prev => ({
											...prev,
											poolCount: Number(value),
										}))
									}
									style={{ width: "6em", marginRight: "3em" }}
								/>
							)}
							<Button key="back" onClick={() => handleClose()}>
								Cancel
							</Button>
							<Button
								key="submit"
								type="primary"
								disabled={selectedRows.length <= 0}
								onClick={onAdd}
							>
								Add
							</Button>
						</Flex>
					</Flex>,
				]}
			>
				<Layout style={{ height: "100%", width: "100%" }}>
					{contextHolder}
					<Sider
						width="20%"
						collapsed={collapsed}
						collapsedWidth={0}
						theme="light"
						style={{
							overflow: "hidden auto",
						}}
					>
						<FilterMenu
							onChange={handleFiltersChange}
							componentsData={filterData}
							onReset={handleFilterReset}
							onClose={() => setCollapsed(true)}
						></FilterMenu>
					</Sider>
					<Layout style={{ background: "white" }}>
						<Header
							style={{
								backgroundColor: colorBgContainer,
								padding: 0,
							}}
						>
							<Flex
								style={{ height: "100%", padding: "1em" }}
								justify="space-between"
							>
								<Space align="center" size={"large"}>
									<Button
										type={collapsed ? "default" : "primary"}
										onClick={() =>
											setCollapsed(prev => !prev)
										}
										icon={
											collapsed ? (
												<Badge
													color="#de6834"
													size="small"
													offset={[2, 1]}
													count={
														(tableState.keywords
															.length > 0
															? 1
															: 0) +
														(tableState.createdBy
															.length > 0
															? 1
															: 0) +
														(tableState.questionType !=
														0
															? 1
															: 0) +
														(tableState.dropDownValue !==
														1
															? 1
															: 0)
													}
												>
													<FilterIcon
														size={16}
														style={{
															paddingTop:
																"0.15rem",
														}}
													/>
												</Badge>
											) : (
												<CloseOutlined size={14} />
											)
										}
									>
										Filters
									</Button>
									<Row>
										<Search
											size="middle"
											placeholder="Search questions..."
											allowClear
											value={searchTerm}
											loading={loading}
											onInput={handleSearch}
											onClear={handleSearchClear}
										/>
									</Row>
								</Space>
							</Flex>
						</Header>
						<Content>
							<Flex
								justify="space-evenly"
								style={{ height: "100%", overflow: "auto" }}
							>
								<div
									style={{
										width: "76%",
										borderRight: "1px solid #00000040",
										paddingRight: "20px",
										height: "100%",
										overflow: "auto",
									}}
								>
									<DataTable
										pagination={false}
										data={tableData}
										enableRowSelection={true}
										columns={columns}
										totalRecords={tableState.totalRecords}
										pageSize={tableState.pageSize}
										currentPage={tableState.currentPage}
										isServerSide={false}
										onTableChange={handleTableChange}
										onRowSelection={onRowSelection}
										loading={loading}
										selectedKeys={selectedRowKeys}
									/>
								</div>

								<div
									style={{
										width: "20%",
										overflowY: "auto",
										height: "100%",
										scrollbarWidth: "thin",
									}}
								>
									<Flex vertical>
										<Typography.Text strong>
											{selectedRows?.length || 0}{" "}
											questions selected
										</Typography.Text>
										<Flex
											vertical
											gap={10}
											style={{ marginTop: "10px" }}
										>
											{selectedRows.map((row, index) => (
												<Flex
													key={row._id}
													justify="space-between"
													align="center"
													gap={10}
													style={{
														marginRight: "10px",
													}}
												>
													<Flex gap={5}>
														<Typography.Text
															style={{
																color: "#888888",
															}}
														>
															{index + 1}.
														</Typography.Text>
														{row.title.length > 30
															? row.title.substr(
																	0,
																	25
																) + "..."
															: row.title}
													</Flex>
													<Tooltip
														placement="top"
														title={"Remove"}
													>
														<CloseOutlined
															onClick={() =>
																handleDelete(
																	row._id
																)
															}
															style={{
																fontSize:
																	"14px",
																color: "#00000060",
															}}
														/>
													</Tooltip>
												</Flex>
											))}
										</Flex>
									</Flex>
								</div>
							</Flex>
						</Content>
					</Layout>
				</Layout>
			</Modal>
			{showPreviewModal && (
				<PreviewModalAddQuestion
					record={showPreviewModal}
					onClose={() => setShowPreviewModal(undefined)}
				/>
			)}
		</>
	);
}

interface PreviewModalAddQuestionProps {
	record: Question;
	onClose: () => void;
}

const PreviewModalAddQuestion: React.FC<PreviewModalAddQuestionProps> = ({
	record,
	onClose,
}) => {
	const [selectedQuestionDescription, setSelectedQuestionDescription] =
		useState<{ title: string; testCase?: number } | null>(null);

	const setDescriptionFunc = useCallback(async () => {
		try {
			const response = await quizClient.getDescription(record._id);
			setSelectedQuestionDescription(response);
		} catch (err) {
			console.error(err);
		}
	}, [record._id]);

	useEffect(() => {
		setDescriptionFunc();
	}, [record, setDescriptionFunc]);

	let testcases = 0;
	if (record.type === QuestionType.CODING) {
		testcases = record.questionTypeCoding?.testCase?.length ?? 0;
	} else if (record.type === QuestionType.WEB) {
		testcases = record.questionTypeWeb?.testCase?.length ?? 0;
	} else if (record.type === QuestionType.MULTIPLE) {
		testcases = record.questionTypeCoding?.testCase?.length ?? 0;
	}

	return (
		<QuestionPreview
			title={record.title}
			count={testcases}
			description={selectedQuestionDescription?.title ?? ""}
			type={record.type}
			id={record._id}
			onClose={() => onClose()}
		/>
	);
};

export default AddQuestionModal;
