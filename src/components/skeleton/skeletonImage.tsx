import { Flex, Image, Skeleton } from "antd";
import skeletonImageStyle from "./skeleton-image.module.css";
import { useEffect, useRef } from "react";

const SkeletonImage = ({
	src,
	imageUrl,
	width = "100%",
	height = 160,
	onLoad,
	onClick,
}: {
	src?: string;
	imageUrl?: string;
	width?: string | number;
	height?: string | number;
	fileName?: string;
	onLoad: (_src: string) => void;
	onClick?: () => void;
}) => {
	const fallbackImage = "/images/unavailable-image.png";
	const handleOnLoadRef = useRef(onLoad);

	useEffect(() => {
		if (typeof src === "string") {
			return () => {};
		}
		const controller = new AbortController();
		(async function loadImage() {
			try {
				if (imageUrl === undefined) {
					handleOnLoadRef.current(fallbackImage);
					return;
				}
				const response = await fetch(imageUrl, {
					signal: controller.signal,
				});
				const imageSrc = await response.text();
				handleOnLoadRef.current(imageSrc);
			} catch (error) {
				console.error(error);
			}
		})();
		return () => {
			controller.abort();
		};
	}, [imageUrl, src]);

	return (
		<Flex align="center" style={{ width, height }}>
			<Image
				src={src}
				style={{
					maxWidth: "100%",
					display: src ? "initial" : "none",
				}}
				onClick={onClick}
			/>
			{src === undefined && (
				<Skeleton.Image
					active
					style={{
						width,
						height: "160px",
					}}
					rootClassName={skeletonImageStyle.rootElement}
				/>
			)}
		</Flex>
	);
};

export default SkeletonImage;
