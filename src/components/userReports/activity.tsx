import { useState } from "react";
import InnerTab, { InnerTabItem } from "./innerTab";
import { useParams } from "react-router";
import FullScreen from "./activityTables/fullScreen";
import TabSwitch from "./activityTables/tabSwitch";
import LoginCount from "./activityTables/loginCount";

type ActivityTabKey = "tabSwitch" | "loginCount" | "fullScreen";

const Activity = ({
	loginCount,
	fullScreenInCount,
	fullScreenOutCount,
}: {
	loginCount: number;
	fullScreenInCount: number;
	fullScreenOutCount: number;
}) => {
	console.log(fullScreenInCount, fullScreenOutCount);
	const { id, userId } = useParams();
	// const [loading, setLoading] = useState(false);
	const [activeTabKey, setActiveTabKey] =
		useState<ActivityTabKey>("tabSwitch");
	const [tabSwitchDataLength, setTabSwitchDataLength] = useState<number>();
	const [loginCountDataLength, setLoginCountDataLength] =
		useState<number>(loginCount);
	const [fullScreenDataLength, setFullScreenDataLength] = useState<number>(
		+fullScreenInCount + +fullScreenOutCount
	);

	const tabsItems: InnerTabItem<ActivityTabKey>[] = [
		{
			title: "Tab Switch",
			key: "tabSwitch",
			count: tabSwitchDataLength,
			children: (
				<>
					<TabSwitch
						testId={id}
						userId={userId}
						setTabSwitchDataLength={(length: number) =>
							setTabSwitchDataLength(length)
						}
					/>
				</>
			),
		},
		{
			title: "Full Screen",
			key: "fullScreen",
			count: fullScreenDataLength,
			children: (
				<>
					<FullScreen
						testId={id}
						userId={userId}
						setFullScreenDataLength={(length: number) =>
							setFullScreenDataLength(length)
						}
					/>
				</>
			),
		},
		{
			title: "Login Count",
			key: "loginCount",
			count: loginCountDataLength,
			children: (
				<>
					<LoginCount
						testId={id}
						userId={userId}
						setLoginCountDataLength={(length: number) =>
							setLoginCountDataLength(length)
						}
					/>
				</>
			),
		},
	];

	return (
		<div>
			<InnerTab<ActivityTabKey>
				activeTabKey={activeTabKey}
				tabsItems={tabsItems}
				onTabChange={setActiveTabKey}
			/>
		</div>
	);
};

export default Activity;
