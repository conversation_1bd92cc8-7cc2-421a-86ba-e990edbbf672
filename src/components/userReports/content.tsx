import { useState } from "react";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,
	Modal,
	Space,
	Table,
	Tag,
	Typography,
	Tooltip,
	Flex,
	InputNumber,
	Empty,
} from "antd";
import timeDate from "@/utils/timeDate";
import { ColumnsType } from "antd/es/table";
import {
	EditOutlined,
	EyeOutlined,
	FileDoneOutlined,
	SaveOutlined,
} from "@ant-design/icons";
import { AttemptData, UserAttempt } from "@/testReport/data/data";
import {
	QuestionType,
	QuestionTypesMap,
	PostEndPointsMap,
	EndPointRequestBody,
	ScoreUpdationActivityResponse,
	ScoreUpdationActivityListDataType,
} from "@/client/test-add";

import { AppConfig } from "@/config";
import QuestionPreview from "../question-preview/QuestionPreview";
import { email } from "@/constants/email";
import { quizClient, useAppStore } from "../../store/index";
import { useAppMessage } from "@/hooks/message";
import { useParams } from "react-router";
import { DataTable } from "../dataTable/dataTable";
import { UserRole } from "@/constants/roles";

const { Text } = Typography;

type UserDataType = UserAttempt & {
	fromRedisData?: {
		quizId?: string;
		userId?: string;
		questionId?: string;
	};
};

type AttemptDataType = AttemptData & {
	fromRedisData?: {
		quizId?: string;
		userId?: string;
		questionId?: string;
	};
};

export interface TestSectionQuestion {
	_id: string;
	score: number;
	title: string;
	type: string;
	outOfTotalScore: number;
	status: "submitted" | "Executed" | "Not Submitted";
	userAttempts: UserDataType[];
	options?: string[];
	correctAnswer?: string;
	description: string;
	testCaseCount: number;
}

export interface QuizSegmentsDataType {
	_id: string;
	title: string;
	questions: TestSectionQuestion[];
	userScore: number;
	segementTotalScore: number;
	status: "submitted" | "not submitted";
}

type ScoreUpdationRequestType = EndPointRequestBody<
	PostEndPointsMap["api/getScoreUpdationActivity"]
>;

const Content = ({
	segmentsArray,
}: {
	segmentsArray: QuizSegmentsDataType[] | undefined;
}) => {
	const { id: quizId, userId } = useParams();
	const messageInstance = useAppMessage();
	const { session, user } = useAppStore();
	const [updatedActivityData, setUpdatedActivityData] =
		useState<ScoreUpdationActivityResponse | null>(null);
	const [initialData, setInitialData] = useState<QuizSegmentsDataType[]>(
		segmentsArray ?? []
	);
	const [editingId, setEditingId] = useState<string | null>(null);
	const [editScoreValue, setEditScoreValue] = useState<number | null>(0);
	const [previewQuestion, setPreviewQuestion] =
		useState<TestSectionQuestion | null>(null);

	const handleSave = async (currScore: number, totalScore: number) => {
		if (!editingId || !quizId || !userId) return;
		if (editScoreValue === null) {
			messageInstance?.error("Score cannot be empty");
			return;
		}
		if (editScoreValue < 0) {
			messageInstance?.error("Score cannot be negative");
			return;
		} else if (editScoreValue > totalScore) {
			messageInstance?.error("Score Must Be Less Then Total Score.");
			return;
		}
		if (editScoreValue === currScore) {
			setEditingId(null);
			return;
		}
		try {
			const response = await quizClient.updateUserQuestionScore({
				additionalScore: editScoreValue,
				quesId: editingId,
				quizId,
				userId,
			});

			if ("success" in response) {
				messageInstance?.success("Score Update Successfully.");
				setInitialData(prev => {
					return prev.map(segment => {
						segment.questions = segment.questions.map(question => {
							if (question._id === editingId) {
								return {
									...question,
									score: editScoreValue,
								};
							}
							return question;
						});
						return segment;
					});
				});
				setEditingId(null);
			}
		} catch (ex) {
			console.log(ex);
			messageInstance?.error("Score update failed.");
		}
	};

	const getScoreUpdatedData = async (questionId: string) => {
		if (!quizId || !userId || !questionId) return;
		try {
			const requestBody: ScoreUpdationRequestType = {
				questionId,
				quizId,
				userId,
			};
			const response = await quizClient.getUpdatedScoreActivity({
				...requestBody,
			});
			if ("error" in response) {
				throw new Error(response.error);
			}
			if (response?.scoreUpdationActivityList?.length === 0) {
				messageInstance?.error("No audit log recorded.");
				return;
			}
			if (
				response?.scoreUpdationActivityList &&
				Array.isArray(response.scoreUpdationActivityList)
			) {
				response.scoreUpdationActivityList =
					response.scoreUpdationActivityList.sort((a, b) => {
						let bTimeInNumber = 0;
						let aTimeInNumber = 0;
						if (typeof a.updatedAt === "number") {
							aTimeInNumber = a.updatedAt;
						}
						if (typeof b.updatedAt === "number") {
							bTimeInNumber = b.updatedAt;
						}

						if (typeof a.updatedAt === "string") {
							if (!isNaN(new Date(a.updatedAt).getTime())) {
								aTimeInNumber = new Date(a.updatedAt).getTime();
							}
						}

						if (typeof b.updatedAt === "string") {
							if (!isNaN(new Date(b.updatedAt).getTime())) {
								bTimeInNumber = new Date(b.updatedAt).getTime();
							}
						}

						return bTimeInNumber - aTimeInNumber;
					});
			}
			setUpdatedActivityData(response);
		} catch (ex) {
			messageInstance?.error("Unable to get score update activity.");
			console.log(ex);
		}
	};

	return initialData && initialData.length > 0 ? (
		<Space direction="vertical" style={{ width: "100%" }}>
			{(initialData ?? []).map((segment, index) => (
				<Collapse
					style={{ overflow: "hidden" }}
					key={segment._id}
					items={[
						{
							styles: {
								body: {
									backgroundColor: "white",
									padding: "0px",
								},
							},
							style: {
								backgroundColor: "white",
							},
							key: segment._id,
							label: (
								<Space size="middle">
									<Text strong>
										<Text style={{ color: "#888" }}>
											{index + 1}.
										</Text>{" "}
										{segment.title}
									</Text>
									<Tag
										color={
											segment.status === "submitted"
												? "green"
												: "red"
										}
									>
										{segment.status}
									</Tag>
								</Space>
							),
							extra: (
								<Text>{`Score: ${segment.userScore} / ${segment.segementTotalScore}`}</Text>
							),
							children: (
								<Table<TestSectionQuestion>
									columns={[
										{
											key: "Serial Number",
											render: (
												_value,
												_record,
												index
											) => {
												return `${index + 1}.`;
											},
											width: 75,
										},
										{
											title: "Question",
											dataIndex: "title",
											key: "title",
											render: (value, record) => {
												if (!value) return "NA";
												return (
													<>
														<Tooltip
															title={
																user?.role
																	.id !==
																UserRole.USER
																	? "Preview question"
																	: ""
															}
															mouseEnterDelay={
																0.4
															}
														>
															<Text
																onClick={
																	user?.role
																		.id !==
																	UserRole.USER
																		? () =>
																				setPreviewQuestion(
																					record
																				)
																		: undefined
																}
																style={{
																	cursor: "pointer",
																}}
															>
																{value}
															</Text>
														</Tooltip>
													</>
												);
											},
										},
										{
											title: "Type",
											dataIndex: "type",
											key: "type",
											render: (
												value: keyof typeof QuestionTypesMap
											) =>
												QuestionTypesMap[value] ?? "NA",
										},
										{
											title: "Score",
											dataIndex: "score",
											key: "score",
											render: (_, record) => {
												const canEdit =
													(record.type !==
														QuestionType.SUBJECTIVE &&
														record.type !==
															QuestionType.WEB &&
														session?.email !==
															email) ||
													session?.role === "1";
												const isEditing =
													editingId === record._id;
												return (
													<>
														{!canEdit &&
														isEditing ? (
															<InputNumber
																value={
																	editScoreValue
																}
																onChange={value =>
																	setEditScoreValue(
																		value
																	)
																}
															/>
														) : (
															<Text>
																{record.score}
															</Text>
														)}

														<Text> / </Text>
														<Text>
															{
																record.outOfTotalScore
															}
														</Text>

														{!canEdit &&
															userId !==
																undefined && (
																<Space
																	style={{
																		columnGap:
																			"0px",
																	}}
																>
																	{isEditing ? (
																		<Tooltip title="Save">
																			<Button
																				type="link"
																				onClick={() =>
																					handleSave(
																						record.score,
																						record.outOfTotalScore
																					)
																				}
																				icon={
																					<SaveOutlined />
																				}
																			/>
																		</Tooltip>
																	) : (
																		record.status ===
																			"submitted" && (
																			<Tooltip title="Edit">
																				<Button
																					type="link"
																					icon={
																						<EditOutlined />
																					}
																					onClick={() => {
																						setEditScoreValue(
																							record.score
																						);
																						setEditingId(
																							record._id
																						);
																					}}
																				/>
																			</Tooltip>
																		)
																	)}
																	{record.status ===
																		"submitted" && (
																		<Button
																			type="text"
																			icon={
																				<EyeOutlined />
																			}
																			onClick={() =>
																				getScoreUpdatedData(
																					record._id
																				)
																			}
																		/>
																	)}
																</Space>
															)}
													</>
												);
											},
										},
										Table.EXPAND_COLUMN,
										{
											title: "Status",
											dataIndex: "status",
											key: "status",
											render: value => {
												if (!value) return "-NA-";
												return (
													<Tag
														color={
															value ===
															"submitted"
																? "green"
																: "red"
														}
													>
														{value}
													</Tag>
												);
											},
										},
									]}
									dataSource={segment.questions.map(q => ({
										...q,
										key: q._id,
									}))}
									expandable={{
										fixed: "right",
										showExpandColumn: true,
										rowExpandable: record =>
											(record.userAttempts?.length ?? 0) >
											0,
										expandedRowRender: record => (
											<ExpandedTable
												userAttempts={
													record.userAttempts
												}
												type={record.type}
											/>
										),
									}}
									pagination={false}
									style={{ padding: "0px" }}
								/>
							),
						},
					]}
					size="large"
				/>
			))}

			{previewQuestion && (
				<QuestionPreview
					title={previewQuestion.title}
					count={previewQuestion.testCaseCount}
					description={previewQuestion.description ?? ""}
					type={previewQuestion.type}
					id={previewQuestion._id}
					onClose={() => setPreviewQuestion(null)}
				/>
			)}

			{updatedActivityData && (
				<UpdateScoreModal
					data={updatedActivityData}
					onCancel={() => setUpdatedActivityData(null)}
				/>
			)}
		</Space>
	) : (
		<Flex align="center" justify="center" style={{ height: "100%" }}>
			<Empty />
		</Flex>
	);
};

const ExpandedTable = ({
	userAttempts,
	type,
}: {
	userAttempts?: UserAttempt[];
	type: string;
}) => {
	const [open, setOpen] = useState<string | null>(null);

	const baseColumns: ColumnsType<AttemptDataType> = [
		{
			title: "Attempt Time",
			dataIndex: "timeOfCreation",
			key: "timeOfCreation",
			align: "center",
			render: value => timeDate(value) || "-",
		},
		{
			title: "Attempt Status",
			dataIndex: "finalSubmission",
			key: "finalSubmission",
			align: "center",
			render: (_value, record) => {
				const isError = record.userCompilationError ? true : false;
				const isSubmitted =
					record.finalSubmission || record.submissionId
						? true
						: false;
				return (
					<Flex align="center" justify="center">
						<Tag
							color={
								isSubmitted
									? isError
										? "red"
										: "green"
									: isError
										? "red"
										: "blue"
							}
						>
							{isSubmitted
								? isError
									? "Error"
									: "Submitted"
								: isError
									? "Error"
									: "Executed"}
						</Tag>
						<Tooltip title="View attempt" mouseEnterDelay={0.4}>
							<Button
								size="small"
								type="dashed"
								onClick={() => setOpen(record._id)}
							>
								<FileDoneOutlined />
							</Button>
						</Tooltip>
						<Modal
							title="Question Attempt Preview"
							open={open === record._id}
							onCancel={() => setOpen(null)}
							footer={null}
							destroyOnClose
							width="95%"
							styles={{
								body: {
									height: "80vh",
									padding: "0px",
									overflow: "hidden",
								},
								header: {
									padding: "20px 24px",
								},
								content: {
									padding: "0px",
								},
							}}
							centered
						>
							{record.fromRedis ? (
								<iframe
									src={`${AppConfig.quizServerURL}/quest/preview-react/${record?.fromRedisData?.quizId}/${record?.fromRedisData?.userId}/${record?.fromRedisData?.questionId}/${record._id}?cache=true`}
									height="100%"
									width="100%"
									allowFullScreen
									style={{
										border: "none",
										borderRadius: "0px 0px 8px 8px",
									}}
								></iframe>
							) : (
								<iframe
									src={`${AppConfig.quizServerURL}/quest/preview-react/${record.attemptBucketId}/${record._id}`}
									height="100%"
									width="100%"
									allowFullScreen
									style={{ border: "none" }}
								></iframe>
							)}
						</Modal>
					</Flex>
				);
			},
		},
	];

	if (type === QuestionType.CODING) {
		baseColumns.push({
			title: "TestCases",
			key: "testcases",
			align: "center",
			render: (_, record) => {
				if (
					record.testCasesPassed === undefined ||
					record.totalTestCase === undefined
				)
					return "NA";
				return (
					<Space>
						<Text>{record.testCasesPassed ?? "-"}</Text>
						<Text>/</Text>
						<Text>{record.totalTestCase ?? "-"}</Text>
					</Space>
				);
			},
		});
	}

	return (
		<Table<AttemptDataType>
			bordered
			columns={baseColumns}
			pagination={false}
			style={{
				width: "70%",
				margin: "0px 0px 16px calc(50% - 24px)",
				transform: "translateX(-50%)",
				borderRadius: "8px",
				padding: "12px",
			}}
			dataSource={
				userAttempts
					?.flatMap(attempt => attempt.attemptData)
					.map((attempt, index) => ({
						...attempt,
						key: index,
					}))
					.sort((a, b) => {
						let aTime = 0;
						let bTime = 0;
						if (typeof a.timeOfCreation === "number") {
							aTime = a.timeOfCreation;
						}
						if (typeof b.timeOfCreation === "number") {
							bTime = b.timeOfCreation;
						}
						if (
							typeof a.timeOfCreation === "string" &&
							!isNaN(new Date(a.timeOfCreation).getTime())
						) {
							aTime = new Date(a.timeOfCreation).getTime();
						}
						if (
							typeof b.timeOfCreation === "string" &&
							!isNaN(new Date(b.timeOfCreation).getTime())
						) {
							aTime = new Date(b.timeOfCreation).getTime();
						}
						return bTime - aTime;
					}) as AttemptDataType[]
			}
		/>
	);
};

const UpdateScoreModal = ({
	data,
	onCancel,
}: {
	data: ScoreUpdationActivityResponse;
	onCancel: () => void;
}) => {
	const columns: ColumnsType<ScoreUpdationActivityListDataType> = [
		{
			key: "Serial Number",
			render: (_, __, index) => `${index + 1}.`,
			align: "right",
			width: 80,
		},
		{
			key: "originalScore",
			title: "Old Score",
			dataIndex: "originalScore",
			align: "center",
			render: value => value ?? "NA",
		},
		{
			key: "newScore",
			title: "New Score",
			dataIndex: "newScore",
			align: "center",
			render: value => value ?? "NA",
		},
		{
			key: "updatedAt",
			title: "Updated At",
			dataIndex: "updatedAt",
			render: value => timeDate(value) ?? "NA",
		},
		{
			key: "updatedBy",
			title: "Updated By",
			dataIndex: "updatedBy",
			render: value => data.userMap[value] ?? "NA",
		},
	];
	return (
		<Modal
			title="Update Activity"
			open={true}
			footer={null}
			onCancel={onCancel}
			destroyOnClose
			width="75%"
			styles={{
				body: {
					maxHeight: "65vh",
					overflow: "auto",
				},
			}}
		>
			<div
				style={{
					width: "100%",
					height: "100%",
					overflow: "auto",
				}}
			>
				<DataTable<ScoreUpdationActivityListDataType>
					columns={columns}
					data={data.scoreUpdationActivityList}
					pagination={false}
				/>
			</div>
		</Modal>
	);
};
export default Content;
