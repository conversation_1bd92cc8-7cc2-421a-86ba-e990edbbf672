import { useEffect, useMemo, useState } from "react";
import {
	Breadcrumb,
	Typography,
	Divider,
	Image,
	Flex,
	Descriptions,
	Card,
	Empty,
	Tooltip,
	Layout,
	Collapse,
	Tabs,
	Spin,
} from "antd";
import { RightOutlined } from "@ant-design/icons";
import { DescriptionsItemType } from "antd/es/descriptions";
import StickyBox from "react-sticky-box";
import Content, { QuizSegmentsDataType, TestSectionQuestion } from "./content";
import Activity from "./activity";
import Proctoring from "./proctoring";
import Submission from "./submission";
import {
	Link,
	useLocation,
	useNavigate,
	useParams,
	useSearchParams,
} from "react-router";
import { EndPointResponseBody, GetEndPointsMap } from "@/client/test-add";
import { quizClient } from "@/store/index";
import {
	Quiz,
	QuizContent,
	QuizSegment,
	QuizUserDetail,
	SubmissionDataType,
	TestDetailsReportDataType,
	UserAttempt,
	UserQuizSubmittedSegmentObj,
} from "@/testReport/data/data";
import timeDate from "@/utils/timeDate";
import { useAppMessage } from "@/hooks/message";
import { PlatformOrganisation } from "@/constants";
import { AppConfig } from "@/config";
import SectionControlLog from "./sectionControlLog";
import PageHelmet from "../page-helmet/PageHelmet";

const { Text } = Typography;
const { Sider } = Layout;

const descriptionQuizData = (data: Quiz): DescriptionsItemType[] => {
	const quizData = {
		startTime: timeDate(data.startTime),
		endTime: data.endTime ? timeDate(data.endTime) : "-NA-",
		title: data.title,
	};
	return Object.entries(quizData).map(([label, value]) => ({
		label: label.charAt(0).toUpperCase() + label.slice(1),
		children: (
			<Tooltip title={value as React.ReactNode}>
				<Text
					ellipsis
					style={{
						maxWidth: 250,
						display: "block",
					}}
				>
					{value}
				</Text>
			</Tooltip>
		),
	}));
};

const descriptionCustomFieldData = (
	data: QuizUserDetail[]
): DescriptionsItemType[] => {
	return data.map(item => {
		return {
			label:
				item.fieldLabel.charAt(0).toUpperCase() +
				item.fieldLabel.slice(1),
			children: (
				<Tooltip title={item.fieldValue as React.ReactNode}>
					<Text
						ellipsis
						style={{
							maxWidth: 250,
							display: "block",
						}}
					>
						{item.fieldValue ?? "-NA-"}
					</Text>
				</Tooltip>
			),
		};
	});
};

type UserReportData = EndPointResponseBody<
	GetEndPointsMap["/useranswer/userReport/:quizId/:userId"]
>;

function processUserData(userData: UserQuizSubmittedSegmentObj, quiz: Quiz) {
	let completed = false;
	let timeTaken = "";
	let startTime = "";
	let extraTime = 0;

	if (userData.endTime) {
		completed = true;
	} else if (
		(userData.startTime && Number(userData.startTime) == 0) ||
		!userData.startTime
	) {
		completed = true;
	}

	if (!completed) {
		if (userData.startTime) {
			startTime = userData.startTime;
			extraTime = +(userData.extraTime ?? 0);
		} else if (userData.userSessions?.length) {
			startTime =
				userData.userSessions[userData.userSessions.length - 1]
					.startTime;
			extraTime =
				userData.userSessions[userData.userSessions.length - 1]
					.extraTime;
		}
		if (startTime) {
			if (new Date(startTime).toString() !== "Invalid Date") {
				startTime = new Date(startTime).getTime().toString();
			}
			if (
				new Date(Number(startTime)).getTime() +
					extraTime +
					parseInt(quiz.quizTime.toString()) * 1000 * 60 +
					1 * 60 * 1000 <
				new Date().getTime()
			) {
				completed = true;
			}
		}
		if (completed) {
			timeTaken = (
				Number(quiz.quizTime) +
				+(userData.extraTime ?? 0) / 60
			).toString();
			userData.userSessions?.forEach(element => {
				timeTaken += element.extraTime ?? 0;
			});
			userData.endTime = new Date(
				Number(startTime) + Number(timeTaken) * 1000 * 60
			)
				.getTime()
				.toString();
			if (userData.userSessions && userData.userSessions.length) {
				userData.endTime = new Date(
					Number(startTime) +
						Number(userData.sessionTime ?? userData.extraTime) *
							1000
				)
					.getTime()
					.toString();
				userData.userSessions.push({
					endTime: userData.endTime,
					startTime: userData.startTime ?? "",
					_id: "",
					sessionTime: 0,
					extraTime: 0,
				});
			} else {
				userData.startTime = startTime;
			}
		}
	}
	if (
		userData.userSessions &&
		userData.userSessions[0] &&
		userData.userSessions[0].startTime
	) {
		startTime = userData.userSessions[0].startTime;
	}
	if (
		userData.endTime &&
		userData.startTime &&
		(!userData.userSessions || userData.userSessions.length == 0)
	) {
		if (!userData.idleTime) userData.idleTime = 0;

		const msDifference =
			new Date(+userData.endTime).getTime() -
				new Date(+userData.startTime).getTime() || 0;

		const minutes = +(msDifference / 1000 / 60).toFixed(2) || 0;
		timeTaken = minutes.toString();

		userData.endTime = new Date(+userData.startTime).getTime().toString();
		userData.endTime = (
			Number(userData.endTime) +
			Number(timeTaken) * 60_000
		).toString();
	} else {
		if (userData.userSessions && !Array.isArray(userData.userSessions)) {
			userData.userSessions = JSON.parse(userData.userSessions);
		}
		if (
			completed &&
			userData.userSessions &&
			Array.isArray(userData.userSessions) &&
			userData.userSessions.length
		) {
			let totalsMins = 0;
			userData.userSessions.forEach(val => {
				const TimeOne = +val.startTime;
				const Timetwo = +val.endTime;

				const dateOne = isNaN(TimeOne) ? val.startTime : TimeOne;
				const dateTwo = isNaN(Timetwo) ? val.endTime : Timetwo;

				if (dateOne && dateTwo) {
					const msDifference =
						+(
							new Date(dateTwo).getTime() -
							new Date(dateOne).getTime()
						) || 0;
					const minutes = +(msDifference / 1000 / 60).toFixed(2) || 0;
					totalsMins += minutes;
				}
			});
			userData.endTime =
				userData.userSessions[userData.userSessions.length - 1].endTime;
			timeTaken = totalsMins.toString();
		}
	}

	return {
		...userData,
		timeTaken: timeTaken,
		startTime: startTime,
		extraTime: extraTime,
		isAttempting: false,
		completed: completed,
		status: completed ? "Completed" : "InProgress",
	};
}

function minutesToFormattedTime(minutes: number | string) {
	const mins = Math.floor(+minutes);
	const seconds = Math.round((+minutes - mins) * 60);
	return `${mins} min ${seconds > 0 ? `${seconds} sec` : ""}`;
}

function formatDateTime(dateString?: string | number): string {
	if (!dateString) return "NA";
	const dateObj = new Date(+dateString);
	const date = dateObj.toLocaleDateString();
	const time = dateObj.toLocaleTimeString([], {
		hour: "2-digit",
		minute: "2-digit",
		hour12: false,
	});
	return `${date}, ${time}`;
}

const UserReport = () => {
	const { id, userId } = useParams();
	const messageInstance = useAppMessage();
	const { state } = useLocation();
	const [searchParams, setSearchParams] = useSearchParams();
	const navigate = useNavigate();
	const tabId = searchParams.get("tabId") ?? "content";
	const [activeTabKey, setActiveTabKey] = useState(tabId);
	const [userData, setUserData] = useState<UserReportData | null>(null);

	useEffect(() => {
		if (!id || !userId) return;
		const fetchData = async () => {
			try {
				const data = await quizClient.getUserReport(id, userId);
				setUserData(data);
			} catch (ex) {
				messageInstance?.error("Unable to fetch data");
				navigate(`/tests/${id}/report`);
				console.log(ex);
			}
		};
		fetchData();
	}, [id, messageInstance, navigate, userId]);

	const {
		testDetails,
		contentTabData,
		submissionTabData,
		sectionControlTabData: sectionControlLogTabData,
		sectionTitles,
	} = useMemo(() => {
		if (!userData) return {};
		const { quiz } = userData;

		const submittedSectionsSet = new Set(
			Array.from(
				typeof quiz.userQuizSubmittedSegment?.quizSubmittedSegments ===
					"string"
					? JSON.parse(
							quiz.userQuizSubmittedSegment.quizSubmittedSegments
						)
					: quiz.userQuizSubmittedSegment?.quizSubmittedSegments
			).map(Number)
		);

		const questionPriority: Record<string, number> = {};

		const testDetails: TestDetailsReportDataType = {
			userScore: 0,
			totalScore: 0,
			userAttemptQues: 0,
			totalQues: 0,
			startTime: undefined,
			endTime: undefined,
			CompletedIn: undefined,
			tabSwitchCount: quiz.userQuizSubmittedSegment?.tabSwitchCount,
			tabSwitchInCount: quiz.userQuizSubmittedSegment?.tabSwitchInCount,
			fullScreenInCount: quiz.userQuizSubmittedSegment?.fullScreenInCount,
			fullScreenOutCount:
				quiz.userQuizSubmittedSegment?.fullScreenOutCount,
			loginCount: quiz.userQuizSubmittedSegment?.loginCount,
			camBlockCount: quiz.userQuizSubmittedSegment?.camBlockCount,
			codePasteCount: quiz.userQuizSubmittedSegment?.codePasteCount,
		};

		if (quiz.userQuizSubmittedSegment?.questionId) {
			try {
				const questionIds: Array<string> = [];
				if (
					typeof quiz.userQuizSubmittedSegment.questionId === "string"
				) {
					const questionIdsParsed = JSON.parse(
						quiz.userQuizSubmittedSegment.questionId
					);
					questionIds.push(...questionIdsParsed);
				} else if (
					Array.isArray(quiz.userQuizSubmittedSegment.questionId)
				) {
					questionIds.push(
						...quiz.userQuizSubmittedSegment.questionId
					);
				}
				if (
					(quiz.randomizeQuestion || quiz.poolQuestion) &&
					questionIds.length
				) {
					questionIds.map((id, index) => {
						questionPriority[id] = index;
					});
				}
			} catch (error) {
				console.trace("Failed to set question priority", error);
			}
		}

		const proccessedUserData = processUserData(
			userData.userQuizSubmittedSegmentObj,
			quiz
		);
		testDetails.startTime = formatDateTime(proccessedUserData.startTime);
		testDetails.endTime = formatDateTime(proccessedUserData.endTime);
		testDetails.CompletedIn =
			minutesToFormattedTime(proccessedUserData.timeTaken) ?? "NA";

		let segmentsArray: QuizSegment[] = [];

		if (quiz && quiz.quizSegments && quiz.quizSegments.length) {
			segmentsArray = quiz.quizSegments;
		}

		let quizSubmittedQuestion: string[] | undefined = [];
		if (
			quiz.poolQuestion == true ||
			quiz.randomizeQuestion == true ||
			(quiz &&
				quiz.userQuizSubmittedSegment &&
				quiz.userQuizSubmittedSegment.questionId)
		) {
			quizSubmittedQuestion = quiz.userQuizSubmittedSegment?.questionId;
		} else {
			quizSubmittedQuestion = quiz.questionId;
		}

		let content_counter: number = 0;
		const lastIndex: number = segmentsArray.length - 1;
		const contentTabData: QuizSegmentsDataType[] = [];
		const sectionTitles: string[] = [];

		segmentsArray.forEach((segment: QuizSegment, index: number) => {
			sectionTitles.push(segment.title);
			let contentArray: QuizContent[] = [];
			let segementScore: number = 0;
			let segmentTotalScore: number = 0;

			contentArray = quiz.quizContent.slice(
				content_counter,
				content_counter + segment.count
			);

			content_counter += segment.count;

			let questions: TestSectionQuestion[] = [];
			for (let i = 0; i < contentArray.length; i++) {
				const question: TestSectionQuestion = {
					_id: "",
					score: 0,
					title: "",
					type: "",
					outOfTotalScore: 0,
					status: "Not Submitted",
					userAttempts: [],
					description: "",
					testCaseCount: 0,
				};
				if (
					quizSubmittedQuestion &&
					quizSubmittedQuestion.indexOf(contentArray[i].id) !== -1
				) {
					testDetails.totalQues += 1;
					segmentTotalScore += contentArray[i].question?.score ?? 0;
					segementScore += +(
						contentArray[i].quizSubmittedQuestion?.score ?? 0
					);
					if (contentArray[i].question) {
						const questionScore =
							contentArray[i].quizSubmittedQuestion?.score ?? 0;
						const questionOutOfTotalScore =
							contentArray[i].question?.score ?? 0;
						question._id = contentArray[i].question?._id ?? "";
						question.title = contentArray[i].question?.title ?? "";
						question.type = contentArray[i].question?.type ?? "";
						question.score = questionScore;

						question.outOfTotalScore = questionOutOfTotalScore;
						question.status = contentArray[i].quizSubmittedQuestion
							? contentArray[i].quizSubmittedQuestion
									?.hasSubmitted
								? "submitted"
								: "Executed"
							: "Not Submitted";
						question.userAttempts =
							contentArray[i].attempts?.userAttempts.map(
								attempt =>
									({
										...attempt,
										attemptData: attempt.attemptData.map(
											attemptData =>
												attemptData.fromRedis
													? {
															...attemptData,
															fromRedisData: {
																quizId: id,
																userId,
																questionId:
																	attempt.questionId,
															},
														}
													: attemptData
										),
									}) as UserAttempt
							) || [];
						question.description =
							contentArray[i].question?.text ?? "";
						question.testCaseCount =
							contentArray[i].question?.questionTypeCoding
								?.testCase.length ?? 0;
					}
					questions.push(question);
				}
				if (question.status !== "Not Submitted") {
					testDetails.userAttemptQues += 1;
				}
			}

			testDetails.totalScore += segmentTotalScore;
			testDetails.userScore += segementScore;

			if (
				questions?.length &&
				(quiz.randomizeQuestion || quiz.poolQuestion) &&
				questionPriority &&
				Object.keys(questionPriority).length
			) {
				questions = questions.sort((a, b) => {
					return questionPriority[a._id] - questionPriority[b._id];
				});
			}

			const segmentData: QuizSegmentsDataType = {
				title: segment.title,
				_id: segment._id,
				questions: questions,
				userScore: segementScore,
				segementTotalScore: segmentTotalScore,
				status:
					quiz.revisitAllowed ||
					submittedSectionsSet.has(index) ||
					(lastIndex === index &&
						submittedSectionsSet.size === lastIndex &&
						quiz.userQuizSubmittedSegment?.endTime)
						? "submitted"
						: "not submitted",
			};
			contentTabData.push(segmentData);
		});

		const sectionControlTabData: SubmissionDataType[] = (
			userData.userQuizSubmittedSegmentObj.segmentSubmissionHistory ?? []
		)?.filter(data => data.submissionType === 2);

		const submissionTabData: SubmissionDataType[] = (
			userData.userQuizSubmittedSegmentObj.segmentSubmissionHistory ?? []
		)?.filter(data => data.submissionType === 1);

		return {
			testDetails,
			contentTabData,
			submissionTabData,
			sectionControlTabData,
			sectionTitles,
		};
	}, [id, userData, userId]);

	const tabsItems = useMemo(() => {
		const items = [
			{
				key: "content",
				title: "Content",
				children: <Content segmentsArray={contentTabData} />,
			},
			{
				key: "activityOverview",
				title: "Activity Overview",
				children: (
					<Activity
						loginCount={testDetails?.loginCount ?? 0}
						fullScreenInCount={testDetails?.fullScreenInCount ?? 0}
						fullScreenOutCount={
							testDetails?.fullScreenOutCount ?? 0
						}
					/>
				),
			},
			{
				key: "submissionHistory",
				title: "Submission History",
				children: (
					<Submission
						submissionData={submissionTabData}
						sectionTitles={sectionTitles}
					/>
				),
			},
			{
				key: "sectionControlLog",
				title: "Section Control Log",
				children: (
					<SectionControlLog
						submissionData={sectionControlLogTabData}
						sectionTitles={sectionTitles}
					/>
				),
			},
		];

		if (userData?.quiz?.isWebCamAllowed) {
			items.splice(2, 0, {
				key: "proctoring",
				title: "Proctoring",
				children: <Proctoring />,
			});
		}

		return items;
	}, [
		contentTabData,
		sectionControlLogTabData,
		sectionTitles,
		submissionTabData,
		userData?.quiz?.isWebCamAllowed,
	]);

	return !userData ? (
		<Flex
			justify="center"
			align="center"
			style={{
				height: "100vh",
			}}
		>
			<Spin size="large" />
		</Flex>
	) : (
		<>
			{userData?.quiz?.displayname && (
				<PageHelmet title={userData?.quiz?.displayname + "'s report"} />
			)}
			<Layout style={{ height: "100%" }}>
				<Layout.Header
					style={{ backgroundColor: "transparent", height: "auto" }}
				>
					<Breadcrumb
						separator={
							<RightOutlined
								style={{
									color: "#bbb",
									fontSize: 12,
								}}
							/>
						}
						style={{
							border: "0px solid #000",
							padding: "16px",
						}}
						items={[
							{
								key: "tests",
								title: <Link to="/tests">All Tests</Link>,
							},
							{
								key: "test",
								title: (
									<Link to={`/tests/${id}/report`}>
										{userData?.quiz.title}
									</Link>
								),
							},
							{
								title: `${userData?.quiz.displayname}`,
							},
						]}
					/>
				</Layout.Header>

				<Layout
					style={{
						flex: 1,
						display: "flex",
						height: "100%",
					}}
				>
					<Sider
						width={350}
						theme="light"
						style={{
							overflowY: "auto",
							height: "100%",
							position: "relative",
							scrollbarWidth: "thin",
							borderRight: "1px solid #e8e8e8",
							paddingTop: 24,
						}}
					>
						<Flex
							vertical
							align="center"
							style={{
								width: "100%",
								height: "100%",
								textAlign: "center",
								alignItems: "center",
							}}
						>
							<Image
								width={80}
								height={80}
								src={""}
								fallback={`https://api.dicebear.com/5.x/initials/svg?seed=${userData?.userQuizSubmittedSegmentObj.displayName}&backgroundColor=DE6834&chars=1`}
								alt="User"
								preview={false}
								style={{
									borderRadius: "50%",
								}}
							/>
							<Text
								style={{
									fontSize: "16px",
									fontWeight: "500",
									marginTop: "8px",
									padding: "0 16px",
								}}
							>
								{userData?.quiz.displayname}
							</Text>
							<Divider />

							<Descriptions
								title="About"
								column={1}
								style={{
									padding: "0 16px",
									textAlign: "start",
								}}
							>
								<Descriptions.Item label="Email">
									{userData?.quiz?.email || "-"}
								</Descriptions.Item>

								{PlatformOrganisation.CHITKARA ===
									AppConfig.platformOrganisation && (
									<Descriptions.Item label="Roll Number">
										{userData?.quiz?.enrollmentId || "-NA-"}
									</Descriptions.Item>
								)}
							</Descriptions>

							<Divider />
							<Descriptions
								title="Quiz Details"
								column={1}
								items={descriptionQuizData(userData.quiz)}
								styles={{
									title: {
										textAlign: "start",
									},
									content: {
										textAlign: "start",
									},
								}}
								style={{
									padding: "0 16px",
								}}
							/>
							<Divider />

							{(() => {
								const detailsRaw =
									userData.userQuizSubmittedSegmentObj
										?.quizUserDetails;

								let parsedDetails = [];

								if (Array.isArray(detailsRaw)) {
									parsedDetails = detailsRaw;
								} else if (typeof detailsRaw === "string") {
									try {
										const temp = JSON.parse(detailsRaw);
										if (Array.isArray(temp)) {
											parsedDetails = temp;
										}
									} catch (e) {
										console.error(
											"Failed to parse quizUserDetails:",
											e
										);
									}
								}

								return parsedDetails.length > 0 ? (
									<Descriptions
										title="Custom Fields"
										column={1}
										items={descriptionCustomFieldData(
											parsedDetails
										)}
										styles={{
											title: {
												textAlign: "start",
											},
											content: {
												textAlign: "start",
											},
										}}
										style={{
											padding: "0 16px",
										}}
									/>
								) : (
									<Empty
										description="No Custom Fields"
										style={{
											margin: "auto",
										}}
									/>
								);
							})()}
						</Flex>
					</Sider>

					<Layout
						style={{
							flex: 1,
							padding: "24px",
							height: "100%",
							overflow: "auto",
							backgroundColor: "#FFFFFF",
						}}
					>
						<Collapse
							defaultActiveKey={
								state && state.fromTestReport ? [] : ["1"]
							}
							items={[
								{
									key: "1",
									label: "Test Details",
									children: (
										<Card bordered={false}>
											<Card.Grid
												hoverable={false}
												style={{
													width: "100%",
													padding: "16px",
												}}
											>
												<Descriptions
													title="General Info"
													column={{
														md: 2,
														lg: 3,
													}}
													items={[
														{
															label: "Marks Obtained",
															children: (
																<Text>
																	{`${testDetails?.userScore ?? 0} /
																	${testDetails?.totalScore}`}
																</Text>
															),
														},
														{
															label: "Questions Attempted",
															children: (
																<Text>{`${testDetails?.userAttemptQues ?? 0} / ${testDetails?.totalQues}`}</Text>
															),
														},
													]}
												/>
											</Card.Grid>
											<Card.Grid
												hoverable={false}
												style={{
													width: "100%",
													padding: "16px",
												}}
											>
												<Descriptions
													title="Time Details"
													column={{
														md: 2,
														lg: 3,
													}}
													items={[
														{
															label: "Start Time",
															children: (
																<Text>
																	{testDetails?.startTime ??
																		"NA"}
																</Text>
															),
														},
														{
															label: "End Time",
															children: (
																<Text>
																	{testDetails?.endTime ??
																		"NA"}
																</Text>
															),
														},
														{
															label: "Completed In",
															children: (
																<Text>
																	{testDetails?.CompletedIn ??
																		"NA"}
																</Text>
															),
														},
													]}
												/>
											</Card.Grid>
											<Card.Grid
												hoverable={false}
												style={{
													width: "100%",
													padding: "16px",
												}}
											>
												<Descriptions
													title="Tab Activity"
													column={{
														md: 2,
														lg: 3,
													}}
													items={[
														{
															label: "Tabs Switched In",
															children: (
																<Text>
																	{testDetails?.tabSwitchInCount ??
																		"NA"}
																</Text>
															),
														},
														{
															label: "Tabs Switched Out",
															children: (
																<Text>
																	{testDetails?.tabSwitchCount ??
																		"NA"}
																</Text>
															),
														},
													]}
												/>
											</Card.Grid>
											<Card.Grid
												hoverable={false}
												style={{
													width: "100%",
													padding: "16px",
												}}
											>
												<Descriptions
													title="Full Screen Activity"
													column={{
														md: 2,
														lg: 3,
													}}
													items={[
														{
															label: "Full Screen In",
															children: (
																<Text>
																	{testDetails?.fullScreenInCount ??
																		"NA"}
																</Text>
															),
														},
														{
															label: "Full Screen Out",
															children: (
																<Text>
																	{testDetails?.fullScreenOutCount ??
																		"NA"}
																</Text>
															),
														},
													]}
												/>
											</Card.Grid>
											<Card.Grid
												hoverable={false}
												style={{
													width: "100%",
													padding: "16px",
												}}
											>
												<Descriptions
													title="Other Activities"
													column={{
														md: 2,
														lg: 3,
													}}
													items={[
														{
															label: "Tried Copy-Paste",
															children: (
																<Text>
																	{testDetails?.codePasteCount ??
																		"NA"}
																</Text>
															),
														},
														{
															label: "Login Count",
															children: (
																<Text>
																	{testDetails?.loginCount ??
																		"NA"}
																</Text>
															),
														},
														{
															label: "Cam Block",
															children: (
																<Text>
																	{testDetails?.camBlockCount ??
																		"NA"}
																</Text>
															),
														},
													]}
												/>
											</Card.Grid>
										</Card>
									),
								},
							]}
						></Collapse>
						<Tabs
							activeKey={activeTabKey}
							items={tabsItems.map(item => ({
								key: item.key,
								label: item.title,
								children: item.children,
							}))}
							onChange={key => {
								setActiveTabKey(key);
								setSearchParams({
									tabId: key,
								});
							}}
							renderTabBar={(props, DefaultTabBar) => (
								<StickyBox
									offsetTop={-25}
									offsetBottom={0}
									style={{
										zIndex: 1000,
									}}
								>
									<DefaultTabBar
										{...props}
										style={{
											backgroundColor: "#FFFFFF",
										}}
									/>
								</StickyBox>
							)}
						/>
					</Layout>
				</Layout>
			</Layout>
		</>
	);
};

export default UserReport;
