import { Tabs, Segmented, Badge, Space } from "antd";

export type InnerTabItem<K extends string = string> = {
	key: K;
	title?: string;
	icon?: React.ReactNode;
	count?: number;
	children?: React.ReactNode;
};

export interface InnerTabProps<K extends string = string> {
	activeTabKey: string;
	tabsItems: InnerTabItem<K>[];
	onTabChange: (_key: K) => void;
}

const InnerTab = <K extends string = string>({
	activeTabKey,
	tabsItems,
	onTabChange,
}: InnerTabProps<K>) => {
	return (
		<Tabs
			activeKey={activeTabKey}
			destroyInactiveTabPane
			items={tabsItems.map(item => ({
				key: item.key,
				label: item.title,
				icon: item.icon ?? <></>,
				children: item.children,
			}))}
			renderTabBar={props => (
				<Segmented<K>
					value={props.activeKey as K}
					onChange={key => onTabChange(key)}
					options={tabsItems.map(item => ({
						value: item.key,
						label: (
							<Space>
								{item.title}
								{(item.count ?? 0) > 0 && (
									<Badge
										count={item.count ?? 0}
										color="#de6834"
										style={{ borderColor: "#0000" }}
										size="default"
									/>
								)}
							</Space>
						),
						icon: item.icon ?? <></>,
					}))}
					style={{ marginBottom: "16px", width: "fit-content" }}
					size="middle"
				/>
			)}
		/>
	);
};

export default InnerTab;
