import { useMemo, useState } from "react";
import { TableColumnsType } from "antd";
import timeDate from "@/utils/timeDate";
import { DataTable } from "../dataTable/dataTable";
import PreviewImageButton, { PreviewImage } from "@/utils/previewImage";
import { SubmissionDataType } from "@/testReport/data/data";

const Submission = ({
	submissionData,
	sectionTitles,
}: {
	submissionData: SubmissionDataType[] | undefined;
	sectionTitles: string[] | undefined;
}) => {
	const [activeSources, setActiveSources] = useState<string[]>([]);

	const sourceIndexMap = useMemo(
		() =>
			Object.fromEntries(
				activeSources.map((source: string, index: number) => [
					source,
					index,
				])
			),
		[activeSources]
	);

	const submissionColumns: TableColumnsType<SubmissionDataType> = useMemo(
		() => [
			{
				key: "Serial Number",
				render: (_value, _record, index) => index + 1,
				width: 30,
				fixed: "left",
			},
			{
				title: "Submission Section",
				key: "segmentIndex",
				dataIndex: "segmentIndex",
				render: value =>
					value === -1
						? "Quiz Submit"
						: sectionTitles
							? sectionTitles[value]
							: "-NA-",
			},
			{
				title: "Time",
				key: "time",
				dataIndex: "time",
				sorter: (a, b) =>
					new Date(a.time ?? 0).getTime() -
					new Date(b.time ?? 0).getTime(),
				showSorterTooltip: false,
				render: value => (value ? timeDate(value) : "-NA-"),
			},
			{
				title: "Created By",
				key: "createdBy",
				dataIndex: "createdBy",
				render: value => (value ? value.displayname : "-NA-"),
			},
			{
				title: "OTP",
				key: "otp",
				dataIndex: "otp",
				render: value => (value ? value : "-NA-"),
			},
			{
				title: "Image",
				key: "imageURL",
				dataIndex: "imageURL",
				render: (value: string) => {
					if (!value) return "-NA-";
					return (
						<PreviewImageButton
							src={value}
							onReady={src => {
								setActiveSources(previous => [
									...previous,
									src,
								]);
							}}
						/>
					);
				},
			},
		],
		[sectionTitles]
	);

	const handlePreviewClose = () => setActiveSources([]);

	return (
		<div>
			<>
				<DataTable<SubmissionDataType>
					columns={submissionColumns}
					data={submissionData ?? []}
					pagination={false}
				/>
				<PreviewImage
					sources={activeSources}
					onClose={handlePreviewClose}
					onDownload={url => {
						const index = sourceIndexMap[url];
						if (
							index === undefined ||
							submissionData === undefined
						) {
							return;
						}
						const record = submissionData[index];
						return `${record.createdBy.displayname.replace(/\s+/g, "")}-${record.createdBy._id}`;
					}}
				/>
			</>
		</div>
	);
};

export default Submission;
