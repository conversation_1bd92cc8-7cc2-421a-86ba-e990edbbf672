import { DataTable } from "@/components/dataTable/dataTable";
import timeDate from "@/utils/timeDate";
import { quizClient } from "../../../store";
import { TableColumnsType } from "antd";
import { useEffect, useState } from "react";

interface LoginCountDataType {
	loginTime: string;
	ip: string;
	platform: string;
	_id: string;
}

interface LoginCountProps {
	testId?: string;
	userId?: string;
	setLoginCountDataLength?: (_length: number) => void;
	scroll?: { y: number };
}
const loginCountColums: TableColumnsType<LoginCountDataType> = [
	{
		key: "Serial Number",
		render: (_value, _record, index) => index + 1,
		width: 30,
		fixed: "left",
	},
	{
		title: "Login Time",
		key: "loginTime",
		dataIndex: "loginTime",
		sorter: (a, b) =>
			new Date(a.loginTime ?? 0).getTime() -
			new Date(b.loginTime ?? 0).getTime(),
		showSorterTooltip: false,
		render: value => (value ? timeDate(value) : "-NA-"),
	},
	{
		title: "IP",
		key: "ip",
		dataIndex: "ip",
		render: ip => ip || "-NA-",
	},
	{
		title: "Platform",
		key: "platform",
		dataIndex: "platform",
		render: platform => platform || "-NA-",
	},
];

const LoginCount = ({
	testId,
	userId,
	setLoginCountDataLength,
	scroll,
}: LoginCountProps) => {
	const [loading, setLoading] = useState(false);
	const [loginCountData, setLoginCountData] = useState<LoginCountDataType[]>(
		[]
	);

	useEffect(() => {
		if (!testId || !userId) return;
		(async function () {
			setLoading(true);
			try {
				const loginTimeStampsData =
					await quizClient.getloginTimestampsReport(testId, userId);

				if (loginTimeStampsData) {
					const data = loginTimeStampsData;
					if (data && data.timeStampsArr) {
						const loginContainer: LoginCountDataType[] = [];
						data.timeStampsArr.forEach(
							({ loginTime, ip, platform, _id }) => {
								if (loginTime) {
									if (
										platform &&
										typeof platform === "string"
									) {
										try {
											const parsedPlatform =
												JSON.parse(platform);
											platform = parsedPlatform.browser
												? `${parsedPlatform.os || "Unknown"}/${parsedPlatform.browser === "Electron" ? "App" : parsedPlatform.browser}`
												: "Unknown";
										} catch {
											platform = "-NA-";
										}
									}
									loginContainer.push({
										loginTime,
										ip,
										platform,
										_id,
									});
								}
							}
						);
						setLoginCountData(loginContainer);
						if (setLoginCountDataLength) {
							setLoginCountDataLength(loginContainer.length);
						}
					}
				}
			} catch (ex) {
				console.log(ex);
			} finally {
				setLoading(false);
			}
		})();
	}, [testId, userId, setLoginCountDataLength]);
	return (
		<>
			<DataTable<LoginCountDataType>
				columns={loginCountColums}
				data={loginCountData}
				pagination={false}
				loading={loading}
				scroll={scroll}
			/>
		</>
	);
};

export default LoginCount;
