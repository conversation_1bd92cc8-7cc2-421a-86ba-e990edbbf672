import { TableColumnsType } from "antd";
import { useEffect, useState } from "react";
import timeDate from "@/utils/timeDate";
import { DataTable } from "@/components/dataTable/dataTable";
import { quizClient } from "../../../store";

interface TabSwitchDataType {
	outTime?: string;
	inTime?: string;
	duration?: string;
	_id: string;
}
interface FullScreenDataType {
	outTime?: string;
	inTime?: string;
	duration?: string;
	_id: string;
}

interface FullScreenProps {
	testId?: string;
	userId?: string;
	setFullScreenDataLength?: (_length: number) => void;
	scroll?: { y: number };
}
const fullScreenColumns: TableColumnsType<FullScreenDataType> = [
	{
		key: "Serial Number",
		render: (_value, _record, index) => index + 1,
		width: 30,
		fixed: "left",
	},
	{
		title: "Out Time",
		key: "outTime",
		dataIndex: "outTime",
		sorter: (a, b) =>
			new Date(a.outTime ?? 0).getTime() -
			new Date(b.outTime ?? 0).getTime(),
		showSorterTooltip: false,
		render: value => (value ? timeDate(value) : "-NA-"),
	},
	{
		title: "In Time",
		key: "inTime",
		dataIndex: "inTime",
		sorter: (a, b) =>
			new Date(a.inTime ?? 0).getTime() -
			new Date(b.inTime ?? 0).getTime(),
		showSorterTooltip: false,
		render: value => (value ? timeDate(value) : "-NA-"),
	},
	{
		title: "Duration",
		key: "duration",
		sorter: (a, b) => {
			const durationA = a.outTime
				? new Date(a.inTime ?? 0).getTime() -
					new Date(a.outTime ?? 0).getTime()
				: 0;
			const durationB = b.outTime
				? new Date(b.inTime ?? 0).getTime() -
					new Date(b.outTime ?? 0).getTime()
				: 0;
			return durationA - durationB;
		},
		showSorterTooltip: false,
		render: (_value, record) => {
			if (record.outTime && record.inTime) {
				const msDifference =
					new Date(record.inTime).getTime() -
					new Date(record.outTime).getTime();
				if (msDifference < 1000) return "1 sec";

				const secondsDifference = Math.floor(msDifference / 1000);
				const minutes = Math.floor(secondsDifference / 60);
				const seconds = secondsDifference % 60;

				let duration = minutes ? `${minutes} min` : "";
				if (seconds) duration += ` ${seconds} sec`;
				return duration;
			}
			return "-NA-";
		},
	},
];

const FullScreen = ({
	testId,
	userId,
	setFullScreenDataLength,
	scroll,
}: FullScreenProps) => {
	const [loading, setLoading] = useState(false);
	const [fullScreenData, setFullScreenData] = useState<FullScreenDataType[]>(
		[]
	);
	useEffect(() => {
		if (!testId || !userId) return;
		(async function () {
			setLoading(true);
			try {
				const fullScreenData = await quizClient.getFullScreenReport(
					testId,
					userId
				);
				if (fullScreenData) {
					const data = fullScreenData;
					if (data && data.timeStampsArr) {
						const tabSwitchEventType = { in: 1, out: 2 };
						const fullScreenContainer: TabSwitchDataType[] = [];
						let prevVal: number | undefined;
						data.timeStampsArr.forEach(
							({
								eventType: tabSwitchType,
								time: tabTime,
								_id,
							}) => {
								if (!prevVal) {
									if (
										tabSwitchType === tabSwitchEventType.out
									) {
										fullScreenContainer.push({
											outTime: tabTime,
											_id,
										});
									} else {
										fullScreenContainer.push({
											inTime: tabTime,
											_id,
										});
									}
								} else {
									if (
										tabSwitchType === tabSwitchEventType.out
									) {
										fullScreenContainer.push({
											outTime: tabTime,
											_id,
										});
									} else {
										if (
											prevVal === tabSwitchEventType.out
										) {
											fullScreenContainer[
												fullScreenContainer.length - 1
											].inTime = tabTime;
										} else {
											fullScreenContainer.push({
												inTime: tabTime,
												_id,
											});
										}
									}
								}
								prevVal = tabSwitchType;
							}
						);
						setFullScreenData(fullScreenContainer);
						if (setFullScreenDataLength) {
							setFullScreenDataLength(fullScreenContainer.length);
						}
					}
				}
			} catch (ex) {
				console.log(ex);
			} finally {
				setLoading(false);
			}
		})();
	}, [testId, userId, setFullScreenDataLength]);
	return (
		<>
			<DataTable<FullScreenDataType>
				columns={fullScreenColumns}
				data={fullScreenData}
				pagination={false}
				loading={loading}
				scroll={scroll}
			/>
		</>
	);
};

export default FullScreen;
