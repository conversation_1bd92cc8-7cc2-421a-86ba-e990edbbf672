import {
	Pagination,
	Card,
	Row,
	Col,
	Empty,
	Flex,
	message,
	Tag,
	Badge,
	Typography,
	Image,
} from "antd";
import {
	UserOutlined,
	QuestionCircleOutlined,
	TeamOutlined,
	LoginOutlined,
	LogoutOutlined,
	StopOutlined,
	LoadingOutlined,
} from "@ant-design/icons";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import SkeletonImage from "../skeleton/skeletonImage";
import timeDate from "@/utils/timeDate";
import InnerTab from "./innerTab";
import { EndPointResponseBody, GetEndPointsMap } from "@/client/test-add";
import { useParams } from "react-router";
import { createToolbarRender, downloadFile } from "@/utils/ImageToolBar";
import { ProctoringType, subTypeMap } from "@/constants/";
import { quizClient } from "../../store";

type ImagesDataType = EndPointResponseBody<
	GetEndPointsMap["/test/aiProctorReport/:testId/:userId"]
>[number] & { index: number };

const PaginateImages = ({
	images,
	setImages,
	isAll = false,
}: {
	images: ImagesDataType[];
	setImages: React.Dispatch<React.SetStateAction<ImagesDataType[]>>;
	isAll?: boolean;
}) => {
	const [currentPage, setCurrentPage] = useState(1);
	const [currentImageIndex, setCurrentImageIndex] = useState(-1);
	const [pageSize, setPageSize] = useState(12);

	const paginatedImages = images.slice(
		(currentPage - 1) * pageSize,
		currentPage * pageSize
	);
	const currentImageSrc = paginatedImages[currentImageIndex]?.src;

	const handleOnLoad = useCallback(
		(index: number, src: string) => {
			setImages(prevState => {
				const newImages = [...prevState];
				newImages[index] = {
					...newImages[index],
					src,
				};
				return newImages;
			});
		},
		[setImages]
	);

	return (
		<Flex
			vertical
			align="stretch"
			style={{
				width: "100%",
				minHeight: `calc(100vh - 22.25rem)`,
				alignItems: "unset",
				justifyContent: "unset",
			}}
		>
			{paginatedImages.length > 0 ? (
				<Row
					gutter={[24, 24]}
					justify="start"
					style={{ width: "100%" }}
				>
					<Image.PreviewGroup
						preview={{
							imageRender(image) {
								return image.props.src === undefined ? (
									<LoadingOutlined style={{ fontSize: 24 }} />
								) : (
									React.cloneElement(image, {
										style: {
											...(image.props.style || {}),
											border: "8px solid #fff",
											borderRadius: "8px",
											backgroundColor: "#fff",
										},
									})
								);
							},
							styles: {
								mask: { backdropFilter: "blur(5px)" },
							},
							toolbarRender: createToolbarRender({
								onDownload:
									currentImageSrc === undefined
										? undefined
										: () => {
												downloadFile(
													currentImageSrc,
													`${subTypeMap[paginatedImages[currentImageIndex].subType]}-${paginatedImages[currentImageIndex].index}`
												);
											},
							}),
							countRender(current, total) {
								if (total < 2) return null;
								return (
									<div
										style={{
											padding: "8px 16px",
											backgroundColor:
												"rgba(0, 0, 0, 0.4)",
											borderRadius: "100px",
										}}
									>
										{current} / {total}
									</div>
								);
							},
							onChange: (index: number) =>
								setCurrentImageIndex(index),
						}}
					>
						{paginatedImages.map((image, index) => (
							<Col
								className="gutter-row"
								key={image.index}
								sm={24}
								md={12}
								lg={8}
								xl={6}
								xxl={4}
							>
								<Card
									styles={{
										body: {
											padding: 8,
										},
										cover: { overflow: "hidden" },
									}}
									style={{ overflow: "hidden" }}
									cover={
										<SkeletonImage
											src={image.src}
											imageUrl={
												image.imageUrl ?? undefined
											}
											onLoad={src => {
												handleOnLoad(image.index, src);
											}}
											onClick={() =>
												setCurrentImageIndex(index)
											}
										/>
									}
								>
									{isAll && (
										<Tag
											style={{
												position: "absolute",
												top: 8,
												right: 0,
											}}
										>
											{subTypeMap[image.subType]}
										</Tag>
									)}
									<Badge
										count={index + 1}
										color="#f0f0f0"
										styles={{
											root: {
												position: "absolute",
												top: 8,
												left: 8,
											},
										}}
										style={{ color: "#888" }}
									/>
									<Flex justify="center">
										<Typography.Text
											style={{ color: "#666" }}
										>
											{timeDate(image.time)}
										</Typography.Text>
									</Flex>
								</Card>
							</Col>
						))}
					</Image.PreviewGroup>
				</Row>
			) : (
				<Empty
					description="No proctoring images"
					style={{ fontSize: "16px", margin: "auto" }}
				/>
			)}
			<Pagination
				current={currentPage}
				pageSize={pageSize}
				total={images.length}
				onChange={(page, pageSize) => {
					setCurrentPage(page);
					setPageSize(pageSize);
				}}
				style={{
					margin: "16px auto",
					textAlign: "center",
				}}
				hideOnSinglePage
				showSizeChanger={false}
				align="center"
			/>
		</Flex>
	);
};

const Proctoring = () => {
	const { id, userId } = useParams();
	const [activeTabKey, setActiveTabKey] = useState(
		subTypeMap[ProctoringType.ALL]
	);
	const [images, setImages] = useState<ImagesDataType[]>([]);
	const [messageApi, contextHolder] = message.useMessage();
	useEffect(() => {
		if (!id || !userId) return;
		const fetchData = async () => {
			try {
				const response = await quizClient.getAiProctoringReport(
					id,
					userId
				);
				if (!response.length) {
					return;
				}
				const data: ImagesDataType[] = response.map((img, index) => ({
					...img,
					index,
				}));

				setImages(data);
			} catch (error) {
				messageApi.error(`${error}`);
				console.log(error);
			}
		};
		fetchData();
	}, [id, messageApi, userId]);

	const tabsItems = useMemo(() => {
		const filteredImages =
			activeTabKey === subTypeMap[ProctoringType.ALL]
				? images
				: images.filter(
						img => subTypeMap[img.subType] === activeTabKey
					);
		const items = [
			{
				key: subTypeMap[ProctoringType.ALL],
				title: subTypeMap[ProctoringType.ALL],
				icon: <UserOutlined />,
				count: images.length,
				children: (
					<PaginateImages
						images={filteredImages}
						setImages={setImages}
						isAll={true}
					/>
				),
			},
			{
				key: subTypeMap[ProctoringType.USER_ENTER],
				title: subTypeMap[ProctoringType.USER_ENTER],
				icon: <LoginOutlined />,
				count: 0,
				children: (
					<PaginateImages
						images={filteredImages}
						setImages={setImages}
					/>
				),
			},
			{
				key: subTypeMap[ProctoringType.MULTIPLE_PERSON],
				title: subTypeMap[ProctoringType.MULTIPLE_PERSON],
				icon: <TeamOutlined />,
				count: 0,
				children: (
					<PaginateImages
						images={filteredImages}
						setImages={setImages}
					/>
				),
			},
			{
				key: subTypeMap[ProctoringType.USER_EXIT],
				title: subTypeMap[ProctoringType.USER_EXIT],
				icon: <LogoutOutlined />,
				count: 0,
				children: (
					<PaginateImages
						images={filteredImages}
						setImages={setImages}
					/>
				),
			},
			{
				key: subTypeMap[ProctoringType.PROHIBITED_ITEMS],
				title: subTypeMap[ProctoringType.PROHIBITED_ITEMS],
				icon: <StopOutlined />,
				count: 0,
				children: (
					<PaginateImages
						images={filteredImages}
						setImages={setImages}
					/>
				),
			},
			{
				key: subTypeMap[ProctoringType.RANDOM],
				title: subTypeMap[ProctoringType.RANDOM],
				icon: <QuestionCircleOutlined />,
				count: 0,
				children: (
					<PaginateImages
						images={filteredImages}
						setImages={setImages}
					/>
				),
			},
		];
		const subTypeKeyMap = Object.fromEntries(
			Object.entries(subTypeMap).map(([key, value]) => [value, +key])
		);
		const itemsMap = new Map(
			items.map(item => [subTypeKeyMap[item.key], item])
		);
		images.forEach(image => {
			const item = itemsMap.get(image.subType);
			if (item === undefined) {
				return;
			}
			item.count++;
		});
		return items;
	}, [activeTabKey, images]);

	return (
		<div>
			{contextHolder}
			<InnerTab
				activeTabKey={activeTabKey}
				tabsItems={tabsItems}
				onTabChange={setActiveTabKey}
			/>
		</div>
	);
};

export default Proctoring;
