import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Divider, Modal } from "antd";

interface ModalCompProps {
	title: string;
	val: number;
	name: string;
	email: string;
	children?: React.ReactNode;
}

const { Meta } = Card;
const ModalComp = ({ title, val, name, email, children }: ModalCompProps) => {
	const [isModalOpen, setIsModalOpen] = useState(false);

	const showModal = () => {
		setIsModalOpen(true);
	};

	const handleOk = () => {
		setIsModalOpen(false);
	};

	const handleCancel = () => {
		setIsModalOpen(false);
	};

	return (
		<>
			<Button
				onClick={() => val > 0 && showModal()}
				type="link"
				disabled={val <= 0}
			>
				{val}
			</Button>
			<Modal
				title={title}
				open={isModalOpen}
				onOk={handleOk}
				footer={null}
				onCancel={handleCancel}
				styles={{ body: { padding: 0 } }}
				destroyOnClose={true}
				width={660}
			>
				<Card
					style={{
						border: "none",
					}}
				>
					<Meta
						avatar={
							<Avatar
								src={`https://api.dicebear.com/5.x/initials/svg?seed=${name}&backgroundColor=DE6834&chars=1`}
								size={48}
							/>
						}
						title={name}
						description={email}
					/>
					<Divider style={{ margin: "16px 0px" }} />
					{children}
				</Card>
			</Modal>
		</>
	);
};

export default ModalComp;
