import PreviewImageButton, { PreviewImage } from "@/utils/previewImage";
import timeDate from "@/utils/timeDate";
import { TableColumnsType } from "antd";
import { useEffect, useMemo, useState } from "react";
import { subTypeMap } from "@/constants/";
import { DataTable } from "@/components/dataTable/dataTable";
import { quizClient } from "../../../store";
interface UserProps {
	testId?: string;
	userId?: string;
	defaultSubTypeVal: number;
}

type UserEnterDataType = {
	imageUrl: string | null;
	src?: string;
	time: string;
	subType: number;
	_id: string;
	illegalObject: never[];
};

const UserProctoringCount = ({
	testId,
	userId,
	defaultSubTypeVal,
}: UserProps) => {
	const [loading, setLoading] = useState(false);
	const [proctoringData, setProctoringData] = useState<UserEnterDataType[]>(
		[]
	);
	const [activeSources, setActiveSources] = useState<string[]>([]);

	const [filteredSubType, setFilteredSubType] = useState<number[] | null>([
		defaultSubTypeVal,
	]);
	const handlePreviewClose = () => setActiveSources([]);

	const sourceIndexMap = useMemo(
		() =>
			Object.fromEntries(
				activeSources.map((source: string, index: number) => [
					source,
					index,
				])
			),
		[activeSources]
	);
	const userEnterColumns: TableColumnsType<UserEnterDataType> = useMemo(
		() => [
			{
				key: "Serial Number",
				render: (_value, _record, index) => index + 1,
				width: 55,
				fixed: "left",
			},
			{
				title: "Loggedin Time",
				key: "time",
				dataIndex: "time",
				sorter: (a, b) =>
					new Date(a.time ?? 0).getTime() -
					new Date(b.time ?? 0).getTime(),
				showSorterTooltip: false,
				render: value => (value ? timeDate(value) : "-NA-"),
			},
			{
				title: "Sub Type",
				key: "subType",
				dataIndex: "subType",
				filters: Object.entries(subTypeMap)
					.splice(1)
					.map(([key, text]) => ({
						text,
						value: Number(key),
					})),
				// defaultFilteredValue: [filteredSubType],
				filteredValue: filteredSubType || null,
				onFilter: (value, data) => {
					if (value === 0) return true;
					return data.subType === value;
				},
				filterMultiple: false,
				render: (value: number) => {
					const val = subTypeMap[value] || "-NA-";
					return val;
				},
			},
			{
				title: "Image",
				key: "imageUrl",
				dataIndex: "imageUrl",
				render: (value: string) => {
					if (!value) return "-NA-";
					return (
						<>
							<PreviewImageButton
								src={value}
								onReady={src => {
									setActiveSources(previous => [
										...previous,
										src,
									]);
								}}
							/>
						</>
					);
				},
			},
		],
		[filteredSubType]
	);

	useEffect(() => {
		if (!testId || !userId) return;
		setLoading(true);
		const fetchData = async () => {
			try {
				const response = await quizClient.getAiProctoringReport(
					testId,
					userId
				);
				setProctoringData(response);
			} catch (error) {
				console.log(error);
			} finally {
				setLoading(false);
			}
		};
		fetchData();
	}, [testId, userId, defaultSubTypeVal]);

	return (
		<>
			<DataTable<UserEnterDataType>
				columns={userEnterColumns}
				data={proctoringData}
				pagination={false}
				loading={loading}
				scroll={{ y: 200 }}
				onTableChange={(_, filters) => {
					const subType = (filters as UserEnterDataType).subType;
					setFilteredSubType([subType]); // need to fix the type
				}}
			/>
			{activeSources.length > 0 && (
				<PreviewImage
					sources={activeSources}
					onClose={handlePreviewClose}
					onDownload={url => {
						const index = sourceIndexMap[url];
						if (index === undefined) {
							return;
						}
						const record = proctoringData[index];
						return `${subTypeMap[record.subType]}`;
					}}
				/>
			)}
		</>
	);
};

export default UserProctoringCount;
