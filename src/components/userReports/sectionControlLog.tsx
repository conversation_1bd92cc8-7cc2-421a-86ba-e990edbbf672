import { useMemo } from "react";
import { TableColumnsType } from "antd";
import timeDate from "@/utils/timeDate";
import { DataTable } from "../dataTable/dataTable";
import { SubmissionDataType } from "@/testReport/data/data";

const SectionControlLog = ({
	submissionData,
	sectionTitles,
}: {
	submissionData: SubmissionDataType[] | undefined;
	sectionTitles: string[] | undefined;
}) => {
	const submissionColumns: TableColumnsType<SubmissionDataType> = useMemo(
		() => [
			{
				key: "Serial Number",
				render: (_value, _record, index) => index + 1,
				width: 30,
				fixed: "left",
			},
			{
				title: "Section Name",
				key: "segmentIndex",
				dataIndex: "segmentIndex",
				render: value =>
					value === -1
						? "Quiz Submit"
						: sectionTitles
							? sectionTitles[value]
							: "-NA-",
			},
			{
				title: "Time",
				key: "time",
				dataIndex: "time",
				sorter: (a, b) =>
					new Date(a.time ?? 0).getTime() -
					new Date(b.time ?? 0).getTime(),
				showSorterTooltip: false,
				render: value => (value ? timeDate(value) : "-NA-"),
			},
			{
				title: "Controlled By",
				key: "createdBy",
				dataIndex: "createdBy",
				render: value => (value ? value.displayname : "-NA-"),
			},
		],
		[sectionTitles]
	);

	return (
		<div>
			<>
				<DataTable<SubmissionDataType>
					columns={submissionColumns}
					data={submissionData ?? []}
					pagination={false}
				/>
			</>
		</div>
	);
};

export default SectionControlLog;
