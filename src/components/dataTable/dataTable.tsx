import {
	Table,
	TableColumnsType,
	TablePaginationConfig,
	TableProps,
} from "antd";
import {
	GetRowKey,
	TableLocale,
	TableRowSelection,
} from "antd/es/table/interface";
import { Key } from "react";
interface DataTableProps<T> {
	data: T[];
	columns: TableColumnsType<T>;
	loading?: boolean;
	totalRecords?: number;
	pageSize?: number;
	currentPage?: number;
	onTableChange?: (
		_pagination: TablePaginationConfig,
		_filter: unknown,
		_sorting: unknown
	) => void;
	onRowSelection?: (_selectedRowKeys: Key[], _selectedRows: T[]) => void;
	enableRowSelection?: boolean;
	isServerSide?: boolean;
	scroll?: TableProps["scroll"];
	pagination?: TableProps["pagination"];
	locale?: TableLocale;
	selectedKeys?: Key[];
}

export const DataTable = <T extends object | { _id: string }>({
	data,
	columns,
	loading,
	totalRecords = 0,
	pageSize = 0,
	currentPage = 0,
	onTableChange,
	onRowSelection,
	enableRowSelection = false,
	isServerSide = false,
	scroll,
	pagination,
	locale,
	selectedKeys,
}: DataTableProps<T>) => {
	const rowSelection: TableRowSelection<T> = {
		type: "checkbox",
		selectedRowKeys: selectedKeys === undefined ? [] : selectedKeys,
		onChange: (selectedKeys: Key[], selectedRows: T[]) => {
			onRowSelection?.(selectedKeys, selectedRows);
		},
	};

	const ServerSidePagination: TablePaginationConfig = {
		pageSize: pageSize,
		total: totalRecords,
		showSizeChanger: true,
		current: currentPage,
		style: {
			position: "sticky",
			bottom: "0px",
			padding: "0.75em",
			backgroundColor: "white",
			margin: 0,
		},
		position: ["bottomCenter"],
	};
	const getRowKey: GetRowKey<T> = (record: T, index?: number) => {
		if ("_id" in record && record._id) return record._id;
		if (index !== undefined) return `row-${index}`;
		return crypto.randomUUID();
	};
	return (
		<Table
			rowKey={getRowKey}
			dataSource={data}
			columns={columns}
			loading={loading}
			size="large"
			pagination={isServerSide ? ServerSidePagination : pagination}
			onChange={(pagination, filters, sorter) => {
				onTableChange?.(pagination, filters, sorter);
			}}
			rowSelection={enableRowSelection ? rowSelection : undefined}
			scroll={scroll}
			locale={locale}
		></Table>
	);
};
