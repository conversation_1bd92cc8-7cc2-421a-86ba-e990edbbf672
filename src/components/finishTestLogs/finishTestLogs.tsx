import { Modal, Space, TableColumnsType, Tag, Tooltip, Typography } from "antd";
import { useCallback, useEffect, useState } from "react";
import { quizClient } from "../../store";
import timeDate from "@/utils/timeDate";
import { DataTable } from "../dataTable/dataTable";

interface FinishTestLogsDataType {
	name: string;
	email: string;
	ip: string;
	status: boolean;
	time: string;
	successUserIds: number;
	failedUserIds: number;
}

const finishLogColumns: TableColumnsType<FinishTestLogsDataType> = [
	{
		key: "Serial Number",
		render: (_value, _record, index) => `${index + 1}.`,
		width: 65,
		align: "left",
	},
	{
		title: "Submitted By",
		key: "submittedBy",
		render: (_value, record) => {
			return (
				<Space direction="vertical" size={2}>
					<Typography.Text>{record.name}</Typography.Text>
					<Typography.Text style={{ whiteSpace: "nowrap" }}>
						{record.email}
					</Typography.Text>
				</Space>
			);
		},
	},
	{
		title: "IP",
		key: "ip",
		dataIndex: "ip",
		align: "center",
		render: value => value,
	},
	{
		title: "Status",
		key: "status",
		align: "center",
		render: (value, record) => {
			if (!value) {
				return "OTP is not valid";
			}
			return (
				<Space size={1}>
					<Tooltip title="Success User Ids">
						<Tag color="#87d068">{record.successUserIds}</Tag>
					</Tooltip>
					<Tooltip title="Failed User Ids">
						<Tag color="#f50">{record.failedUserIds}</Tag>
					</Tooltip>
				</Space>
			);
		},
	},
	{
		title: "Time",
		key: "time",
		dataIndex: "time",
		align: "center",
		render: value => (value ? value : "-NA-"),
	},
];

const FinishTestLogs = ({
	id,
	onClose,
	isFinishTestLogsOpen,
}: {
	id: string | undefined;
	onClose: () => void;
	isFinishTestLogsOpen: boolean;
}) => {
	const [loading, setLoading] = useState(false);
	const [finsihLogData, setFinishLogData] = useState<
		FinishTestLogsDataType[]
	>([]);

	const fetchData = useCallback(
		async (id: string) => {
			setLoading(true);
			try {
				const { userData, logs } =
					await quizClient.getFinishDescriptionLogs(id);
				const usersMap = new Map(
					userData.map(user => [user._id, user])
				);
				const finishLogData: FinishTestLogsDataType[] = logs.reduce(
					(acc, entry) => {
						const successUserIds = entry.data.userIds ?? [];
						const failedUserIds =
							entry.data.failedUserIds?.filter(
								userId => !successUserIds.includes(userId)
							) ?? [];
						const createdByUser = usersMap.get(entry.createdBy);
						if (createdByUser) {
							acc.push({
								name: createdByUser.displayname,
								email: createdByUser.email,
								ip: entry.data.ip ?? "-NA-",
								status: !entry.data?.error,
								successUserIds: successUserIds.length,
								failedUserIds: failedUserIds.length,
								time: timeDate(entry.activityTime),
							});
						}
						return acc;
					},
					[] as FinishTestLogsDataType[]
				);
				setFinishLogData(finishLogData);
			} catch (ex) {
				console.log(ex);
			} finally {
				setLoading(false);
			}
		},
		[setLoading]
	);

	useEffect(() => {
		if (!id) return;
		fetchData(id);
	}, [fetchData, id]);

	const handleCancel = () => {
		onClose();
	};

	return (
		<Modal
			open={isFinishTestLogsOpen}
			title="Submitted Test Logs"
			onCancel={handleCancel}
			footer={null}
			destroyOnClose={true}
			loading={loading}
			centered
			width={"auto"}
		>
			<DataTable<FinishTestLogsDataType>
				columns={finishLogColumns}
				data={finsihLogData}
				pagination={false}
				scroll={{ y: 400 }}
			/>
		</Modal>
	);
};

export default FinishTestLogs;
