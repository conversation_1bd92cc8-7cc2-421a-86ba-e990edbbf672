import { Divider, Form, Input } from "antd";
import { useForm } from "antd/es/form/Form";
import { TabsRequestMap } from "../test-edit-layout";
import SwitchCard from "../form/switch-card";
import TestEditBaseForm from "./test-edit-form";
import Quill from "../quill/quill";

type TestNotificationsFields = TabsRequestMap["notifications"];

const infoVariables: {
	[key: string]: { field: string; templateString: string; label: string };
} = {
	name: { field: "displayname", templateString: "name", label: "User Name" },
	testTitle: {
		field: "title",
		templateString: "testTitle",
		label: "Test Title",
	},
	// testInviteLink: {
	// 	field: "testInviteLink",
	// 	templateString: "testInviteLink",
	// 	label: "Invite Link",
	// },
	// testDurationInMins: {
	// 	field: "testDurationInMins",
	// 	templateString: "testDurationInMins",
	// 	label: "Test Duration (mins)",
	// },
	rollNumber: {
		field: "enrollmentId",
		templateString: "rollNumber",
		label: "Roll Number",
	},
	senderName: {
		field: "senderName",
		templateString: "senderName",
		label: "Sender Name",
	},
	senderEmail: {
		field: "senderEmail",
		templateString: "senderEmail",
		label: "Sender Email",
	},
};

export default function TestNotificationsEditForm() {
	const [form] = useForm<TestNotificationsFields>();

	return (
		<TestEditBaseForm
			tabKey="notifications"
			form={form}
			submitButtonConfig={{ content: { idleState: "Save" } }}
		>
			<SwitchCard
				form={form}
				name="candidate.sendEmail"
				labelTitle="Send emails to candidate"
			>
				<Form.Item
					name="candidate.subject"
					label="Test completion subject"
				>
					<Input />
				</Form.Item>
				<Divider style={{ marginTop: "1em", marginBottom: "1em" }} />
				<Form.Item
					name="candidate.message"
					label="Test completion message"
				>
					<Quill templateVariable={infoVariables} />
				</Form.Item>
			</SwitchCard>

			<SwitchCard
				form={form}
				name="organisation.sendEmail"
				labelTitle="Send emails to organisation"
			>
				<Form.Item
					name="organisation.subject"
					label="Test completion subject"
				>
					<Input />
				</Form.Item>
				<Divider style={{ marginTop: "1em", marginBottom: "1em" }} />
				<Form.Item
					name="organisation.message"
					label="Test completion message"
				>
					<Quill templateVariable={infoVariables} />
				</Form.Item>
			</SwitchCard>
		</TestEditBaseForm>
	);
}
