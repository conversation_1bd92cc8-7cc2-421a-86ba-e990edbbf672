import { Form, Input, InputNumber } from "antd";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import TestEditBaseForm from "./test-edit-form";
import { TabsRequestMap } from "../test-edit-layout";
import { useOutletContext, useSearchParams } from "react-router";
import { AppConfig } from "@/config";
import { PlatformOrganisation } from "@/constants";
import { TextLayoutContextType } from "../test-layout/test-layout";
import { testTitleValidator } from "@/testList/common/common";

const defaultInstructions: ReactQuill.Value = `<ol><li>Make sure you have a good Internet Connection.</li><li>It is recommended to install the latest version of the Chrome web browser for the competition.</li><li>Once you start the test, it is recommended to pursue it in one go for the complete duration.</li><li>Do not press the back button of the browser during the test.</li><li>If you face any technical issues during the test, please send a mail to ${AppConfig.platformOrganisation === PlatformOrganisation.CHITKARA ? "<EMAIL>" : "<EMAIL>"} with a snapshot of the issue.</li></ol>`;

const customMenu = [
	[{ header: [1, 2, 3, 4, 5, 6, false] }],
	[{ indent: "-1" }, { indent: "+1" }],
	["bold", "italic", "underline", "strike"],
	["image"],
	["clean"],
];

export default function TestDetailsEditForm() {
	const [searchParams] = useSearchParams();
	const testTitle = searchParams.get("title") ?? "<no title>";
	const { isFrozen, isFromToken } = useOutletContext<TextLayoutContextType>();
	return (
		<TestEditBaseForm
			tabKey="general"
			initialValues={{
				title: testTitle,
				instructions: defaultInstructions as string,
			}}
		>
			<Form.Item<TabsRequestMap["general"]>
				name="title"
				label="Test title"
				required
				rules={[
					{
						validator: testTitleValidator,
					},
				]}
			>
				<Input placeholder="Type test title here" maxLength={125} />
			</Form.Item>

			<Form.Item<TabsRequestMap["general"]>
				name="quizTime"
				label="Duration"
				required
				tooltip="Time to attempt test (in minutes)"
				normalize={value => {
					if (value === null || value === undefined) return null;
					return Number.parseInt(value);
				}}
				rules={[
					{
						required: true,
						validator: (_, value) => {
							if (value === null || value === undefined) {
								return Promise.reject(
									new Error("Test duration is required")
								);
							}
							if (value < 1 || value > 99999) {
								return Promise.reject(
									new Error(
										"Duration Can Be In Range [1 - 99999]"
									)
								);
							}
							return Promise.resolve();
						},
					},
				]}
			>
				<InputNumber
					style={{ width: "100%" }}
					placeholder="Type test duration here"
					maxLength={5}
				/>
			</Form.Item>

			<Form.Item<TabsRequestMap["general"]>
				name="instructions"
				label="Instructions"
				tooltip="These instructions will be shown to the candidates before the test begins."
			>
				<ReactQuill
					theme="snow"
					bounds=".test-edit-form-container"
					readOnly={isFrozen || isFromToken}
					className="test-edit-form-container"
					modules={{
						toolbar: customMenu,
					}}
				/>
			</Form.Item>
		</TestEditBaseForm>
	);
}
