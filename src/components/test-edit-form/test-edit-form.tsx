import { useOutletContext } from "react-router";
import { TabsRequestMap, TestEditLayoutContextType } from "../test-edit-layout";
import { Button, Divider, Form, GetProps, Space, Typography } from "antd";
import { LoadingOutlined } from "@ant-design/icons";
import React, { useCallback, useEffect, useState } from "react";
import formStyle from "./form.module.css";
import { tabsData } from "@/constants";

type FormState = "idle" | "submitting" | "unsaved";

type ButtonConfig<C extends boolean = true> =
	| false
	| {
			content: never;
			render: (formState: FormState) => React.ReactNode;
	  }
	| {
			render?: never;
			content?: { [K in FormState as `${K}State`]?: React.ReactNode };
			onClick?: C extends true ? () => void : never;
	  };

interface TestEditBaseFormsProps<K extends keyof TabsRequestMap>
	extends GetProps<typeof Form<TabsRequestMap[K]>> {
	tabKey: K;
	initialValues?: Partial<TabsRequestMap[K]>;
	backButtonConfig?: ButtonConfig;
	submitButtonConfig?: ButtonConfig<false>;
	onValuesChange?: (
		fields: Partial<TabsRequestMap[K]>,
		values: TabsRequestMap[K]
	) => void;
	// onFormStateChange?: (state: "submitting" | "idle") => void;
}

export default function TestEditBaseForm<K extends keyof TabsRequestMap>({
	form,
	tabKey,
	submitButtonConfig,
	children,
	...props
}: TestEditBaseFormsProps<K>) {
	const [formInstance] = Form.useForm<TabsRequestMap[K]>(form);
	const context = useOutletContext<TestEditLayoutContextType>();
	const [formState, setFormState] = useState<FormState>("idle");

	async function submitForm(values: TabsRequestMap[K]) {
		setFormState("submitting");
		await context.saveTab(tabKey, values);
		setFormState("idle");
	}

	const resetFieldsValue = useCallback(
		function () {
			const formData = context.tabsData[tabKey];
			if (formData) {
				formInstance.setFieldsValue(formData);
			} else {
				formInstance.resetFields();
			}
			context.removeUnsavedTab(tabKey);
		},
		[context.tabsData, formInstance, tabKey]
	);

	useEffect(() => {
		resetFieldsValue();
	}, [resetFieldsValue]);

	const showNextButton =
		(context.isFrozen && context.canVisitNextTab() === true) ||
		(context.canVisitNextTab() && !context.unsavedTabs.includes(tabKey));

	const showSubmitButton =
		!showNextButton &&
		(context.unsavedTabs.includes(tabKey) || context.isPartialCreated());

	return (
		<Form
			{...props}
			form={formInstance}
			layout="vertical"
			size="middle"
			scrollToFirstError
			className={formStyle.form}
			disabled={context.isFrozen || context.isFromToken}
			onFinish={submitForm}
			onValuesChange={(fields: Partial<TabsRequestMap[K]>, values) => {
				props.onValuesChange?.(fields, values);
				context.addUnsavedTab(tabKey);
			}}
		>
			<Space direction="vertical">
				<Typography.Title level={3} style={{ marginBottom: "-4px" }}>
					{tabsData[tabKey].title}
				</Typography.Title>
				<Typography.Text type="secondary">
					{tabsData[tabKey].description}
				</Typography.Text>
			</Space>
			<Divider style={{ margin: "-4px 0px 0px" }} />
			{children}
			<Space
				style={{
					position: "sticky",
					margin: "-1rem 0px -1.5rem",
					padding: "1rem 0px",
					bottom: 0,
					backgroundColor: "#f1f1f1",
				}}
			>
				{context.submissionType !== "add" &&
				context.unsavedTabs.includes(tabKey) ? (
					<FormButton
						formState={formState}
						config={{ onClick: resetFieldsValue }}
					>
						Discard
					</FormButton>
				) : (
					<FormButton
						formState={formState}
						config={
							context.canVisitPreviousTab()
								? {
										onClick: context.visitPreviousTab,
									}
								: false
						}
					>
						Previous
					</FormButton>
				)}
				{showNextButton ? (
					<FormButton
						formState={formState}
						config={
							context.canVisitNextTab()
								? { onClick: context.visitNextTab }
								: false
						}
					>
						Next
					</FormButton>
				) : showSubmitButton ? (
					<FormSubmitButton
						formState={formState}
						config={submitButtonConfig}
						idleStateContent={
							context.submissionType === "add"
								? "Create & next"
								: context.isPartialCreated()
									? "Save & next"
									: "Save"
						}
					/>
				) : null}
			</Space>
		</Form>
	);
}

function FormButton(props: {
	formState: FormState;
	config?: ButtonConfig;
	children: React.ReactNode;
}) {
	if (props.config === false) {
		return <></>;
	}
	if (typeof props.config?.render === "function") {
		return props.config.render(props.formState);
	}
	return (
		<Form.Item>
			<Button
				htmlType="button"
				type="default"
				disabled={props.formState === "submitting"}
				onClick={props.config?.onClick}
				{...props}
			>
				{props.config?.content?.idleState ?? props.children}
			</Button>
		</Form.Item>
	);
}

function FormSubmitButton(props: {
	formState: FormState;
	config?: ButtonConfig<false>;
	idleStateContent: React.ReactNode;
}) {
	if (props.config === false) {
		return <></>;
	}
	if (typeof props.config?.render === "function") {
		return props.config.render(props.formState);
	}
	return (
		<Form.Item>
			<Button
				htmlType="submit"
				type="primary"
				disabled={props.formState === "submitting"}
				{...props}
			>
				{props.formState === "submitting" ? (
					<Space>
						<LoadingOutlined />
						{props.config?.content?.submittingState ?? "saving"}
					</Space>
				) : (
					(props.config?.content?.idleState ?? props.idleStateContent)
				)}
			</Button>
		</Form.Item>
	);
}
