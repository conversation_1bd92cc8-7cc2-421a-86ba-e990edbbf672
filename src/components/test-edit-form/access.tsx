import React, { useEffect, useState } from "react";
import {
	Check<PERSON><PERSON>cleFilled,
	CloseCircleFilled,
	CloseCircleOutlined,
	GlobalOutlined,
	LoadingOutlined,
	LockFilled,
} from "@ant-design/icons";
import {
	Button,
	DatePicker,
	Flex,
	Form,
	Input,
	Popconfirm,
	Space,
	Typography,
} from "antd";
import formStyle from "./form.module.css";
import SwitchCard, { SegmentedCard } from "../form/switch-card";
import Tags from "../tags";
import dayjs from "dayjs";
import { TabsRequestMap, TestEditLayoutContextType } from "../test-edit-layout";
import { useOutletContext } from "react-router";
import { AppConfig } from "@/config";
import TestEditBaseForm from "./test-edit-form";
import { ValidatorRule } from "rc-field-form/lib/interface";
import { quizClient } from "../../store";

const IPv4Regex =
	/^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;

// Custom validation function for IP addresses
const validateIPAddresses: ValidatorRule["validator"] = (
	_rule,
	value: string[]
) => {
	for (const ip of value) {
		if (!IPv4Regex.test(ip)) {
			return Promise.reject(
				// `"${ip}" is not a valid IP address!`
				`One or more provided IP addresses are not valid!`
			);
		}
	}
	return Promise.resolve();
};

type TestAccessFields = TabsRequestMap["access"];

const linkAvailabilityStatesMap = {
	empty: {
		label: undefined,
		icon: undefined,
		color: "initial",
	},
	available: {
		label: "Available",
		icon: <CheckCircleFilled />,
		color: "green",
	},
	unavailable: {
		label: "Unvailable",
		icon: <CloseCircleFilled />,
		color: "red",
	},
	checking: {
		label: "Checking",
		icon: <LoadingOutlined />,
		color: "gray",
	},
};

export default function TestAccessEditForm() {
	const context = useOutletContext<TestEditLayoutContextType>();

	const [form] = Form.useForm<TestAccessFields>();
	const startTime: dayjs.Dayjs | undefined = Form.useWatch("startTime", {
		form,
		preserve: true,
	});
	const endTime = Form.useWatch("endTime", { form, preserve: true });
	const isNotPrivate = Form.useWatch("isNotPrivate", {
		form,
		preserve: true,
	});
	const allowedIP =
		Form.useWatch("allowedIP", { form, preserve: true }) ?? [];
	const minDate = dayjs(startTime);
	const maxDate = dayjs(endTime);
	const invalidIPAddresses = allowedIP.filter(
		ipAddress => !IPv4Regex.test(ipAddress)
	);

	const isEndTimeUpdated =
		context.tabsData.access?.endTime?.valueOf() !== endTime?.valueOf();
	const [linkAvailabilityState, setLinkAvailabilityState] =
		useState<keyof typeof linkAvailabilityStatesMap>("available");
	const linkAvailabilityStateValue =
		linkAvailabilityStatesMap[linkAvailabilityState];
	const [linkInputTimeout, setLinkInputTimeout] = useState<NodeJS.Timeout>();

	async function checkLinkAvailability(link: string) {
		try {
			const isAvailable = await quizClient.checkLinkAvailability(link);
			return isAvailable ? "available" : "unavailable";
		} catch (ex) {
			console.log(ex);
			return "unavailable";
		}
	}

	function onLinkInputChange(event: React.ChangeEvent<HTMLInputElement>) {
		const value = event.target.value.trim();
		setLinkAvailabilityState("checking");
		clearTimeout(linkInputTimeout);
		if (value === "") {
			return setLinkAvailabilityState("empty");
		} else if (value === context?.tabsData?.access?.link) {
			setLinkAvailabilityState("available");
			return;
		}
		setLinkInputTimeout(
			setTimeout(async function () {
				setLinkAvailabilityState(await checkLinkAvailability(value));
			}, 500)
		);
	}

	useEffect(() => {
		if (startTime && endTime) {
			form.validateFields(["startTime"]);
		}
	}, [endTime, form, startTime]);

	useEffect(() => {
		if (!endTime) return;
		const isShowResultsDisabled = dayjs(endTime).isAfter(dayjs());

		if (isShowResultsDisabled) {
			form.setFieldValue("showResults", false);
		}
	});

	return (
		<TestEditBaseForm
			tabKey="access"
			form={form}
			initialValues={{
				isNotPrivate: false,
				startTime: dayjs(Date.now()),
				showResults: false,
				allowedIP: [],
			}}
		>
			<Flex align="flex-start" gap="1rem">
				<Form.Item
					name="startTime"
					label="Start time"
					required
					rules={[
						{ required: true, message: "Start time is required" },
						{
							validator(_rule, value) {
								if (
									value &&
									endTime &&
									maxDate &&
									value.valueOf() >= maxDate.valueOf()
								) {
									return Promise.reject(
										"Start time cannot be greater than or equal to end time"
									);
								}
								return Promise.resolve();
							},
						},
					]}
				>
					<DatePicker
						maxDate={endTime ? maxDate : undefined}
						showTime
						format={"YYYY-MM-DD HH:mm"}
						showNow={false}
					/>
				</Form.Item>
				<Form.Item
					name="endTime"
					label="End time"
					validateTrigger={["onChange"]}
					rules={[
						{
							validator(_rule, value) {
								if (!isEndTimeUpdated) {
									return Promise.resolve();
								}
								if (value) {
									if (value.valueOf() <= dayjs().valueOf()) {
										return Promise.reject(
											"End time cannot be less than or equal to current time"
										);
									}
									if (
										startTime &&
										value.valueOf() <= minDate.valueOf()
									) {
										return Promise.reject(
											"End time cannot be less than or equal to start time"
										);
									}
								}
								return Promise.resolve();
							},
						},
					]}
				>
					<DatePicker
						minDate={minDate}
						showTime
						format={"YYYY-MM-DD HH:mm"}
						showNow={false}
						onChange={date => {
							if (!date) {
								form.setFieldValue("showResults", false);
							}
						}}
					/>
				</Form.Item>
				{/* <Form.Item
					name="entryStopTime"
					label="Entry Stop time"
					rules={[
						{
							validator(_rule, value) {
								if (
									value &&
									startTime &&
									minDate &&
									value.valueOf() <= minDate.valueOf()
								) {
									return Promise.reject(
										"Entry stop time cannot be less than or equal to start time"
									);
								}
								if (
									value &&
									endTime &&
									maxDate &&
									value.valueOf() > maxDate.valueOf()
								) {
									return Promise.reject(
										"Entry stop time cannot be greater than to end time"
									);
								}
								return Promise.resolve();
							},
						},
					]}
				>
					<DatePicker
						minDate={minDate}
						maxDate={endTime ? maxDate : undefined}
						showTime
						format={"YYYY-MM-DD HH:mm"}
						showNow={false}
					/>
				</Form.Item> */}
			</Flex>

			<SegmentedCard
				labelTitle="Availability"
				labelDescription={
					isNotPrivate
						? "The 	 can be accessed via the test link and code."
						: "Only individuals with invited email addresses will be able to access the test."
				}
				form={form}
				disabled={context.isFrozen || context.isFromToken}
				name="isNotPrivate"
				alignHeaderItems="flex-start"
				segmentedOptions={[
					{
						value: true,
						label: (
							<Space>
								<GlobalOutlined />
								Public
							</Space>
						),
					},
					{
						value: false,
						label: (
							<Space>
								<LockFilled />
								Private
							</Space>
						),
					},
				]}
			>
				<Flex className={formStyle.form}>
					<Form.Item
						label="Link"
						name="link"
						tooltip="To access and log into the test."
						rules={[
							{
								validator() {
									if (
										linkAvailabilityState === "unavailable"
									) {
										return Promise.reject(
											"Link is not available"
										);
									}
									return Promise.resolve();
								},
							},
							{
								pattern: /^[a-zA-Z0-9-]{1,30}$/,
								message: "Link is not valid",
							},
						]}
					>
						<Input
							disabled={
								!isNotPrivate ||
								context.isFrozen ||
								context.isFromToken
							}
							addonBefore={
								<Typography.Text type="secondary">
									{AppConfig.testAttemptURL + "/"}
								</Typography.Text>
							}
							onChange={onLinkInputChange}
							addonAfter={
								<span
									style={{
										color: linkAvailabilityStateValue.color,
									}}
								>
									{linkAvailabilityStateValue.icon}
									&nbsp;
									<span style={{ fontSize: "small" }}>
										{linkAvailabilityStateValue.label}
									</span>
								</span>
							}
						/>
					</Form.Item>
					<Form.Item
						label="Code"
						name="quizCode"
						tooltip="Test code"
						required={isNotPrivate}
						rules={
							isNotPrivate
								? [
										{
											required: true,
											message: "Test code is required",
										},
										{
											pattern: /^[1-9][0-9]*$/,
											message:
												"Test code must be numeric and cannot start with zero..",
										},
									]
								: undefined
						}
					>
						<Input
							disabled={
								!isNotPrivate ||
								context.isFrozen ||
								context.isFromToken
							}
							placeholder="test code"
							style={{ width: "100%" }}
							maxLength={8}
						/>
					</Form.Item>
				</Flex>
			</SegmentedCard>

			<Flex gap="middle" flex="1">
				<Form.Item<TestAccessFields>
					name="allowedIP"
					label={
						<>
							<span>Allowed IP addresses</span>
							{allowedIP.length > 0 && (
								<Popconfirm
									title="Clear all IP addresses"
									description="Are you sure you want to remove all values?"
									onConfirm={() =>
										form.setFieldValue("allowedIP", [])
									}
									okText="Yes"
									cancelText="No"
								>
									<Button
										title="Clear all"
										size="small"
										type="text"
										icon={<CloseCircleOutlined />}
										disabled={
											context.isFromToken ||
											context.isFrozen
										}
									/>
								</Popconfirm>
							)}
						</>
					}
					style={{ width: "100%" }}
					rules={[{ validator: validateIPAddresses }]}
				>
					<Tags
						separator={/,|\s/}
						invalidValues={invalidIPAddresses}
						isDisabled={context.isFrozen || context.isFromToken}
					/>
				</Form.Item>
			</Flex>

			<SwitchCard
				form={form}
				name="showResults"
				labelTitle="Show Results"
				labelDescription="Allow users to see their attempts after the test has ended."
				disabled={
					!endTime ||
					dayjs(endTime).isAfter(dayjs()) ||
					context.isFromToken ||
					context.isFrozen
				}
			/>
		</TestEditBaseForm>
	);
}
