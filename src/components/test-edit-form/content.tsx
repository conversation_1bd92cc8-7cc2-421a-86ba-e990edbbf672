import React, { useEffect, useState } from "react";
import {
	CloseOutlined,
	DeleteOutlined,
	EditOutlined,
	SaveOutlined,
} from "@ant-design/icons";
import {
	But<PERSON>,
	Card,
	Dropdown,
	Flex,
	Form,
	Input,
	InputNumber,
	message,
	Select,
	Switch,
	Table,
} from "antd";
import Tags from "../tags";
import SwitchCard from "../form/switch-card";
import DragSortTable from "../drag-sort-table";
import confirm from "antd/es/modal/confirm";
import useObjectList from "@/hooks/util";
import { useNavigate, useOutletContext } from "react-router";
import { TestEditLayoutContextType } from "../test-edit-layout";
import { TestData, TestSaveProgressStep } from "@/client/test-add";
import TestEditBaseForm from "./test-edit-form";
import { useAppStore } from "../../store";
import { PlatformOrganisation } from "@/constants";
import { AppConfig } from "@/config";
import {
	CustomField,
	CustomFieldType,
	CustomFieldTypeMap,
	defaultCustomFields,
	defaultFieldKeys,
} from "@/constants/test";

type TestContentFields = TestData["tabs"]["content"];

const FlexRowItemStyle: React.CSSProperties = {
	width: "calc(100% / 3 - 16px)",
	flexGrow: "1",
	flexShrink: "0",
};

// defaultCustomFields.push({
// 	key: "ha",
// 	label: "Ha",
// 	type: CustomFieldType.SELECT,
// 	active: true,
// 	values: ["a", "b", "c", "d"],
// });

const customFields = defaultCustomFields.map<CustomField>(field => ({
	...field,
	editable: false,
	inEditMode: false,
}));

const TextFieldAllowedPattern = /^[a-zA-Z0-9.@&/\\-_ $]+$/;
const TextInvalidCharRegex = /[^a-zA-Z0-9.@&/\\-_ $]/g;

export default function TestContentEditForm() {
	const navigate = useNavigate();
	const [form] = Form.useForm<TestContentFields>();
	const shuffleQuestions = Form.useWatch("shuffleQuestions", { form });
	const randomQuestions = Form.useWatch("randomQuestions", { form });
	const context = useOutletContext<TestEditLayoutContextType>();
	const questionLanguagesAllowedToOwner = useAppStore(
		state => state.questionLanguagesAllowed
	);
	const randomQuestionsDisabled =
		context.isFrozen ||
		context.isFromToken ||
		(!context.disabledTabs.has(
			PlatformOrganisation.CHITKARA === AppConfig.platformOrganisation
				? "proctoring"
				: "notifications"
		)
			? true
			: false);
	const shouldDisableShuffleQuestions =
		randomQuestions && randomQuestionsDisabled;

	useEffect(() => {
		if (
			shuffleQuestions &&
			context.disabledTabs.has(
				PlatformOrganisation.CHITKARA === AppConfig.platformOrganisation
					? "proctoring"
					: "notifications"
			)
		) {
			form.setFieldValue("randomQuestions", false);
		}
	}, [context.disabledTabs, form, shuffleQuestions]);

	useEffect(() => {
		if (randomQuestions) {
			form.setFieldValue("shuffleQuestions", false);
		}
	}, [form, randomQuestions]);

	useEffect(() => {
		if (context.progressStep === undefined) {
			return;
		}
		if (context.progressStep < TestSaveProgressStep.CONTENT) {
			const defaultLanguageCode: string[] =
				questionLanguagesAllowedToOwner.map(l => l.code);

			form.setFieldValue("customFields", customFields);
			form.setFieldValue("languagesAllowed", defaultLanguageCode);
			return;
		}
	}, [context.progressStep, form, navigate, questionLanguagesAllowedToOwner]);

	// if (context.disabledTabs.has("content")) {
	// 	return <Navigate to={"../general"} />;
	// }

	return (
		<TestEditBaseForm
			tabKey="content"
			form={form}
			initialValues={{
				customFields,
				languagesAllowed: questionLanguagesAllowedToOwner.map(
					l => l.code
				),
			}}
			onValuesChange={() => {}}
		>
			<Flex gap="middle" flex="1">
				<Form.Item
					// labelCol={{ xs: { span: 24 }, sm: { span: 60 } }}
					// wrapperCol={{ xs: { span: 24 }, sm: { span: 60 } }}
					name="cutOffMarks"
					label="Cut-off Marks"
					tooltip="Applicants scoring above the cut-off will qualify automatically."
					style={FlexRowItemStyle}
				>
					<InputNumber
						min={0}
						maxLength={5}
						style={{ width: "100%" }}
					/>
				</Form.Item>
				<Form.Item
					name="languagesAllowed"
					label="Languages Allowed"
					style={FlexRowItemStyle}
				>
					<Select
						placeholder="Please select languages"
						options={questionLanguagesAllowedToOwner.map(
							language => ({
								value: language.code,
								label: language.name,
							})
						)}
						filterOption={(input, option) =>
							(option?.label ?? "")
								.toLowerCase()
								.includes(input.trim().toLowerCase())
						}
						optionFilterProp="label"
						mode="multiple"
						allowClear
					/>
				</Form.Item>
			</Flex>

			<Flex gap="middle" flex="1" style={{ width: "100%" }}>
				<Form.Item
					name="keywords"
					label="Keywords"
					style={{ width: "100%" }}
				>
					<Tags separator={/,|\s/} />
				</Form.Item>
			</Flex>

			<SwitchCard
				form={form}
				name="shuffleQuestions"
				labelTitle="Shuffle Questions"
				labelDescription="The order of questions will be different for each candidate."
				disabled={
					shouldDisableShuffleQuestions ||
					context.isFrozen ||
					context.isFromToken
				}
			/>

			<SwitchCard
				form={form}
				name="randomQuestions"
				labelTitle="Randomise Questions"
				labelDescription="With this, random questions will be picked from the questions. You can determine how many."
				disabled={randomQuestionsDisabled}
			/>

			<SwitchCard
				form={form}
				name="ShuffleMCQOptions"
				labelTitle="Shuffle MCQ Options"
				labelDescription="Choose if multiple-choice options are shuffled for each candidate."
				disabled={context.isFromToken || context.isFrozen}
			/>

			<SwitchCard
				form={form}
				name="revisitSections"
				labelTitle="Allow Section Revisit"
				labelDescription="Choose if candidates are allowed to revisit sections that are already submitted."
				disabled={context.isFromToken || context.isFrozen}
			/>

			<Card
				title="Custom Fields"
				// extra={
				// 	<Dropdown
				// 		menu={{
				// 			items: [
				// 				{
				// 					type: "item",
				// 					key: "remove-all-custom-fields",
				// 					label: "Remove all",
				// 				},
				// 			],
				// 		}}
				// 	>
				// 		<Button type="text" size="small">
				// 			<MenuOutlined />
				// 		</Button>
				// 	</Dropdown>
				// }
				styles={{ body: { padding: "1px 0px 0px" } }}
			>
				<Form.Item<TestContentFields>
					name="customFields"
					rules={[
						{
							async validator(
								_rule,
								value: TestContentFields["customFields"]
							) {
								for (const field of value) {
									if (field.key === "") {
										throw "One or more fields are not set";
									}
								}
							},
						},
					]}
				>
					<CustomFieldsCardBody />
				</Form.Item>
			</Card>
		</TestEditBaseForm>
	);
}

function CustomFieldsCardBody(props: {
	value?: CustomField[];
	onChange?: (_value: CustomField[]) => void;
}) {
	const customFieldsList = useObjectList(props.value ?? [], props.onChange);
	const context = useOutletContext<TestEditLayoutContextType>();

	const hasPendingFieldChanges = customFieldsList.items.some(
		field => field.inEditMode
	);

	const customFieldTypeEntries = Object.entries(CustomFieldTypeMap).slice(
		1
	) as unknown as [
		keyof typeof CustomFieldTypeMap,
		(typeof CustomFieldTypeMap)[keyof typeof CustomFieldTypeMap],
	][];

	return (
		<>
			<DragSortTable
				expandable={{
					childrenColumnName: "Values",
					columnWidth: "20px",
					defaultExpandedRowKeys: customFieldsList.items.map(field =>
						field.values?.length === 0 ? field.key : ""
					),
					expandedRowRender(record, index) {
						return (
							<Flex style={{ padding: "0px 3rem" }}>
								<Tags
									value={record.values}
									onChange={value =>
										customFieldsList.updateProperty(
											index,
											"values",
											value
										)
									}
								/>
							</Flex>
						);
					},
					expandedRowClassName: "expanded-row-no-z-index",
					rowExpandable(record) {
						return record.type !== CustomFieldType.TEXT;
					},
				}}
				columns={[
					{
						align: "center",
						width: "80px",
						render(_value, record, index) {
							if (defaultFieldKeys.has(record.key)) {
								return (
									<Switch
										size="small"
										checked={record.active}
										onChange={checked =>
											customFieldsList.updateProperty(
												index,
												"active",
												checked
											)
										}
									/>
								);
							}
						},
					},
					{
						dataIndex: "label",
						title: "Field",
						render(data, record, index) {
							return (
								<EditableCustomField
									data={data}
									record={record}
									index={index}
									customFieldsList={customFieldsList}
								/>
							);
						},
					},
					{
						dataIndex: "type",
						title: "Type",
						render(data: CustomFieldType) {
							return CustomFieldTypeMap[data].title;
						},
					},
					Table.EXPAND_COLUMN,
					{
						title: "Values",
						align: "center",
						width: "70px",
						render: (_data, record) => record.values?.length ?? "",
					},
					{
						title: "Actions",
						align: "center",
						render: (data, record, index) =>
							record.editable ? (
								<>
									<Button
										type="text"
										size="small"
										disabled={!data.editable}
										onClick={() => {
											confirm({
												title: "Are you sure you want to delete this field?",
												type: "confirm",
												onOk() {
													customFieldsList.remove(
														index
													);
												},
											});
										}}
									>
										<DeleteOutlined />
									</Button>
								</>
							) : (
								<></>
							),
					},
				]}
				dataSource={customFieldsList.items}
				moveData={customFieldsList.move}
				disabled={
					hasPendingFieldChanges ||
					context.isFrozen ||
					context.isFromToken
				}
			/>
			{
				<Dropdown.Button
					type="dashed"
					htmlType="button"
					style={{ margin: "1em" }}
					disabled={
						hasPendingFieldChanges ||
						context.isFrozen ||
						context.isFromToken
					}
					menu={{
						items: customFieldTypeEntries.map(([type, field]) => ({
							key: type,
							type: "item",
							label: `Add ${field.title} field`,
							onClick() {
								customFieldsList.add({
									editable: true,
									label: "",
									key: "",
									type: type,
									values: [],
									inEditMode: true,
									active: true,
								});
							},
						})),
					}}
					onClick={() => {
						customFieldsList.add({
							editable: true,
							label: "",
							key: "",
							type: CustomFieldType.TEXT,
							inEditMode: true,
							active: true,
						});
					}}
				>
					Add text field
				</Dropdown.Button>
			}
		</>
	);
}

function generateFieldKeyFromLabel(label: string) {
	return label
		.toLowerCase()
		.split(" ")
		.map((el, i) => {
			if (i) {
				return el.charAt(0).toUpperCase() + el.slice(1);
			}

			return el;
		})
		.join("");
}

function EditableCustomField(props: {
	data: string;
	record: CustomField;
	index: number;
	customFieldsList: ReturnType<typeof useObjectList<CustomField>>;
}) {
	const [messageInstance, contextHolder] = message.useMessage({
		maxCount: 1,
	});
	const { data, record, index, customFieldsList } = props;
	const userCustomFields = new Set(
		customFieldsList.items.map(field => field.key)
	);
	const [value, setValue] = useState(data);
	return (
		<Flex align="center" gap="0.5em">
			{contextHolder}
			{record.inEditMode ? (
				<Input
					size="small"
					value={value}
					autoFocus
					onChange={event => setValue(event.target.value)}
					onPressEnter={event => {
						event.preventDefault();
					}}
				/>
			) : (
				<span>{data}</span>
			)}
			{record.editable ? (
				<Flex style={{ marginLeft: "auto" }}>
					{record.inEditMode ? (
						<Button
							type="text"
							size="small"
							onClick={() => {
								if (value === "") {
									return messageInstance.error(
										"Title cannot be empty"
									);
								}

								if (userCustomFields.has(value.toLowerCase())) {
									return messageInstance.error(
										"Custom field name already present."
									);
								}
								if (!TextFieldAllowedPattern.test(value)) {
									const invalidChars =
										value.match(TextInvalidCharRegex) || [];
									const uniqueInvalid = [
										...new Set(invalidChars),
									].join(", ");
									return messageInstance.error(
										`Title contains invalid character(s): ${uniqueInvalid}`
									);
								}

								if (
									record.type !== CustomFieldType.TEXT &&
									record.values &&
									record.values.length === 0
								) {
									return messageInstance.error(
										"Please Enter The Custom Field Values."
									);
								}
								customFieldsList.update(index, item => {
									if (item.key === "") {
										item.key =
											generateFieldKeyFromLabel(value);
									}
									item.label = value;
									item.inEditMode = false;
									return item;
								});
							}}
						>
							<SaveOutlined />
						</Button>
					) : (
						<></>
					)}
					<Button
						type="text"
						size="small"
						disabled={
							customFieldsList.items[index].label === "" ||
							customFieldsList.items[index].key === ""
						}
						style={{ marginLeft: "auto" }}
						onClick={() => {
							customFieldsList.update(index, item => {
								item.inEditMode = !item.inEditMode;
								return item;
							});
						}}
					>
						{record.inEditMode ? (
							<CloseOutlined />
						) : (
							<EditOutlined />
						)}
					</Button>
				</Flex>
			) : (
				<></>
			)}
		</Flex>
	);
}
