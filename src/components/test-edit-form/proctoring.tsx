import { Divider, Form } from "antd";
import SwitchCard from "../form/switch-card";
import { TabsRequestMap } from "../test-edit-layout";
import TestEditBaseForm from "./test-edit-form";
import { AppConfig } from "@/config";
import { PlatformOrganisation } from "@/constants";
import { useEffect } from "react";
import ResettableInputNumber from "../form/ResettableInputNumber";

type TestProctoringFields = TabsRequestMap["proctoring"];

const innerSwitchCardDividerStyle = { marginTop: "1em", marginBottom: "1em" };

export default function TestProctoringEditForm() {
	const { platformOrganisation } = AppConfig;
	const [form] = Form.useForm<TestProctoringFields>();
	// const context = useOutletContext<TestEditLayoutContextType>();
	// console.log(context.progressStep, TestSaveProgressStep.CONTENT)
	const tabSwitchLimit = Form.useWatch("tabSwitchAlertLimit", {
		form,
		preserve: true,
	});
	const isWebCamAllowed = Form.useWatch("isWebCamAllowed", {
		form,
		preserve: true,
	});
	const isAppOnly = Form.useWatch("isAppOnly", {
		form,
		preserve: true,
	});

	const isPreventTabSwitch = Form.useWatch("preventTabSwitch", {
		form,
		preserve: true,
	});
	const isLiveStreamEnabled = Form.useWatch("isLiveStreamEnabled", {
		form,
		preserve: true,
	});
	const isRecordingEnabled = Form.useWatch("isRecordingEnabled", {
		form,
		preserve: true,
	});
	const isVmDetectionEnabled = Form.useWatch("isVmDetectionEnabled", {
		form,
		preserve: true,
	});

	useEffect(() => {
		if (isPreventTabSwitch === false) {
			form.setFieldValue("tabSwitchAlertLimit", 3);
			form.setFieldValue("submitTestOnTabSwitchLimitBreach", false);
		}
	}, [form, isPreventTabSwitch]);

	useEffect(() => {
		if (isWebCamAllowed === false) {
			// form.setFieldValue("isRandomImageCaptureEnabled", false);
			// form.setFieldValue("isAIProctoringEnabled", false);
			form.setFieldValue("isLiveStreamEnabled", false);
		}
	}, [form, isWebCamAllowed]);

	useEffect(() => {
		if (isLiveStreamEnabled === false) {
			form.setFieldValue("onlySuspiciousRoom", false);
		}
	}, [form, isLiveStreamEnabled]);

	useEffect(() => {
		if (isAppOnly === false) {
			form.setFieldValue("allowClose", false);
			form.setFieldValue("isVmDetectionEnabled", false);
		}
	}, [form, isAppOnly]);

	useEffect(() => {
		if (isVmDetectionEnabled === false) {
			form.setFieldValue("isTerminateStudentEnable", false);
		}
	}, [form, isVmDetectionEnabled]);

	useEffect(() => {
		if (isRecordingEnabled === false) {
			form.setFieldValue("isRecordingUploadBlockingEnabled", false);
		}
	}, [form, isRecordingEnabled]);

	useEffect(() => {
		if (isAppOnly === false || isWebCamAllowed === false) {
			form.setFieldValue("isRecordingEnabled", false);
		}
	}, [form, isAppOnly, isWebCamAllowed]);

	return (
		<TestEditBaseForm
			tabKey="proctoring"
			form={form}
			submitButtonConfig={
				AppConfig.platformOrganisation === PlatformOrganisation.CHITKARA
					? { content: { idleState: "Save" } }
					: {}
			}
		>
			{AppConfig.platformOrganisation === PlatformOrganisation.CQ && (
				<SwitchCard
					form={form}
					name="isSignUpAllowed"
					labelTitle="Allow Signups"
					labelDescription="Candidates can create their login IDs and access the test"
				/>
			)}

			<SwitchCard
				form={form}
				name="preventTabSwitch"
				labelTitle="Prevent Tab Switch"
				labelDescription="Enable this option to restrict candidates from switching tabs during the test."
			>
				<Form.Item
					name="tabSwitchAlertLimit"
					label="Tab switch limit"
					layout="vertical"
					tooltip="Number of times a user can leave test window"
					rules={[
						{
							required: true,
							message: "Tab switch limit is required",
						},
						{
							validator: (_, value: number) =>
								value >= 0 && value <= 20
									? Promise.resolve()
									: Promise.reject(
											new Error(
												"The number of tab switches must be between 0 and 20"
											)
										),
						},
					]}
				>
					<ResettableInputNumber
						value={tabSwitchLimit}
						onChange={value =>
							form.setFieldValue("tabSwitchAlertLimit", value)
						}
					/>
				</Form.Item>
				<Divider style={innerSwitchCardDividerStyle} />
				<SwitchCard
					form={form}
					name="submitTestOnTabSwitchLimitBreach"
					labelTitle="Submit Test On Tab Switch Breach"
					labelDescription="Choose whether the test should be submitted when the tab switch limit is exceeded."
					ghost
					bordered={false}
				/>
			</SwitchCard>

			<SwitchCard
				form={form}
				name="copyPasteAllowed"
				labelTitle="Allow Copy Paste"
				labelDescription="Choose if candidates can copy/paste data."
			/>

			<SwitchCard
				form={form}
				name="isFullScreen"
				labelTitle="Fullscreen Mode"
				labelDescription="Please indicate whether candidates are required to activate fullscreen mode before starting the test."
			/>

			<>
				{platformOrganisation === PlatformOrganisation.CQ ? (
					<SwitchCard
						form={form}
						name="isWebCamAllowed"
						labelTitle="Webcam"
						labelDescription="Capture candidate images during test and candidate can’t access test without system camera access."
					>
						{/* <SwitchCard
						ghost
						bordered={false}
						form={form}
						name="isRandomImageCaptureEnabled"
						labelTitle="Capture Random Images"
						labelDescription="Only random images will be captured."
					/>
					<Divider style={innerSwitchCardDividerStyle} />
					<SwitchCard
						ghost
						bordered={false}
						form={form}
						name="isAIProctoringEnabled"
						labelTitle="AI Proctoring"
						labelDescription="Images will be captured based on AI detection."
					/>
					<Divider style={innerSwitchCardDividerStyle} /> */}
						{/* <SwitchCard
						form={form}
						name="isLiveStreamEnabled"
						labelTitle="Remote Proctoring"
						labelDescription="Enables or disables remote supervision of students during Test."
					>
						<SwitchCard
							ghost
							bordered={false}
							form={form}
							name="onlySuspiciousRoom"
							labelTitle="Only Suspicious Room"
							labelDescription="Student will join only suspicious room."
						/>
					</SwitchCard> */}
					</SwitchCard>
				) : (
					<SwitchCard
						form={form}
						name="isWebCamAllowed"
						labelTitle="Webcam"
						labelDescription="Capture candidate images during test and candidate can’t access test without system camera access."
					>
						<SwitchCard
							form={form}
							name="isLiveStreamEnabled"
							labelTitle="Remote Proctoring"
							labelDescription="Enables or disables remote supervision of students during Test."
						>
							<SwitchCard
								ghost
								bordered={false}
								form={form}
								name="onlySuspiciousRoom"
								labelTitle="Only Suspicious Room"
								labelDescription="Student will join only suspicious room."
							/>
						</SwitchCard>
					</SwitchCard>
				)}
				<SwitchCard
					form={form}
					name="isAppOnly"
					labelTitle="Secured Mode"
					labelDescription="This option restricts test in secure mode, maintaining a focused and controlled test environment."
				>
					<SwitchCard
						ghost
						bordered={false}
						form={form}
						name="allowClose"
						labelTitle="Allow Test Exit"
						labelDescription="This feature allows users close the app while they are on the test without submitting the test."
					/>
					{/* <Divider style={innerSwitchCardDividerStyle} />
					<SwitchCard
						ghost
						bordered={false}
						form={form}
						name="isStrictMode"
						labelTitle="Strict Mode"
						labelDescription="This option enables the strict mode."
					/> */}
					{/* <Divider style={innerSwitchCardDividerStyle} />
					<SwitchCard
						ghost
						bordered={false}
						form={form}
						name="isVPNAllowed"
						labelTitle="Allow VPN"
						labelDescription="This option allows student to use VPN."
					/> */}
					<Divider style={innerSwitchCardDividerStyle} />
					<SwitchCard
						form={form}
						name="isVmDetectionEnabled"
						labelTitle="Detect VM"
						labelDescription="This option detects if virtual machine is used and sends an email to the creator."
					>
						<SwitchCard
							ghost
							bordered={false}
							form={form}
							name="isTerminateStudentEnable"
							labelTitle="Terminate Test on Detection"
							labelDescription="This option terminates the student test if virtual machine is detected."
						/>
					</SwitchCard>
				</SwitchCard>
			</>

			<SwitchCard
				form={form}
				name="isRecordingEnabled"
				labelTitle="Recording"
				labelDescription={
					!(isWebCamAllowed && isAppOnly)
						? "Enable both Webcam and Secured Mode inorder to enable Recording."
						: "Record candidate videos during test."
				}
				disabled={!isWebCamAllowed || !isAppOnly}
			>
				<SwitchCard
					ghost
					bordered={false}
					form={form}
					name="isRecordingUploadBlockingEnabled"
					labelTitle="Strict Recording"
					labelDescription="Block user to exit until all recording are uploaded."
				/>
			</SwitchCard>
		</TestEditBaseForm>
	);
}
