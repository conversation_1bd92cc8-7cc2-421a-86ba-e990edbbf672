import { PlatformOrganisation } from "@/constants";

const hostNameTOServerName: {
	[key: string]:
		| {
				courseServerURL: string;
				loginServerURL: string;
				quizServerURL: string;
				platformOrganisation: PlatformOrganisation;
				socketServerURL: string;
				testAttemptUrlBase: string;
				masterVideoURL: string;
		  }
		| undefined;
} = {
	"app.cqtestga.com": {
		courseServerURL: "https://course.cqtestga.com",
		loginServerURL: "https://login.cqtestga.com",
		quizServerURL: "https://cqtestga.com",
		platformOrganisation: PlatformOrganisation.CQ,
		socketServerURL: "https://cqtestga.com",
		testAttemptUrlBase: "https://tests.cqtestga.com",
		masterVideoURL: "https://master-video.cqtestga.com/",
	},
	"app.codequotient.com": {
		courseServerURL: "https://course.codequotient.com",
		loginServerURL: "https://login.codequotient.com",
		quizServerURL: "https://codequotient.com",
		platformOrganisation: PlatformOrganisation.CQ,
		socketServerURL: "https://codequotient.com",
		testAttemptUrlBase: "https://tests.codequotient.com",
		masterVideoURL: "https://video-master.codequotient.com/",
	},
	"test.codequotient.com": {
		courseServerURL: "https://course.codequotient.com",
		loginServerURL: "https://login.codequotient.com",
		quizServerURL: "https://codequotient.com",
		platformOrganisation: PlatformOrganisation.CQ,
		socketServerURL: "https://codequotient.com",
		testAttemptUrlBase: "https://tests.codequotient.com",
		masterVideoURL: "https://video-master.codequotient.com/",
	},
	"app.test.chitkara.cqtestga.com": {
		courseServerURL: "https://course.chitkara.cqtestga.com/",
		loginServerURL: "https://login.chitkara.cqtestga.com/",
		quizServerURL: "https://test.chitkara.cqtestga.com",
		platformOrganisation: PlatformOrganisation.CHITKARA,
		socketServerURL: "https://chitkara.cqtestga.com",
		testAttemptUrlBase: "https://exam.chitkara.cqtestga.com",
		masterVideoURL: "https://master-video.chitkara.cqtestga.com/",
	},
	"infra.assess.testpad.chitkara.edu.in": {
		courseServerURL: "https://course.testpad.chitkara.edu.in",
		loginServerURL: "https://login.testpad.chitkara.edu.in",
		quizServerURL: "https://infra.assess.testpad.chitkara.edu.in",
		platformOrganisation: PlatformOrganisation.CHITKARA,
		socketServerURL: "https://infra.assess.testpad.chitkara.edu.in",
		testAttemptUrlBase: "https://exam.testpad.chitkara.edu.in",
		masterVideoURL: "https://master-video.chitkara.cqtestga.com/",
	},
	"assess.testpad.chitkara.edu.in": {
		courseServerURL: "https://course.testpad.chitkara.edu.in",
		loginServerURL: "https://login.testpad.chitkara.edu.in",
		quizServerURL: "https://infra.assess.testpad.chitkara.edu.in",
		platformOrganisation: PlatformOrganisation.CHITKARA,
		socketServerURL: "https://infra.assess.testpad.chitkara.edu.in",
		testAttemptUrlBase: "https://exam.testpad.chitkara.edu.in",
		masterVideoURL: "https://master-video.chitkara.cqtestga.com/",
	},
	"assess.testpad.chitkarauniversity.edu.in": {
		courseServerURL: "https://course.testpad.chitkarauniversity.edu.in",
		loginServerURL: "https://login.testpad.chitkarauniversity.edu.in",
		quizServerURL: "https://infra.assess.testpad.chitkarauniversity.edu.in",
		platformOrganisation: PlatformOrganisation.CHITKARA,
		socketServerURL:
			"https://infra.assess.testpad.chitkarauniversity.edu.in",
		testAttemptUrlBase: "https://exam.testpad.chitkarauniversity.edu.in",
		masterVideoURL: "https://master-video.chitkara.cqtestga.com/",
	},
};

let config = hostNameTOServerName[window.location.hostname];

if (!config) {
	config = {
		courseServerURL: import.meta.env.VITE_COURSE_SERVER_URL as string,
		loginServerURL: import.meta.env.VITE_LOGIN_SERVER_URL as string,
		quizServerURL: import.meta.env.VITE_MAIN_SERVER_URL as string,
		platformOrganisation: import.meta.env
			.VITE_PLATFORM_ORGANISATION as PlatformOrganisation,
		socketServerURL: import.meta.env.VITE_MAIN_SOCKET_SERVER_URL as string,
		testAttemptUrlBase: import.meta.env
			.VITE_TEST_ATTEMPT_URL_BASE as string,
		masterVideoURL: import.meta.env.VITE_MASTER_VIDEO_URL as string,
	};
}

export const AppConfig: APP_CONFIG & {
	platformOrganisation: PlatformOrganisation;
} = {
	courseServerURL: config.courseServerURL,
	loginServerURL: config.loginServerURL,
	quizServerURL: config.quizServerURL,
	platformOrganisation: config.platformOrganisation,
	socketServerURL: config.socketServerURL,
	testAttemptUrlBase: config.testAttemptUrlBase,
	testAttemptURL: config.testAttemptUrlBase + "/test",
	masterVideoURL: config.masterVideoURL,
};
