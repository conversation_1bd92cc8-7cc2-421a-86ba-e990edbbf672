# React Template Development Documentation

## Project Overview

This project is a React-based web application for managing online tests and quizzes. It provides functionality for creating, managing, and analyzing tests, questions, and user reports. The application is built with a modern React stack and follows a component-based architecture.

## Technology Stack

### Core Technologies
- **React 18**: Frontend library for building user interfaces
- **TypeScript**: Static typing for JavaScript
- **Vite**: Build tool and development server
- **React Router**: Client-side routing

### UI Libraries
- **Ant Design**: Main UI component library
- **Shadcn UI**: Additional UI components
- **TailwindCSS**: Utility-first CSS framework

### State Management
- **Zustand**: Lightweight state management library
- **Immer**: Immutable state updates

### Data Fetching
- **Axios**: HTTP client for API requests

### Additional Libraries
- **React Quill**: Rich text editor
- **React Ace**: Code editor component
- **Socket.io-client**: WebSocket client for real-time features
- **@ant-design/charts**: Data visualization
- **@dnd-kit**: Drag and drop functionality
- **dayjs**: Date manipulation
- **xlsx**: Excel file handling

## Project Structure

```
src/
├── @types/              # TypeScript type definitions
│   ├── client/          # Client-related types
│   ├── config/          # Configuration types
│   ├── general.d.ts     # General type definitions
│   ├── ques/            # Question-related types
│   ├── quiz/            # Quiz-related types
│   └── store/           # Store-related types
├── assets/              # Static assets
│   └── img/             # Images
├── client/              # API client implementations
│   ├── index.ts
│   ├── login-service-client.ts
│   ├── quiz-client.ts
│   ├── socket.ts
│   └── test-add.ts
├── components/          # Reusable UI components
│   ├── ui/              # Basic UI components
│   ├── add-question-modal/
│   ├── test-edit-form/
│   ├── question-layout/
│   └── ...
├── config/              # Application configuration
├── constants/           # Application constants
├── hooks/               # Custom React hooks
├── lib/                 # Utility libraries
├── questionList/        # Question list feature
├── screen/              # Screen components
│   └── quiz/
├── store/               # State management
│   ├── app-store/       # Application state
│   ├── ques/            # Question state
│   └── test/            # Test state
├── testList/            # Test list feature
├── testReport/          # Test report feature
├── themes/              # Theme configuration
├── utils/               # Utility functions
├── App.tsx              # Main application component
├── Router.tsx           # Application routing
└── main.tsx             # Application entry point
```

## Key Components

### Core Components

1. **App.tsx**: The main application component that initializes the app state and provides the router.

2. **Router.tsx**: Defines the application routes using React Router.

3. **MainScreen**: The main layout component that wraps all other screens.

### Feature Components

1. **Test Management**:
   - `TestList`: Displays a list of tests
   - `TestEditLayout`: Layout for test editing
   - `TestDetailsEditForm`: Form for editing test details
   - `TestContentEditForm`: Form for editing test content
   - `TestAccessEditForm`: Form for editing test access settings
   - `TestProctoringEditForm`: Form for editing test proctoring settings
   - `TestNotificationsEditForm`: Form for editing test notification settings

2. **Question Management**:
   - `QuestionList`: Displays a list of questions
   - `QuesLayout`: Layout for question editing
   - `QuestionPreview`: Preview of a question

3. **Reporting**:
   - `TestReport`: Displays test reports
   - `UserReport`: Displays user reports
   - `Stats`: Displays test statistics

4. **Proctoring**:
   - `Rooms`: Manages test rooms
   - `MeetingRoom`: Interface for meeting rooms

5. **Dashboard**:
   - `Dashboard`: Main dashboard component

## State Management

The application uses Zustand for state management, with three main stores:

1. **App Store** (`src/store/app-store/app-store.ts`):
   - Manages application-wide state
   - Handles user session and authentication
   - Provides access control functionality

2. **Test Store** (`src/store/test/store.ts`):
   - Manages test-related state
   - Provides methods for fetching and manipulating tests

3. **Question Store** (`src/store/ques/store.ts`):
   - Manages question-related state
   - Provides methods for fetching and manipulating questions

## API Integration

The application communicates with the backend through several client classes:

1. **QuizClient** (`src/client/quiz-client.ts`):
   - Base client class for API communication
   - Handles authentication and error handling

2. **TestAddClient** (`src/client/test-add.ts`):
   - Extends QuizClient
   - Provides methods for test management

3. **LoginServiceClient** (`src/client/login-service-client.ts`):
   - Handles user authentication

4. **SockClient** (`src/client/socket.ts`):
   - Manages WebSocket connections for real-time features

## Authentication Flow

1. The application checks for an existing session on initialization in the `appStoreInit` method.
2. If no session exists, the user is redirected to the login page.
3. User roles and permissions are loaded from the session and stored in the app store.
4. The `hasResourcePermission` method is used to check if a user has permission to access a resource.

## Routing Structure

The application uses React Router for client-side routing. The main routes are:

- `/`: Dashboard
- `/tests`: Test management
  - `/tests/add`: Add a new test
  - `/tests/:id/update`: Update an existing test
  - `/tests/:id/report`: View test report
  - `/tests/:id/content`: View test content
  - `/tests/:id/stats`: View test statistics
  - `/tests/:id/rooms`: View test rooms
  - `/tests/:id/user-report/:userId`: View user report
- `/questions`: Question management
  - `/questions/add/:quesId?/:tabId?`: Add or edit a question
  - `/questions/preview/:id`: Preview a question
- `/report`: Reports
- `/attemptPreview`: Preview attempts

## Data Models

### Test/Quiz

A test (or quiz) is the main entity in the application. It has the following properties:

- Basic information (title, description, time limit)
- Access settings (start time, end time, allowed IPs)
- Content (sections, questions)
- Proctoring settings (webcam, tab switching, full screen)
- Notification settings (email templates)

### Question

Questions can be of different types:

- MCQ (Multiple Choice Questions)
- Subjective
- Coding
- Multiple Answer
- Web

Each question has:
- Title
- Description
- Type
- Difficulty level
- Score
- Additional type-specific properties

### User

Users have different roles with different permissions:

- Admin
- Teacher/Instructor
- Student

## Development Guidelines

### Adding a New Feature

1. **Plan the feature**:
   - Define the requirements
   - Identify the components that need to be created or modified
   - Determine the state management needs

2. **Create/modify components**:
   - Create new components in the appropriate directory
   - Follow the existing component structure and naming conventions

3. **Update state management**:
   - Add new state and methods to the appropriate store
   - Use immer for immutable state updates

4. **Add API integration**:
   - Add new methods to the appropriate client class
   - Define the request and response types

5. **Update routing**:
   - Add new routes to the Router.tsx file if needed

6. **Test the feature**:
   - Test the feature in development
   - Ensure it works with different user roles

### Code Style

- Use TypeScript for all new code
- Follow the existing naming conventions
- Use functional components with hooks
- Use the Ant Design and Shadcn UI components for consistent UI
- Use TailwindCSS for custom styling

### State Management

- Use the appropriate store for state management
- Keep state normalized to avoid duplication
- Use selectors to derive state when possible
- Use immer for immutable state updates

### API Integration

- Use the existing client classes for API communication
- Define request and response types for type safety
- Handle errors appropriately
- Use async/await for asynchronous code

## Deployment

The application is built using Vite. To build for production:

```bash
yarn build
```

This will create a production build in the `dist` directory.

To preview the production build:

```bash
yarn preview
```

## Development Server

To start the development server:

```bash
yarn dev
```

This will start the development server on port 2001.
