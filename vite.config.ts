import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import federation from "@originjs/vite-plugin-federation";
import path from "path";

export default defineConfig({
	plugins: [
		react(),
		federation({
			name: "remoteApp",
			filename: "remoteEntry.js",
			exposes: {
				"./Button": "./src/components/button/button.tsx",
			},
			shared: [
				"react",
				"react-dom",
				"zustand",
				"immer",
				"react-router",
				"axios",
			],
		}),
	],
	build: {
		target: "esnext",
		minify: true,
		outDir: "dist",
	},
	resolve: {
		alias: {
			"@": path.resolve(__dirname, "./src"),
		},
	},
});
