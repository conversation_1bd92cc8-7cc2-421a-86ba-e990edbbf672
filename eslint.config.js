import js from '@eslint/js'
import globals from 'globals'
import reactHooks from 'eslint-plugin-react-hooks'
import reactRefresh from 'eslint-plugin-react-refresh'
import tseslint from 'typescript-eslint'
import jsxA11yPlugin from 'eslint-plugin-jsx-a11y';
import prettierPlugin from 'eslint-plugin-prettier';

export default tseslint.config(
	{ ignores: ["dist"] },
	{
		extends: [js.configs.recommended, ...tseslint.configs.recommended],
		files: ["**/*.{ts,tsx}"],
		languageOptions: {
			ecmaVersion: 2020,
			globals: globals.browser,
		},
		plugins: {
			"react-hooks": reactHooks,
			"react-refresh": reactRefresh,
			"jsx-a11y": jsxA11yPlugin,
			"prettier": prettierPlugin,
		},
		rules: {
			...reactHooks.configs.recommended.rules,
			"react-refresh/only-export-components": [
				"warn",
				{ allowConstantExport: true },
			],
			"@typescript-eslint/no-unused-vars": ["warn", { argsIgnorePattern: "^_" }],

			"jsx-a11y/anchor-is-valid": "error",
			"jsx-a11y/no-noninteractive-element-interactions": "error",

			// Prettier integration
			"prettier/prettier": [
				"error",
				{
					useTabs: true,
					tabWidth: 4,
					singleQuote: false,
					jsxSingleQuote: false,
					trailingComma: 'es5',
					endOfLine: 'auto',
					semi: true,
					printWidth: 80,
					bracketSpacing: true,
					arrowParens: 'avoid',
				},
			],

			// Disable ESLint rules that conflict with Prettier
			"indent": "off",
			"@typescript-eslint/indent": "off",
			"quotes": "off",
			"@typescript-eslint/quotes": "off",
			"semi": "off",
			"@typescript-eslint/semi": "off",
			"comma-dangle": "off",
			"@typescript-eslint/comma-dangle": "off",
			"max-len": "off",
			"object-curly-spacing": "off",
			"array-bracket-spacing": "off",
			"computed-property-spacing": "off",
			"space-before-function-paren": "off",
			"@typescript-eslint/space-before-function-paren": "off",
		},
	},
)
