# Code Specification

This document outlines the coding standards and conventions for this React TypeScript project.

## 📋 Table of Contents

- [General Guidelines](#general-guidelines)
- [TypeScript](#typescript)
- [React Components](#react-components)
- [File Structure](#file-structure)
- [Naming Conventions](#naming-conventions)
- [Code Formatting](#code-formatting)
- [ESLint Rules](#eslint-rules)
- [Import/Export](#importexport)
- [State Management](#state-management)
- [API Integration](#api-integration)

## 🎯 General Guidelines

- Write clean, readable, and maintainable code
- Follow the principle of least surprise
- Use TypeScript for all new code
- Prefer functional components with hooks over class components
- Keep components small and focused on a single responsibility
- Use meaningful variable and function names
- Write self-documenting code with minimal comments

## 🔷 TypeScript

### Type Definitions
- Define interfaces for all data structures
- Use type aliases for complex union types
- Place type definitions in `@types/` directory or co-located with components
- Use generic types where appropriate

```typescript
// Good
interface User {
    id: string;
    name: string;
    email: string;
}

// Avoid
const user: any = { ... };
```

### Type Safety
- Avoid `any` type - use `unknown` if necessary
- Use strict TypeScript configuration
- Leverage type inference where possible
- Use type guards for runtime type checking

## ⚛️ React Components

### Component Structure
- Use functional components with hooks
- Follow the component lifecycle pattern
- Keep JSX readable with proper indentation
- Use fragments instead of unnecessary div wrappers
- Follow consistent hook ordering within components

```typescript
// Good - Follow this hook order
const MyComponent: React.FC<Props> = ({ prop1, prop2 }) => {
    // 1. Props destructuring (already done in parameters)
    
    // 2. Store hooks
    const { user, setUser } = useAppStore();
    const { data, loading } = useDataStore();
    
    // 3. State hooks
    const [localState, setLocalState] = useState<StateType>(initialState);
    const [isVisible, setIsVisible] = useState(false);
    
    // 4. Memoized values (depend on props/state)
    const computedValue = useMemo(() => {
        return expensiveCalculation(prop1, localState);
    }, [prop1, localState]);
    
    // 5. Callback functions (may depend on memoized values)
    const handleClick = useCallback(() => {
        setLocalState(computedValue);
    }, [computedValue]);
    
    const handleSubmit = useCallback((data: FormData) => {
        // Handle submission
    }, []);
    
    // 6. Effects (in order of dependency complexity)
    useEffect(() => {
        // Effect with no dependencies
        setupGlobalListener();
    }, []);
    
    useEffect(() => {
        // Effect with dependencies
        if (prop1) {
            fetchData(prop1);
        }
    }, [prop1]);
    
    return (
        <>
            <div>{computedValue}</div>
            <button onClick={handleClick}>{prop2}</button>
        </>
    );
};
```

### Props and State
- Define prop interfaces explicitly
- Use default parameters for optional props
- Destructure props in function parameters
- Use appropriate hook types

## 📁 File Structure

### Directory Organization
```
src/
├── @types/              # Global type definitions
├── components/          # Reusable UI components
│   ├── ui/             # Basic UI components
│   └── [component]/    # Feature-specific components
├── hooks/              # Custom React hooks
├── store/              # Zustand state management
├── client/             # API client implementations
├── config/             # Configuration files
├── constants/          # Application constants
└── utils/              # Utility functions
```

### File Naming
- Use kebab-case for directories: `user-profile/`
- Use PascalCase for component files: `UserProfile.tsx`
- Use camelCase for utility files: `apiClient.ts`
- Use UPPER_CASE for constants: `API_ENDPOINTS.ts`

## 🏷️ Naming Conventions

### Variables and Functions
- Use camelCase: `userName`, `fetchUserData()`
- Use descriptive names: `isLoading` instead of `loading`
- Use verb-noun pattern for functions: `getUserById()`

### Components
- Use PascalCase: `UserProfile`, `DataTable`
- Use descriptive names that indicate purpose
- Avoid generic names like `Component` or `Item`

### Constants
- Use UPPER_SNAKE_CASE: `API_BASE_URL`, `MAX_RETRY_ATTEMPTS`
- Group related constants in enums or objects

### Types and Interfaces
- Use PascalCase: `UserData`, `ApiResponse`
- Prefix interfaces with `I` if needed for disambiguation
- Use descriptive names: `UserFormData` instead of `FormData`

## 🎨 Code Formatting

### Prettier Configuration
The project uses Prettier with the following settings:

```json
{
  "useTabs": true,
  "tabWidth": 4,
  "singleQuote": false,
  "jsxSingleQuote": false,
  "trailingComma": "es5",
  "endOfLine": "auto",
  "semi": true,
  "printWidth": 80,
  "bracketSpacing": true,
  "arrowParens": "avoid"
}
```

### Formatting Rules
- Use tabs for indentation (4 spaces width)
- Use double quotes for strings
- Always use semicolons
- Trailing commas for ES5 compatibility
- Line length limit of 80 characters
- Space inside object brackets: `{ key: value }`
- Avoid parentheses around single arrow function parameters

## 🔍 ESLint Rules

### Key Rules Enforced
- `no-unused-vars`: Warn for unused variables (ignore parameters starting with `_`)
- `react-hooks/rules-of-hooks`: Enforce hooks rules
- `react-refresh/only-export-components`: Warn for non-component exports
- `jsx-a11y/anchor-is-valid`: Ensure valid anchor elements
- `jsx-a11y/no-noninteractive-element-interactions`: Prevent interactions on non-interactive elements
- `prettier/prettier`: Enforce Prettier formatting rules

### Custom Configurations
- Allow unused parameters starting with underscore: `_param`
- Enable React Refresh for development
- Integrate accessibility checks with jsx-a11y plugin

## 📦 Import/Export

### Import Order
1. React and React-related imports
2. Third-party library imports
3. Internal imports (components, hooks, utils)
4. Relative imports
5. Type-only imports (use `import type`)

```typescript
import React, { useState, useEffect } from "react";
import { Button, Form } from "antd";
import { useAppStore } from "@/store";
import { ApiClient } from "../client";
import type { UserData } from "@/@types/user";
```

### Export Patterns
- Use named exports for utilities and hooks
- Use default exports for components
- Export types and interfaces explicitly

```typescript
// Component
export default UserProfile;

// Utilities
export { formatDate, validateEmail };

// Types
export type { UserData, ApiResponse };
```

## 🗄️ State Management

### Zustand Store
- Use Zustand for global state management
- Keep stores focused and modular
- Use Immer for immutable updates
- Define clear action methods

```typescript
interface AppStore {
    user: User | null;
    isLoading: boolean;
    setUser: (user: User) => void;
    setLoading: (loading: boolean) => void;
}

export const useAppStore = create<AppStore>()(
    immer((set) => ({
        user: null,
        isLoading: false,
        setUser: (user) => set((state) => {
            state.user = user;
        }),
        setLoading: (loading) => set((state) => {
            state.isLoading = loading;
        }),
    }))
);
```

### Local State
- Use `useState` for component-local state
- Use `useReducer` for complex state logic
- Keep state as close to where it's used as possible

## 🌐 API Integration

### Client Structure
- Use dedicated client classes for API communication
- Define request/response types
- Handle errors consistently
- Use async/await pattern

```typescript
class UserClient {
    async getUser(id: string): Promise<User> {
        try {
            const response = await axios.get(`/users/${id}`);
            return response.data;
        } catch (error) {
            throw new Error(`Failed to fetch user: ${error.message}`);
        }
    }
}
```

### Error Handling
- Use try-catch blocks for async operations
- Provide meaningful error messages
- Handle different error types appropriately
- Use loading states for better UX

## 🧪 Testing Guidelines

- Write unit tests for utility functions
- Test component behavior, not implementation
- Use meaningful test descriptions
- Mock external dependencies
- Aim for good test coverage on critical paths

## 📝 Documentation

### Code Comments
- Write comments for complex business logic
- Explain "why" not "what"
- Keep comments up-to-date with code changes
- Use JSDoc for function documentation

### README Updates
- Keep README.md current with project changes
- Document new features and breaking changes
- Include setup and deployment instructions
- Provide examples for common use cases

## 🔧 Development Workflow

1. Run `yarn format` before committing
2. Ensure `yarn lint` passes without errors
3. Test your changes thoroughly
4. Write meaningful commit messages
5. Create focused pull requests
6. Review code for adherence to these specifications

## 📋 Checklist

Before submitting code, ensure:

- [ ] Code follows TypeScript best practices
- [ ] Components are properly typed
- [ ] ESLint passes without errors
- [ ] Code is formatted with Prettier
- [ ] Imports are organized correctly
- [ ] Variable and function names are descriptive
- [ ] No unused variables or imports
- [ ] Error handling is implemented
- [ ] Code is tested and working
- [ ] Documentation is updated if needed
