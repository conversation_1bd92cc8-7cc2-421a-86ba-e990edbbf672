<!doctype html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<link rel="icon" type="image/svg+xml" href="/vite.svg" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title id="title">...</title>
	</head>

	<body>
		<div id="root"></div>
		<script type="module" src="/src/main.tsx"></script>

		<script>
			const hostname = window.location.hostname;
			const isChitkara =
				hostname === "app.test.chitkara.cqtestga.com" ||
				hostname === "assess.testpad.chitkara.edu.in" ||
				hostname === "assess.testpad.chitkarauniversity.edu.in" ||
				hostname === "exam.testpad.chitkara.edu.in" ||
				hostname === "infra.assess.testpad.chitkara.edu.in" ||
				hostname === "infra.assess.testpad.chitkarauniversity.edu.in";
			const link =
				document.querySelector("link[rel*='icon']") ||
				document.createElement("link");
			link.type = "image/x-icon";
			link.rel = "shortcut icon";
			link.href = isChitkara
				? "/images/chitkara_favicon.ico"
				: "/images/favicon.ico";
			document.getElementsByTagName("head")[0].appendChild(link);
			document.getElementById("title").innerHTML = isChitkara
				? "Chitkara"
				: "CodeQuotient";

			function handlePageReload() {
				const container = document.getElementById(
					"page_loader_container"
				);
				if (container) {
					container.style.display = "initial";
				}
				location.reload();
			}
		</script>
	</body>
</html>
