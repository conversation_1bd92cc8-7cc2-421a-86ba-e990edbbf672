{"name": "cq-react", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port 2001 --strictPort", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview --port 2001 --strictPort", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx,json,css,md}\"", "prepare": "husky"}, "dependencies": {"@ant-design/charts": "^2.2.6", "@ant-design/icons": "^6.0.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@originjs/vite-plugin-federation": "^1.3.8", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-slot": "^1.1.1", "@tanstack/react-table": "^8.20.6", "antd": "^5.23.2", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "events": "^3.3.0", "file-saver": "^2.0.5", "immer": "^10.1.1", "lucide-react": "^0.471.1", "react": "^18.3.1", "react-ace": "^14.0.1", "react-csv": "^2.2.2", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-quill": "^2.0.0", "react-router": "^7.1.1", "react-split": "^2.0.14", "react-sticky-box": "^2.0.5", "socket.io-client": "^4.8.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "typed-emitter": "^2.1.0", "xlsx-js-style": "^1.2.0", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/file-saver": "^2.0.7", "@types/lodash": "^4.17.15", "@types/node": "^22.10.7", "@types/react": "^18.3.18", "@types/react-csv": "^1.1.10", "@types/react-dom": "^18.3.5", "@types/xlsx": "^0.0.36", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.2.2", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "husky": "^9.0.11", "postcss": "^8.5.1", "prettier": "^3.4.2", "tailwindcss": "^3.4.17", "typescript": "~5.6.2", "typescript-eslint": "^8.18.2", "vite": "^6.0.11"}}